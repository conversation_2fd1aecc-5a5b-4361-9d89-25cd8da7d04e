#!/usr/bin/env python3
"""
启动稳定系统 - 一键启动稳定的信号捕获和监控系统
"""

import subprocess
import sys
import time
import threading
from pathlib import Path


def install_dependencies():
    """安装必要依赖"""
    required_packages = [
        'requests', 'beautifulsoup4', 'flask', 'feedparser'
    ]
    
    print("🔧 检查依赖包...")
    missing = []
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
            else:
                __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"📦 安装缺失依赖: {', '.join(missing)}")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + missing, 
                          check=True, capture_output=True)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    else:
        print("✅ 所有依赖已安装")
    
    return True


def clear_old_data():
    """清理旧数据"""
    print("🧹 清理旧数据...")
    
    old_files = [
        "logs/validated_signals.json",
        "MQL4/Files/test_account_info.txt",
        "MQL4/Files/validated_signals.csv"
    ]
    
    for file_path in old_files:
        path = Path(file_path)
        if path.exists():
            try:
                path.unlink()
                print(f"✅ 已删除: {file_path}")
            except Exception as e:
                print(f"⚠️  删除失败 {file_path}: {e}")


def start_signal_hunter():
    """启动信号猎手"""
    print("🎯 启动稳定信号猎手...")
    
    try:
        # 启动持续猎取模式
        process = subprocess.Popen([
            sys.executable, "stable_signal_hunter.py"
        ], stdin=subprocess.PIPE, text=True)
        
        # 自动选择持续猎取模式
        process.stdin.write("2\n")
        process.stdin.flush()
        
        return process
        
    except Exception as e:
        print(f"❌ 启动信号猎手失败: {e}")
        return None


def start_dashboard():
    """启动监控面板"""
    print("🌐 启动监控面板...")
    
    try:
        process = subprocess.Popen([
            sys.executable, "enhanced_dashboard.py"
        ])
        return process
        
    except Exception as e:
        print(f"❌ 启动监控面板失败: {e}")
        return None


def monitor_processes(hunter_process, dashboard_process):
    """监控进程状态"""
    print("\n📊 系统运行状态监控:")
    print("="*40)
    
    while True:
        try:
            # 检查信号猎手状态
            if hunter_process and hunter_process.poll() is None:
                hunter_status = "🟢 运行中"
            else:
                hunter_status = "🔴 已停止"
            
            # 检查监控面板状态
            if dashboard_process and dashboard_process.poll() is None:
                dashboard_status = "🟢 运行中"
            else:
                dashboard_status = "🔴 已停止"
            
            print(f"\r信号猎手: {hunter_status} | 监控面板: {dashboard_status} | 时间: {time.strftime('%H:%M:%S')}", end="")
            
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n\n🛑 用户中断，正在停止所有进程...")
            
            if hunter_process:
                hunter_process.terminate()
                print("✅ 信号猎手已停止")
            
            if dashboard_process:
                dashboard_process.terminate()
                print("✅ 监控面板已停止")
            
            break
        except Exception as e:
            print(f"\n❌ 监控异常: {e}")
            break


def main():
    """主函数"""
    print("🚀 稳定信号系统启动器")
    print("="*40)
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 清理旧数据
    clear_old_data()
    
    print("\n🎯 启动选项:")
    print("1. 只启动信号猎手")
    print("2. 只启动监控面板")
    print("3. 启动完整系统（推荐）")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    hunter_process = None
    dashboard_process = None
    
    if choice == '1':
        hunter_process = start_signal_hunter()
        if hunter_process:
            print("✅ 信号猎手已启动")
            try:
                hunter_process.wait()
            except KeyboardInterrupt:
                hunter_process.terminate()
                print("\n✅ 信号猎手已停止")
    
    elif choice == '2':
        dashboard_process = start_dashboard()
        if dashboard_process:
            print("✅ 监控面板已启动")
            print("🌐 访问地址: http://localhost:8080")
            try:
                dashboard_process.wait()
            except KeyboardInterrupt:
                dashboard_process.terminate()
                print("\n✅ 监控面板已停止")
    
    elif choice == '3':
        print("\n🚀 启动完整系统...")
        
        # 启动信号猎手
        hunter_process = start_signal_hunter()
        if not hunter_process:
            print("❌ 信号猎手启动失败")
            return
        
        # 等待几秒让信号猎手初始化
        time.sleep(3)
        
        # 启动监控面板
        dashboard_process = start_dashboard()
        if not dashboard_process:
            print("❌ 监控面板启动失败")
            if hunter_process:
                hunter_process.terminate()
            return
        
        print("✅ 完整系统启动成功！")
        print("\n📋 系统信息:")
        print("🎯 信号猎手: 每5分钟自动捕获真实信号")
        print("🌐 监控面板: http://localhost:8080")
        print("📊 信号文件: logs/stable_signals.json")
        print("📈 CSV文件: MQL4/Files/stable_signals.csv")
        
        print("\n按 Ctrl+C 停止系统")
        
        # 监控进程
        monitor_processes(hunter_process, dashboard_process)
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
