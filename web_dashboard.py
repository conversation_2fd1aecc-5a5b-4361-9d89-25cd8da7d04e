#!/usr/bin/env python3
"""
Web监控面板 - 实时显示跟单系统状态
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import Met<PERSON><PERSON><PERSON>r5 as mt5
from pathlib import Path

from src.utils.config_loader import ConfigLoader
from src.database.db_manager import DatabaseManager


class TradingDashboard:
    """交易监控面板"""
    
    def __init__(self, config_path: str = "config/config.json"):
        self.config = ConfigLoader.load(config_path)
        self.logger = logging.getLogger(__name__)
        
        # Flask应用
        self.app = Flask(__name__, template_folder='templates', static_folder='static')
        self.app.config['SECRET_KEY'] = 'your-secret-key-here'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # 数据库连接
        self.db_manager = None
        
        # 设置路由
        self.setup_routes()
        self.setup_socketio_events()
        
    def setup_routes(self):
        """设置Web路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return render_template('dashboard.html')
        
        @self.app.route('/api/account_info')
        def get_account_info():
            """获取账户信息"""
            try:
                if not mt5.initialize():
                    return jsonify({'error': 'MT5连接失败'})
                
                account_info = mt5.account_info()
                if account_info is None:
                    return jsonify({'error': '无法获取账户信息'})
                
                return jsonify({
                    'login': account_info.login,
                    'balance': account_info.balance,
                    'equity': account_info.equity,
                    'margin': account_info.margin,
                    'free_margin': account_info.margin_free,
                    'margin_level': account_info.margin_level,
                    'profit': account_info.profit
                })
                
            except Exception as e:
                return jsonify({'error': str(e)})
        
        @self.app.route('/api/positions')
        def get_positions():
            """获取持仓信息"""
            try:
                positions = mt5.positions_get()
                if positions is None:
                    return jsonify([])
                
                positions_data = []
                for pos in positions:
                    positions_data.append({
                        'ticket': pos.ticket,
                        'symbol': pos.symbol,
                        'type': 'BUY' if pos.type == 0 else 'SELL',
                        'volume': pos.volume,
                        'price_open': pos.price_open,
                        'price_current': pos.price_current,
                        'profit': pos.profit,
                        'swap': pos.swap,
                        'time': datetime.fromtimestamp(pos.time).isoformat()
                    })
                
                return jsonify(positions_data)
                
            except Exception as e:
                return jsonify({'error': str(e)})
        
        @self.app.route('/api/daily_stats')
        def get_daily_stats():
            """获取当日统计"""
            try:
                today = datetime.now().date()
                from_date = datetime.combine(today, datetime.min.time())
                to_date = datetime.combine(today, datetime.max.time())
                
                # 获取当日交易
                deals = mt5.history_deals_get(from_date, to_date)
                
                if deals is None:
                    deals = []
                
                total_trades = len(deals)
                total_profit = sum(deal.profit for deal in deals)
                winning_trades = len([deal for deal in deals if deal.profit > 0])
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
                
                return jsonify({
                    'total_trades': total_trades,
                    'total_profit': total_profit,
                    'winning_trades': winning_trades,
                    'losing_trades': total_trades - winning_trades,
                    'win_rate': round(win_rate, 2)
                })
                
            except Exception as e:
                return jsonify({'error': str(e)})
        
        @self.app.route('/api/signals_today')
        def get_signals_today():
            """获取今日信号统计"""
            try:
                # 这里应该从数据库获取信号数据
                # 暂时返回模拟数据
                return jsonify({
                    'total_signals': 15,
                    'executed_signals': 8,
                    'filtered_signals': 7,
                    'success_rate': 75.0,
                    'sources': {
                        'telegram': 10,
                        'myfxbook': 5
                    }
                })
                
            except Exception as e:
                return jsonify({'error': str(e)})
        
        @self.app.route('/api/risk_metrics')
        def get_risk_metrics():
            """获取风险指标"""
            try:
                account_info = mt5.account_info()
                if account_info is None:
                    return jsonify({'error': '无法获取账户信息'})
                
                # 计算风险指标
                balance = account_info.balance
                equity = account_info.equity
                margin_level = account_info.margin_level if account_info.margin_level else 0
                
                current_risk = ((balance - equity) / balance * 100) if balance > 0 else 0
                
                return jsonify({
                    'current_risk': round(current_risk, 2),
                    'margin_level': round(margin_level, 2),
                    'daily_risk_used': 2.5,  # 模拟数据
                    'max_daily_risk': 5.0,
                    'total_risk_used': 8.3,  # 模拟数据
                    'max_total_risk': 15.0
                })
                
            except Exception as e:
                return jsonify({'error': str(e)})
        
        @self.app.route('/api/recent_trades')
        def get_recent_trades():
            """获取最近交易"""
            try:
                # 获取最近24小时的交易
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=24)
                
                deals = mt5.history_deals_get(start_time, end_time)
                if deals is None:
                    return jsonify([])
                
                trades_data = []
                for deal in deals[-20:]:  # 最近20笔交易
                    trades_data.append({
                        'ticket': deal.ticket,
                        'symbol': deal.symbol,
                        'type': 'BUY' if deal.type == 0 else 'SELL',
                        'volume': deal.volume,
                        'price': deal.price,
                        'profit': deal.profit,
                        'time': datetime.fromtimestamp(deal.time).isoformat(),
                        'comment': deal.comment
                    })
                
                return jsonify(trades_data)
                
            except Exception as e:
                return jsonify({'error': str(e)})
    
    def setup_socketio_events(self):
        """设置WebSocket事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            self.logger.info("客户端已连接到监控面板")
            emit('status', {'message': '已连接到交易监控系统'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            self.logger.info("客户端已断开连接")
        
        @self.socketio.on('request_update')
        def handle_update_request():
            """客户端请求更新数据"""
            self.broadcast_updates()
    
    def broadcast_updates(self):
        """广播数据更新"""
        try:
            # 获取实时数据
            account_data = self.get_account_data()
            positions_data = self.get_positions_data()
            
            # 广播给所有连接的客户端
            self.socketio.emit('account_update', account_data)
            self.socketio.emit('positions_update', positions_data)
            
        except Exception as e:
            self.logger.error(f"广播更新错误: {e}")
    
    def get_account_data(self):
        """获取账户数据"""
        try:
            account_info = mt5.account_info()
            if account_info is None:
                return {}
            
            return {
                'balance': account_info.balance,
                'equity': account_info.equity,
                'profit': account_info.profit,
                'margin_level': account_info.margin_level,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取账户数据错误: {e}")
            return {}
    
    def get_positions_data(self):
        """获取持仓数据"""
        try:
            positions = mt5.positions_get()
            if positions is None:
                return []
            
            positions_data = []
            for pos in positions:
                positions_data.append({
                    'symbol': pos.symbol,
                    'type': 'BUY' if pos.type == 0 else 'SELL',
                    'volume': pos.volume,
                    'profit': pos.profit,
                    'price_current': pos.price_current
                })
            
            return positions_data
            
        except Exception as e:
            self.logger.error(f"获取持仓数据错误: {e}")
            return []
    
    async def start_background_tasks(self):
        """启动后台任务"""
        # 定期更新数据
        while True:
            try:
                self.broadcast_updates()
                await asyncio.sleep(5)  # 每5秒更新一次
            except Exception as e:
                self.logger.error(f"后台任务错误: {e}")
                await asyncio.sleep(10)
    
    def run(self, host='localhost', port=8080, debug=False):
        """运行Web服务器"""
        try:
            # 初始化MT5连接
            if not mt5.initialize():
                self.logger.error("MT5初始化失败")
                return
            
            self.logger.info(f"🌐 Web监控面板启动: http://{host}:{port}")
            
            # 启动后台任务
            import threading
            background_thread = threading.Thread(
                target=lambda: asyncio.run(self.start_background_tasks())
            )
            background_thread.daemon = True
            background_thread.start()
            
            # 启动Web服务器
            self.socketio.run(self.app, host=host, port=port, debug=debug)
            
        except Exception as e:
            self.logger.error(f"启动Web服务器错误: {e}")
        finally:
            mt5.shutdown()


def create_dashboard_template():
    """创建监控面板HTML模板"""
    template_dir = Path("templates")
    template_dir.mkdir(exist_ok=True)
    
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT4智能跟单系统 - 监控面板</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric { text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #2196F3; }
        .metric-label { color: #666; margin-top: 5px; }
        .status-good { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-danger { color: #F44336; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .profit-positive { color: #4CAF50; }
        .profit-negative { color: #F44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MT4智能跟单系统</h1>
            <p>实时监控面板</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>账户信息</h3>
                <div id="account-info">
                    <div class="metric">
                        <div class="metric-value" id="balance">--</div>
                        <div class="metric-label">账户余额</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>今日统计</h3>
                <div id="daily-stats">
                    <div class="metric">
                        <div class="metric-value" id="daily-profit">--</div>
                        <div class="metric-label">今日盈亏</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>风险指标</h3>
                <div id="risk-metrics">
                    <div class="metric">
                        <div class="metric-value" id="current-risk">--</div>
                        <div class="metric-label">当前风险</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>当前持仓</h3>
            <table id="positions-table">
                <thead>
                    <tr>
                        <th>品种</th>
                        <th>方向</th>
                        <th>手数</th>
                        <th>开仓价</th>
                        <th>当前价</th>
                        <th>盈亏</th>
                    </tr>
                </thead>
                <tbody id="positions-body">
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h3>最近交易</h3>
            <table id="trades-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>品种</th>
                        <th>方向</th>
                        <th>手数</th>
                        <th>价格</th>
                        <th>盈亏</th>
                    </tr>
                </thead>
                <tbody id="trades-body">
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        const socket = io();
        
        // 连接事件
        socket.on('connect', function() {
            console.log('已连接到服务器');
            loadInitialData();
        });
        
        // 加载初始数据
        function loadInitialData() {
            fetch('/api/account_info')
                .then(response => response.json())
                .then(data => updateAccountInfo(data));
                
            fetch('/api/positions')
                .then(response => response.json())
                .then(data => updatePositions(data));
                
            fetch('/api/daily_stats')
                .then(response => response.json())
                .then(data => updateDailyStats(data));
                
            fetch('/api/risk_metrics')
                .then(response => response.json())
                .then(data => updateRiskMetrics(data));
                
            fetch('/api/recent_trades')
                .then(response => response.json())
                .then(data => updateRecentTrades(data));
        }
        
        // 更新账户信息
        function updateAccountInfo(data) {
            if (data.error) {
                document.getElementById('balance').textContent = 'Error';
                return;
            }
            document.getElementById('balance').textContent = '$' + data.balance.toFixed(2);
        }
        
        // 更新持仓
        function updatePositions(positions) {
            const tbody = document.getElementById('positions-body');
            tbody.innerHTML = '';
            
            positions.forEach(pos => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${pos.symbol}</td>
                    <td>${pos.type}</td>
                    <td>${pos.volume}</td>
                    <td>${pos.price_open}</td>
                    <td>${pos.price_current}</td>
                    <td class="${pos.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                        $${pos.profit.toFixed(2)}
                    </td>
                `;
            });
        }
        
        // 更新每日统计
        function updateDailyStats(data) {
            if (data.error) return;
            document.getElementById('daily-profit').textContent = '$' + data.total_profit.toFixed(2);
            document.getElementById('daily-profit').className = 
                'metric-value ' + (data.total_profit >= 0 ? 'status-good' : 'status-danger');
        }
        
        // 更新风险指标
        function updateRiskMetrics(data) {
            if (data.error) return;
            document.getElementById('current-risk').textContent = data.current_risk.toFixed(1) + '%';
            document.getElementById('current-risk').className = 
                'metric-value ' + (data.current_risk < 5 ? 'status-good' : 
                data.current_risk < 10 ? 'status-warning' : 'status-danger');
        }
        
        // 更新最近交易
        function updateRecentTrades(trades) {
            const tbody = document.getElementById('trades-body');
            tbody.innerHTML = '';
            
            trades.slice(-10).forEach(trade => {
                const row = tbody.insertRow();
                const time = new Date(trade.time).toLocaleString();
                row.innerHTML = `
                    <td>${time}</td>
                    <td>${trade.symbol}</td>
                    <td>${trade.type}</td>
                    <td>${trade.volume}</td>
                    <td>${trade.price}</td>
                    <td class="${trade.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                        $${trade.profit.toFixed(2)}
                    </td>
                `;
            });
        }
        
        // 定期刷新数据
        setInterval(loadInitialData, 5000);
    </script>
</body>
</html>
    """
    
    with open(template_dir / "dashboard.html", "w", encoding="utf-8") as f:
        f.write(html_content)


if __name__ == "__main__":
    # 创建模板文件
    create_dashboard_template()
    
    # 启动监控面板
    dashboard = TradingDashboard()
    dashboard.run(host='localhost', port=8080, debug=True)
