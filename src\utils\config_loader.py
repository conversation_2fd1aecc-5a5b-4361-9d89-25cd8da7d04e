"""
配置加载器 - 负责加载和验证配置文件
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any


class ConfigLoader:
    """配置加载器"""
    
    @staticmethod
    def load(config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_file = Path(config_path)
            
            if not config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证配置
            ConfigLoader.validate_config(config)
            
            return config
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            raise
    
    @staticmethod
    def validate_config(config: Dict[str, Any]):
        """验证配置文件"""
        required_sections = [
            'api_keys',
            'signal_sources', 
            'risk_management',
            'trading_settings'
        ]
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"配置文件缺少必要部分: {section}")
        
        # 验证API密钥
        api_keys = config['api_keys']
        if 'metaapi' not in api_keys:
            raise ValueError("缺少MetaAPI配置")
        
        # 验证风险管理配置
        risk_config = config['risk_management']
        if 'account_settings' not in risk_config:
            raise ValueError("缺少账户风险设置")
    
    @staticmethod
    def save(config: Dict[str, Any], config_path: str):
        """保存配置文件"""
        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
            raise
