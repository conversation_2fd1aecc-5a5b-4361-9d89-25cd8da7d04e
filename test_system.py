#!/usr/bin/env python3
"""
MT4智能跟单系统 - 测试脚本
用于测试系统各个组件的功能
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path

# 设置基础日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_config_loading():
    """测试配置加载"""
    print("\n🔧 测试配置加载...")
    
    try:
        from src.utils.config_loader import ConfigLoader
        
        # 检查配置文件是否存在
        config_file = Path("config/config.json")
        if not config_file.exists():
            print("❌ 配置文件不存在，请先运行 python start.py 创建配置")
            return False
        
        # 加载配置
        config = ConfigLoader.load("config/config.json")
        print("✅ 配置加载成功")
        
        # 验证关键配置
        if config['api_keys']['metaapi']['token'] == 'YOUR_METAAPI_TOKEN_HERE':
            print("⚠️  MetaAPI Token未配置")
        else:
            print("✅ MetaAPI Token已配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


async def test_signal_parser():
    """测试信号解析器"""
    print("\n📊 测试信号解析器...")
    
    try:
        from src.utils.signal_parser import SignalParser
        
        parser = SignalParser()
        
        # 测试信号文本
        test_signals = [
            "EURUSD BUY @ 1.0850 SL: 1.0800 TP: 1.0950",
            "黄金 做多 入场价格 1950 止损 1940 止盈 1970",
            "GBPUSD SELL 1.2500 stop loss 1.2550 take profit 1.2400",
            "欧美 买入 @ 1.0900 SL 1.0850 TP 1.1000"
        ]
        
        for i, text in enumerate(test_signals, 1):
            print(f"\n测试信号 {i}: {text}")
            result = parser.parse(text)
            
            if result:
                print(f"✅ 解析成功:")
                print(f"   品种: {result['symbol']}")
                print(f"   方向: {result['action']}")
                print(f"   入场: {result.get('entry_price', 'N/A')}")
                print(f"   止损: {result.get('stop_loss', 'N/A')}")
                print(f"   止盈: {result.get('take_profit', 'N/A')}")
            else:
                print("❌ 解析失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号解析器测试失败: {e}")
        return False


async def test_database():
    """测试数据库"""
    print("\n💾 测试数据库...")
    
    try:
        from src.database.db_manager import DatabaseManager
        
        # 创建测试数据库配置
        db_config = {
            'type': 'sqlite',
            'path': 'data/test.db'
        }
        
        db_manager = DatabaseManager(db_config)
        await db_manager.initialize()
        
        # 测试保存信号
        test_signal = {
            'source': 'test',
            'symbol': 'EURUSD',
            'action': 'BUY',
            'entry_price': 1.0850,
            'stop_loss': 1.0800,
            'take_profit': 1.0950,
            'confidence': 0.8,
            'raw_text': 'Test signal',
            'timestamp': datetime.now()
        }
        
        await db_manager.save_signal(test_signal)
        print("✅ 信号保存成功")
        
        # 测试获取统计
        stats = await db_manager.get_statistics()
        print(f"✅ 统计信息获取成功: {stats}")
        
        await db_manager.close()
        
        # 清理测试数据库
        test_db_path = Path("data/test.db")
        if test_db_path.exists():
            test_db_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


async def test_risk_manager():
    """测试风险管理器"""
    print("\n⚠️  测试风险管理器...")
    
    try:
        from src.core.risk_manager import RiskManager
        from src.core.signal_manager import TradingSignal
        
        # 创建测试配置
        risk_config = {
            'account_settings': {
                'max_risk_per_trade': 0.02,
                'max_daily_risk': 0.05,
                'max_total_risk': 0.15,
                'emergency_stop_loss': 0.20
            },
            'trader_limits': {
                'max_allocation_per_trader': 0.30,
                'min_trader_history_days': 90,
                'min_trader_trades': 50,
                'max_trader_drawdown': 25.0
            },
            'position_sizing': {
                'base_lot_size': 0.01,
                'max_lot_size': 1.0,
                'risk_per_trade_percent': 2.0,
                'use_dynamic_sizing': True
            }
        }
        
        risk_manager = RiskManager(risk_config)
        
        # 创建测试信号
        test_signal = TradingSignal(
            source='test',
            symbol='EURUSD',
            action='BUY',
            entry_price=1.0850,
            stop_loss=1.0800,
            take_profit=1.0950,
            confidence=0.8,
            timestamp=datetime.now(),
            raw_text='Test signal'
        )
        
        # 测试风险回报比计算
        rr_ratio = risk_manager.calculate_risk_reward_ratio(test_signal)
        print(f"✅ 风险回报比计算: {rr_ratio:.2f}")
        
        # 测试仓位大小计算
        position_size = await risk_manager.calculate_position_size(test_signal, 10000)
        print(f"✅ 仓位大小计算: {position_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险管理器测试失败: {e}")
        return False


async def test_mt5_connection():
    """测试MT5连接"""
    print("\n🔌 测试MT5连接...")
    
    try:
        import MetaTrader5 as mt5
        
        # 尝试初始化MT5
        if not mt5.initialize():
            print("❌ MT5初始化失败，请确保MT5正在运行")
            return False
        
        # 获取账户信息
        account_info = mt5.account_info()
        if account_info is None:
            print("❌ 无法获取账户信息，请检查MT5登录状态")
            mt5.shutdown()
            return False
        
        print("✅ MT5连接成功")
        print(f"   账户: {account_info.login}")
        print(f"   余额: ${account_info.balance:.2f}")
        print(f"   净值: ${account_info.equity:.2f}")
        
        # 获取品种信息
        symbols = mt5.symbols_get()
        if symbols:
            print(f"   可用品种数量: {len(symbols)}")
        
        mt5.shutdown()
        return True
        
    except ImportError:
        print("❌ MetaTrader5包未安装，请运行: pip install MetaTrader5")
        return False
    except Exception as e:
        print(f"❌ MT5连接测试失败: {e}")
        return False


async def test_web_dashboard():
    """测试Web监控面板"""
    print("\n🌐 测试Web监控面板...")
    
    try:
        # 检查模板文件是否存在
        template_file = Path("templates/dashboard.html")
        if not template_file.exists():
            print("⚠️  Web模板文件不存在，正在创建...")
            from web_dashboard import create_dashboard_template
            create_dashboard_template()
            print("✅ Web模板文件创建成功")
        
        print("✅ Web监控面板组件检查完成")
        print("   启动命令: python web_dashboard.py")
        print("   访问地址: http://localhost:8080")
        
        return True
        
    except Exception as e:
        print(f"❌ Web监控面板测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🧪 开始系统测试...")
    print("=" * 50)
    
    tests = [
        ("配置加载", test_config_loading),
        ("信号解析器", test_signal_parser),
        ("数据库", test_database),
        ("风险管理器", test_risk_manager),
        ("MT5连接", test_mt5_connection),
        ("Web监控面板", test_web_dashboard)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 显示测试结果汇总
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统准备就绪。")
        print("\n🚀 启动建议:")
        print("1. 编辑配置文件: config/config.json")
        print("2. 运行主程序: python start.py")
        print("3. 选择启动选项")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关组件。")
        print("\n🔧 故障排除:")
        print("1. 检查Python依赖: pip install -r requirements.txt")
        print("2. 检查MT5安装和运行状态")
        print("3. 检查配置文件设置")


async def create_sample_config():
    """创建示例配置文件"""
    print("\n📝 创建示例配置文件...")
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    sample_config = {
        "api_keys": {
            "metaapi": {
                "token": "YOUR_METAAPI_TOKEN_HERE",
                "account_id": "YOUR_MT4_ACCOUNT_ID_HERE"
            },
            "telegram": {
                "bot_token": "YOUR_TELEGRAM_BOT_TOKEN_HERE",
                "api_id": "YOUR_TELEGRAM_API_ID",
                "api_hash": "YOUR_TELEGRAM_API_HASH"
            }
        },
        "signal_sources": {
            "telegram_channels": [
                {
                    "name": "TestChannel",
                    "channel_id": "@test_channel",
                    "enabled": False,
                    "weight": 0.5
                }
            ],
            "myfxbook_traders": []
        },
        "risk_management": {
            "account_settings": {
                "max_risk_per_trade": 0.02,
                "max_daily_risk": 0.05,
                "max_total_risk": 0.15,
                "emergency_stop_loss": 0.20
            }
        },
        "trading_settings": {
            "allowed_symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"]
        }
    }
    
    config_file = config_dir / "config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 示例配置文件已创建: {config_file}")


if __name__ == "__main__":
    print("🚀 MT4智能跟单系统 - 测试工具")
    print("=" * 50)
    
    # 创建必要目录
    for directory in ["config", "logs", "data", "templates"]:
        Path(directory).mkdir(exist_ok=True)
    
    # 如果配置文件不存在，创建示例配置
    if not Path("config/config.json").exists():
        asyncio.run(create_sample_config())
    
    # 运行测试
    asyncio.run(run_all_tests())
