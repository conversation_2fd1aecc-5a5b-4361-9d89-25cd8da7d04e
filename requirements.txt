# MT4智能跟单系统依赖包

# 核心依赖
asyncio-mqtt==0.16.1
aiofiles==23.2.1
aiohttp==3.9.1
asyncio==3.4.3

# MT4/MT5连接
MetaTrader5==5.0.45
metaapi-cloud-sdk==27.0.2

# Telegram集成
telethon==1.34.0
python-telegram-bot==20.7

# Myfxbook API
requests==2.31.0
beautifulsoup4==4.12.2
selenium==4.16.0

# 数据处理
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# 数据库
sqlite3
sqlalchemy==2.0.23
alembic==1.13.1

# 机器学习（可选）
scikit-learn==1.3.2
tensorflow==2.15.0
torch==2.1.2

# 技术分析
TA-Lib==0.4.28
talib-binary==0.4.19

# Web界面
flask==3.0.0
flask-socketio==5.3.6
dash==2.16.1
plotly==5.17.0

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1
configparser==6.0.0

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 通知服务
smtplib
email-validator==2.1.0

# 数据验证
pydantic==2.5.2
marshmallow==3.20.2

# 时间处理
pytz==2023.3
python-dateutil==2.8.2

# 加密和安全
cryptography==41.0.8
bcrypt==4.1.2

# 网络请求
httpx==0.25.2
websockets==12.0

# 数据可视化
matplotlib==3.8.2
seaborn==0.13.0

# 工具库
click==8.1.7
tqdm==4.66.1
colorama==0.4.6

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0

# 代码质量
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# 性能优化
uvloop==0.19.0
cython==3.0.6

# 文档生成
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
