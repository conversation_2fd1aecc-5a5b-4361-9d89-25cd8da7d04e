#!/usr/bin/env python3
"""
稳定信号猎手 - 使用可靠的HTTP请求方式获取真实信号
避免浏览器驱动的兼容性问题
"""

import asyncio
import json
import logging
import random
import time
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional

import requests
from bs4 import BeautifulSoup
import feedparser


class StableSignalHunter:
    """稳定信号猎手 - 使用HTTP请求获取信号"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.session = requests.Session()
        
        # 设置随机用户代理
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        # 更新请求头
        self.update_headers()
        
        # 信号源配置
        self.signal_sources = [
            {
                'name': 'DailyFX_RSS',
                'url': 'https://www.dailyfx.com/feeds/market-news',
                'type': 'rss_feed',
                'quality': 'high',
                'enabled': True
            },
            {
                'name': 'ForexLive_News',
                'url': 'https://www.forexlive.com/',
                'type': 'web_scraping',
                'quality': 'high',
                'enabled': True
            },
            {
                'name': 'FXStreet_Analysis',
                'url': 'https://www.fxstreet.com/news',
                'type': 'web_scraping',
                'quality': 'high',
                'enabled': True
            },
            {
                'name': 'Investing_News',
                'url': 'https://www.investing.com/news/forex-news',
                'type': 'web_scraping',
                'quality': 'medium',
                'enabled': True
            }
        ]
        
        self.captured_signals = []
        self.processed_ids = set()
        
        self.stats = {
            'total_attempts': 0,
            'successful_captures': 0,
            'failed_captures': 0,
            'unique_signals': 0,
            'last_capture_time': None
        }
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('StableSignalHunter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            # 文件日志
            file_handler = logging.FileHandler('logs/stable_hunter.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            # 控制台日志
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def update_headers(self):
        """更新请求头"""
        self.session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        })
    
    async def hunt_dailyfx_rss(self, source):
        """猎取DailyFX RSS信号"""
        signals = []
        
        try:
            self.logger.info(f"🎯 猎取 {source['name']} 信号...")
            
            # 获取RSS feed
            feed = feedparser.parse(source['url'])
            
            if not feed.entries:
                self.logger.warning(f"{source['name']} RSS feed为空")
                return signals
            
            for entry in feed.entries[:10]:  # 最近10条
                try:
                    title = entry.title
                    link = entry.link
                    published = entry.get('published', '')
                    summary = entry.get('summary', '')
                    
                    # 解析信号
                    signal = self.parse_news_signal(title, summary, link, source['name'])
                    if signal:
                        signals.append(signal)
                        
                except Exception as e:
                    continue
            
            self.logger.info(f"✅ {source['name']} 猎取完成: {len(signals)} 个信号")
            
        except Exception as e:
            self.logger.error(f"❌ {source['name']} 猎取失败: {e}")
        
        return signals
    
    async def hunt_forexlive_news(self, source):
        """猎取ForexLive新闻信号"""
        signals = []
        
        try:
            self.logger.info(f"🎯 猎取 {source['name']} 信号...")
            
            # 随机延迟
            await asyncio.sleep(random.uniform(1, 3))
            
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找新闻文章
            articles = soup.find_all('article', limit=15)
            
            for article in articles:
                try:
                    # 获取标题
                    title_elem = article.find('h2') or article.find('h3') or article.find('h1')
                    if not title_elem:
                        continue
                    
                    title = title_elem.get_text().strip()
                    
                    # 获取链接
                    link_elem = title_elem.find('a')
                    link = link_elem.get('href') if link_elem else ''
                    if link and not link.startswith('http'):
                        link = 'https://www.forexlive.com' + link
                    
                    # 获取摘要
                    summary_elem = article.find('p')
                    summary = summary_elem.get_text().strip() if summary_elem else ''
                    
                    # 解析信号
                    signal = self.parse_news_signal(title, summary, link, source['name'])
                    if signal:
                        signals.append(signal)
                        
                except Exception as e:
                    continue
            
            self.logger.info(f"✅ {source['name']} 猎取完成: {len(signals)} 个信号")
            
        except Exception as e:
            self.logger.error(f"❌ {source['name']} 猎取失败: {e}")
        
        return signals
    
    async def hunt_fxstreet_analysis(self, source):
        """猎取FXStreet分析信号"""
        signals = []
        
        try:
            self.logger.info(f"🎯 猎取 {source['name']} 信号...")
            
            await asyncio.sleep(random.uniform(2, 4))
            
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找分析文章
            articles = soup.find_all('div', class_='fxs_article_preview', limit=12)
            
            for article in articles:
                try:
                    # 获取标题
                    title_elem = article.find('h3')
                    if not title_elem:
                        continue
                    
                    title_link = title_elem.find('a')
                    if not title_link:
                        continue
                    
                    title = title_link.get_text().strip()
                    link = title_link.get('href', '')
                    
                    # 获取作者
                    author_elem = article.find('span', class_='fxs_article_author')
                    author = author_elem.get_text().strip() if author_elem else 'FXStreet'
                    
                    # 获取摘要
                    summary_elem = article.find('div', class_='fxs_article_summary')
                    summary = summary_elem.get_text().strip() if summary_elem else ''
                    
                    # 解析信号
                    signal = self.parse_analysis_signal(title, summary, link, author, source['name'])
                    if signal:
                        signals.append(signal)
                        
                except Exception as e:
                    continue
            
            self.logger.info(f"✅ {source['name']} 猎取完成: {len(signals)} 个信号")
            
        except Exception as e:
            self.logger.error(f"❌ {source['name']} 猎取失败: {e}")
        
        return signals
    
    async def hunt_investing_news(self, source):
        """猎取Investing.com新闻信号"""
        signals = []
        
        try:
            self.logger.info(f"🎯 猎取 {source['name']} 信号...")
            
            await asyncio.sleep(random.uniform(1, 3))
            
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找新闻文章
            articles = soup.find_all('div', class_='textDiv', limit=12)
            
            for article in articles:
                try:
                    # 获取标题
                    title_elem = article.find('a')
                    if not title_elem:
                        continue
                    
                    title = title_elem.get_text().strip()
                    link = title_elem.get('href', '')
                    
                    # 获取时间
                    time_elem = article.find('span', class_='date')
                    pub_time = time_elem.get_text().strip() if time_elem else ''
                    
                    # 解析信号
                    signal = self.parse_news_signal(title, '', link, source['name'], pub_time)
                    if signal:
                        signals.append(signal)
                        
                except Exception as e:
                    continue
            
            self.logger.info(f"✅ {source['name']} 猎取完成: {len(signals)} 个信号")
            
        except Exception as e:
            self.logger.error(f"❌ {source['name']} 猎取失败: {e}")
        
        return signals
    
    def parse_news_signal(self, title, summary, link, source, pub_time=''):
        """解析新闻信号"""
        try:
            # 检查是否包含交易相关内容
            trading_keywords = [
                'forecast', 'outlook', 'analysis', 'target', 'support', 'resistance',
                'bullish', 'bearish', 'buy', 'sell', 'long', 'short', 'rally', 'decline'
            ]
            
            content = (title + ' ' + summary).lower()
            if not any(keyword in content for keyword in trading_keywords):
                return None
            
            # 提取货币对
            symbol_patterns = [
                (r'\b(EUR/USD|EURUSD|Euro)\b', 'EURUSD'),
                (r'\b(GBP/USD|GBPUSD|Pound|Sterling)\b', 'GBPUSD'),
                (r'\b(USD/JPY|USDJPY|Yen)\b', 'USDJPY'),
                (r'\b(XAU/USD|XAUUSD|Gold)\b', 'XAUUSD'),
                (r'\b(AUD/USD|AUDUSD|Aussie)\b', 'AUDUSD'),
                (r'\b(USD/CAD|USDCAD|Loonie)\b', 'USDCAD'),
                (r'\b(USD/CHF|USDCHF|Swissy)\b', 'USDCHF')
            ]
            
            symbol = None
            for pattern, sym in symbol_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    symbol = sym
                    break
            
            if not symbol:
                return None
            
            # 判断方向
            bullish_words = ['bullish', 'buy', 'long', 'rally', 'rise', 'up', 'support', 'bounce']
            bearish_words = ['bearish', 'sell', 'short', 'decline', 'fall', 'down', 'resistance', 'drop']
            
            action = None
            if any(word in content for word in bullish_words):
                action = 'BUY'
            elif any(word in content for word in bearish_words):
                action = 'SELL'
            
            if not action:
                return None
            
            # 生成唯一ID
            signal_id = f"{source}_{symbol}_{action}_{hash(title)}"
            if signal_id in self.processed_ids:
                return None
            
            self.processed_ids.add(signal_id)
            
            # 计算置信度
            confidence = 0.6  # 基础置信度
            
            # 根据来源调整置信度
            if 'DailyFX' in source:
                confidence += 0.15
            elif 'FXStreet' in source:
                confidence += 0.1
            elif 'ForexLive' in source:
                confidence += 0.1
            
            # 根据关键词调整置信度
            if any(word in content for word in ['forecast', 'target', 'analysis']):
                confidence += 0.05
            
            confidence = min(0.9, confidence)  # 最大90%
            
            return {
                'id': signal_id,
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'confidence': confidence,
                'signal_type': 'news_analysis',
                'title': title[:150],  # 限制长度
                'summary': summary[:200] if summary else '',
                'url': link,
                'publish_time': pub_time,
                'is_real': True,
                'cost_type': 'free',
                'quality': 'medium_high',
                'capture_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"解析新闻信号失败: {e}")
            return None
    
    def parse_analysis_signal(self, title, summary, link, author, source):
        """解析分析信号"""
        try:
            # 专业分析的置信度更高
            signal = self.parse_news_signal(title, summary, link, source)
            if signal:
                signal['signal_type'] = 'professional_analysis'
                signal['author'] = author
                signal['confidence'] = min(0.85, signal['confidence'] + 0.1)  # 提高置信度
                signal['quality'] = 'high'
            
            return signal
            
        except Exception as e:
            return None
    
    async def hunt_all_sources(self):
        """猎取所有信号源"""
        all_signals = []
        
        self.logger.info("🚀 开始全面信号猎取...")
        
        for source in self.signal_sources:
            if not source.get('enabled', True):
                continue
            
            self.stats['total_attempts'] += 1
            
            try:
                # 更新请求头
                self.update_headers()
                
                # 根据类型选择猎取方法
                if source['type'] == 'rss_feed':
                    signals = await self.hunt_dailyfx_rss(source)
                elif source['name'] == 'ForexLive_News':
                    signals = await self.hunt_forexlive_news(source)
                elif source['name'] == 'FXStreet_Analysis':
                    signals = await self.hunt_fxstreet_analysis(source)
                elif source['name'] == 'Investing_News':
                    signals = await self.hunt_investing_news(source)
                else:
                    continue
                
                if signals:
                    all_signals.extend(signals)
                    self.stats['successful_captures'] += 1
                else:
                    self.stats['failed_captures'] += 1
                
                # 随机延迟避免被封
                await asyncio.sleep(random.uniform(3, 6))
                
            except Exception as e:
                self.logger.error(f"猎取源 {source['name']} 异常: {e}")
                self.stats['failed_captures'] += 1
                continue
        
        # 更新统计
        self.stats['unique_signals'] = len(all_signals)
        self.stats['last_capture_time'] = datetime.now().isoformat()
        
        self.logger.info(f"🎯 猎取完成: {len(all_signals)} 个唯一信号")
        
        return all_signals
    
    def save_signals(self, signals):
        """保存信号"""
        if not signals:
            return
        
        try:
            Path("logs").mkdir(exist_ok=True)
            Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
            
            # 保存详细JSON
            with open('logs/stable_signals.json', 'a', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False) + '\n')
            
            # 保存CSV格式
            import csv
            with open('MQL4/Files/stable_signals.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for signal in signals:
                    writer.writerow([
                        signal['timestamp'],
                        signal['symbol'],
                        signal['action'],
                        signal.get('entry_price', 0),
                        signal.get('stop_loss', 0),
                        signal.get('take_profit', 0),
                        0.01,
                        'STABLE_SIGNAL'
                    ])
            
            # 保存统计
            with open('logs/stable_stats.json', 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 已保存 {len(signals)} 个稳定信号")
            
        except Exception as e:
            self.logger.error(f"保存信号失败: {e}")
    
    async def continuous_hunt(self):
        """持续猎取模式"""
        self.logger.info("🔥 启动稳定持续猎取模式...")
        
        cycle_count = 0
        
        while True:
            try:
                cycle_count += 1
                self.logger.info(f"开始第 {cycle_count} 轮猎取...")
                
                # 猎取信号
                signals = await self.hunt_all_sources()
                
                if signals:
                    self.save_signals(signals)
                    
                    print(f"\n=== 第 {cycle_count} 轮猎取报告 ===")
                    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"本轮获得: {len(signals)} 个真实信号")
                    print(f"成功率: {self.stats['successful_captures']}/{self.stats['total_attempts']} = {(self.stats['successful_captures']/max(1,self.stats['total_attempts'])*100):.1f}%")
                    
                    # 显示信号详情
                    print(f"\n🎯 最新真实信号:")
                    for i, signal in enumerate(signals[-3:], 1):
                        print(f"{i}. {signal['symbol']} {signal['action']} - 置信度: {signal['confidence']*100:.0f}%")
                        print(f"   来源: {signal['source']}")
                        print(f"   标题: {signal['title'][:60]}...")
                    
                    # 按质量分类统计
                    quality_stats = {}
                    for signal in signals:
                        quality = signal.get('quality', 'unknown')
                        quality_stats[quality] = quality_stats.get(quality, 0) + 1
                    
                    print(f"\n📊 信号质量分布: {quality_stats}")
                
                else:
                    print(f"第 {cycle_count} 轮未获得新信号 - {datetime.now().strftime('%H:%M:%S')}")
                
                # 等待5分钟
                print(f"等待5分钟后进行下一轮猎取...")
                await asyncio.sleep(300)
                
            except KeyboardInterrupt:
                print(f"\n用户中断，共完成 {cycle_count} 轮猎取")
                break
            except Exception as e:
                self.logger.error(f"第 {cycle_count} 轮猎取异常: {e}")
                await asyncio.sleep(60)  # 异常后等待1分钟


async def main():
    """主函数"""
    print("🎯 稳定信号猎手 - 可靠的HTTP信号捕获")
    print("="*50)
    print("📊 信号源:")
    print("🔸 DailyFX RSS - 机构级分析")
    print("🔸 ForexLive - 实时市场新闻")
    print("🔸 FXStreet - 专业分析师观点")
    print("🔸 Investing.com - 市场资讯")
    print("="*50)
    print("✅ 优势: 稳定可靠，无浏览器依赖")
    print("✅ 信号: 100%真实，来自权威金融网站")
    print("✅ 质量: 专业分析，置信度评估")
    print("="*50)
    
    hunter = StableSignalHunter()
    
    choice = input("\n选择模式:\n1. 单次猎取\n2. 持续猎取\n请选择 (1-2): ").strip()
    
    if choice == '1':
        print("\n🎯 开始单次信号猎取...")
        signals = await hunter.hunt_all_sources()
        if signals:
            hunter.save_signals(signals)
            print(f"\n✅ 猎取完成！获得 {len(signals)} 个真实信号")
            
            # 显示信号摘要
            for i, signal in enumerate(signals[:5], 1):
                print(f"{i}. {signal['symbol']} {signal['action']} - {signal['source']}")
                print(f"   置信度: {signal['confidence']*100:.0f}% | {signal['title'][:50]}...")
        else:
            print("\n⚠️  本次未获得信号，请稍后重试")
    
    elif choice == '2':
        print("\n🔥 开始持续信号猎取...")
        print("按 Ctrl+C 停止猎取")
        await hunter.continuous_hunt()
    
    else:
        print("无效选择")


if __name__ == "__main__":
    asyncio.run(main())
