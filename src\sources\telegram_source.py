"""
Telegram信号源 - 从Telegram频道获取交易信号
"""

import asyncio
import logging
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from telethon import TelegramClient, events
from telethon.tl.types import Message


class TelegramSignalSource:
    """Telegram信号源"""
    
    def __init__(self, channels_config: List[Dict]):
        self.channels_config = channels_config
        self.logger = logging.getLogger(__name__)
        self.client = None
        self.is_running = False
        self.signal_queue = asyncio.Queue()
        self.last_message_ids = {}
        
        # 信号关键词模式
        self.signal_patterns = {
            'buy_signals': [
                r'(?i)\b(buy|long|bull|上涨|买入|做多)\b',
                r'(?i)\b(call|看涨)\b'
            ],
            'sell_signals': [
                r'(?i)\b(sell|short|bear|下跌|卖出|做空)\b',
                r'(?i)\b(put|看跌)\b'
            ],
            'symbols': [
                r'\b(EUR/USD|EURUSD|欧美)\b',
                r'\b(GBP/USD|GBPUSD|镑美)\b',
                r'\b(USD/JPY|USDJPY|美日)\b',
                r'\b(USD/CHF|USDCHF|美瑞)\b',
                r'\b(AUD/USD|AUDUSD|澳美)\b',
                r'\b(USD/CAD|USDCAD|美加)\b',
                r'\b(NZD/USD|NZDUSD|纽美)\b',
                r'\b(XAU/USD|XAUUSD|黄金|GOLD)\b',
                r'\b(XAG/USD|XAGUSD|白银|SILVER)\b'
            ],
            'prices': [
                r'(?i)(?:entry|入场|进场).*?(\d+\.?\d*)',
                r'(?i)(?:tp|take profit|止盈).*?(\d+\.?\d*)',
                r'(?i)(?:sl|stop loss|止损).*?(\d+\.?\d*)'
            ]
        }
    
    async def initialize(self):
        """初始化Telegram客户端"""
        try:
            # 这里需要用户提供Telegram API凭据
            # 实际使用时需要从配置文件读取
            api_id = "YOUR_API_ID"
            api_hash = "YOUR_API_HASH"
            
            self.client = TelegramClient('trading_session', api_id, api_hash)
            await self.client.start()
            
            # 设置消息监听器
            self.client.add_event_handler(
                self.handle_new_message,
                events.NewMessage(chats=[ch['channel_id'] for ch in self.channels_config])
            )
            
            self.is_running = True
            self.logger.info("✅ Telegram信号源初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ Telegram初始化失败: {e}")
            raise
    
    async def handle_new_message(self, event):
        """处理新消息"""
        try:
            message = event.message
            channel_id = str(message.peer_id.channel_id) if hasattr(message.peer_id, 'channel_id') else None
            
            if not channel_id:
                return
            
            # 查找对应的频道配置
            channel_config = None
            for config in self.channels_config:
                if channel_id in config['channel_id'] or config['channel_id'] in str(message.chat_id):
                    channel_config = config
                    break
            
            if not channel_config or not channel_config.get('enabled', True):
                return
            
            # 检查是否是新消息
            last_id = self.last_message_ids.get(channel_id, 0)
            if message.id <= last_id:
                return
            
            self.last_message_ids[channel_id] = message.id
            
            # 解析消息
            signal_data = await self.parse_message(message, channel_config)
            if signal_data:
                await self.signal_queue.put(signal_data)
                self.logger.info(f"📨 收到Telegram信号: {signal_data['symbol']} {signal_data['action']}")
            
        except Exception as e:
            self.logger.error(f"处理Telegram消息错误: {e}")
    
    async def parse_message(self, message: Message, channel_config: Dict) -> Optional[Dict]:
        """解析消息内容"""
        try:
            text = message.text or ""
            if not text:
                return None
            
            # 检查是否包含交易信号
            if not self.is_trading_signal(text):
                return None
            
            # 提取交易信息
            symbol = self.extract_symbol(text)
            action = self.extract_action(text)
            prices = self.extract_prices(text)
            
            if not symbol or not action:
                return None
            
            # 计算信号权重
            weight = channel_config.get('weight', 0.5)
            
            signal_data = {
                'source': f"telegram_{channel_config['name']}",
                'symbol': symbol,
                'action': action,
                'entry_price': prices.get('entry'),
                'stop_loss': prices.get('sl'),
                'take_profit': prices.get('tp'),
                'timestamp': datetime.now(),
                'text': text,
                'weight': weight,
                'channel_name': channel_config['name'],
                'message_id': message.id
            }
            
            return signal_data
            
        except Exception as e:
            self.logger.error(f"解析消息错误: {e}")
            return None
    
    def is_trading_signal(self, text: str) -> bool:
        """判断是否是交易信号"""
        # 检查是否包含买卖关键词
        has_action = False
        for pattern in self.signal_patterns['buy_signals'] + self.signal_patterns['sell_signals']:
            if re.search(pattern, text):
                has_action = True
                break
        
        # 检查是否包含交易品种
        has_symbol = False
        for pattern in self.signal_patterns['symbols']:
            if re.search(pattern, text):
                has_symbol = True
                break
        
        return has_action and has_symbol
    
    def extract_symbol(self, text: str) -> Optional[str]:
        """提取交易品种"""
        for pattern in self.signal_patterns['symbols']:
            match = re.search(pattern, text)
            if match:
                symbol = match.group(0).upper()
                # 标准化符号格式
                symbol = symbol.replace('/', '').replace(' ', '')
                
                # 映射中文名称
                symbol_map = {
                    '欧美': 'EURUSD',
                    '镑美': 'GBPUSD',
                    '美日': 'USDJPY',
                    '美瑞': 'USDCHF',
                    '澳美': 'AUDUSD',
                    '美加': 'USDCAD',
                    '纽美': 'NZDUSD',
                    '黄金': 'XAUUSD',
                    '白银': 'XAGUSD',
                    'GOLD': 'XAUUSD',
                    'SILVER': 'XAGUSD'
                }
                
                return symbol_map.get(symbol, symbol)
        
        return None
    
    def extract_action(self, text: str) -> Optional[str]:
        """提取交易方向"""
        # 检查买入信号
        for pattern in self.signal_patterns['buy_signals']:
            if re.search(pattern, text):
                return 'BUY'
        
        # 检查卖出信号
        for pattern in self.signal_patterns['sell_signals']:
            if re.search(pattern, text):
                return 'SELL'
        
        return None
    
    def extract_prices(self, text: str) -> Dict[str, Optional[float]]:
        """提取价格信息"""
        prices = {'entry': None, 'sl': None, 'tp': None}
        
        try:
            # 提取入场价
            entry_patterns = [
                r'(?i)(?:entry|入场|进场).*?(\d+\.?\d*)',
                r'(?i)(?:price|价格).*?(\d+\.?\d*)',
                r'(?i)@\s*(\d+\.?\d*)'
            ]
            
            for pattern in entry_patterns:
                match = re.search(pattern, text)
                if match:
                    prices['entry'] = float(match.group(1))
                    break
            
            # 提取止盈价
            tp_patterns = [
                r'(?i)(?:tp|take profit|止盈|目标).*?(\d+\.?\d*)',
                r'(?i)(?:target|目标价).*?(\d+\.?\d*)'
            ]
            
            for pattern in tp_patterns:
                match = re.search(pattern, text)
                if match:
                    prices['tp'] = float(match.group(1))
                    break
            
            # 提取止损价
            sl_patterns = [
                r'(?i)(?:sl|stop loss|止损).*?(\d+\.?\d*)',
                r'(?i)(?:stop|停止).*?(\d+\.?\d*)'
            ]
            
            for pattern in sl_patterns:
                match = re.search(pattern, text)
                if match:
                    prices['sl'] = float(match.group(1))
                    break
            
        except ValueError as e:
            self.logger.warning(f"价格解析错误: {e}")
        
        return prices
    
    async def get_signals(self) -> List[Dict]:
        """获取新信号"""
        signals = []
        
        try:
            # 从队列中获取所有待处理信号
            while not self.signal_queue.empty():
                signal = await self.signal_queue.get()
                signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"获取Telegram信号错误: {e}")
        
        return signals
    
    async def get_historical_messages(self, channel_id: str, limit: int = 100) -> List[Dict]:
        """获取历史消息（用于回测）"""
        try:
            messages = []
            async for message in self.client.iter_messages(channel_id, limit=limit):
                if message.text:
                    signal_data = await self.parse_message(message, {'name': 'historical', 'weight': 0.5})
                    if signal_data:
                        messages.append(signal_data)
            
            return messages
            
        except Exception as e:
            self.logger.error(f"获取历史消息错误: {e}")
            return []
    
    def is_active(self) -> bool:
        """检查是否活跃"""
        return self.is_running and self.client and self.client.is_connected()
    
    async def shutdown(self):
        """关闭Telegram客户端"""
        try:
            self.is_running = False
            if self.client:
                await self.client.disconnect()
            
            self.logger.info("✅ Telegram信号源已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭Telegram信号源错误: {e}")


class TelegramSignalFilter:
    """Telegram信号过滤器"""
    
    def __init__(self, filter_config: Dict):
        self.config = filter_config
        self.spam_keywords = [
            '广告', '推广', '加群', '联系', '微信', 'QQ',
            'advertisement', 'promotion', 'contact', 'join'
        ]
    
    def is_spam(self, text: str) -> bool:
        """检查是否是垃圾信息"""
        text_lower = text.lower()
        
        for keyword in self.spam_keywords:
            if keyword in text_lower:
                return True
        
        return False
    
    def is_valid_signal(self, signal_data: Dict) -> bool:
        """验证信号是否有效"""
        # 检查垃圾信息
        if self.is_spam(signal_data['text']):
            return False
        
        # 检查必要字段
        if not signal_data.get('symbol') or not signal_data.get('action'):
            return False
        
        # 检查置信度
        min_confidence = self.config.get('min_confidence', 0.5)
        if signal_data.get('weight', 0) < min_confidence:
            return False
        
        return True
