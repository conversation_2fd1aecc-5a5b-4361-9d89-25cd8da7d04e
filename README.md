# 🚀 MT4智能跟单系统

一个能够自动跟随网上厉害交易师的完整MT4跟单系统，集成多个信号源，智能风险管理。

## ✨ 核心功能

### 📊 多信号源集成
- **Telegram信号抓取**: 监控知名交易师的Telegram频道
- **Myfxbook数据**: 获取顶级交易师的历史业绩和实时信号
- **社交交易平台**: 集成ZuluTrade等平台的信号
- **智能信号过滤**: AI算法筛选高质量交易信号

### 🎯 自动交易执行
- **MT4/MT5连接**: 通过MetaAPI实现云端连接
- **实时信号执行**: 毫秒级信号处理和订单执行
- **多账户管理**: 支持同时管理多个MT4账户
- **订单类型支持**: 市价单、限价单、止损单全支持

### 🛡️ 智能风险管理
- **动态资金分配**: 根据交易师历史表现智能分配资金
- **多层止损保护**: 账户级、交易师级、单笔交易级止损
- **回撤控制**: 实时监控回撤，超限自动暂停跟单
- **风险评估**: 实时计算和显示风险指标

### 📈 数据分析与监控
- **实时监控面板**: Web界面显示所有跟单状态
- **交易师排名**: 根据收益率、胜率、回撤等指标排名
- **收益统计**: 详细的收益分析和报表
- **告警系统**: 重要事件实时通知

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   信号源模块    │    │   风险管理模块  │    │   交易执行模块  │
│                 │    │                 │    │                 │
│ • Telegram Bot  │    │ • 资金分配      │    │ • MetaAPI       │
│ • Myfxbook API  │    │ • 止损控制      │    │ • 订单管理      │
│ • 社交平台      │    │ • 风险评估      │    │ • 状态监控      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   核心控制器    │
                    │                 │
                    │ • 信号处理      │
                    │ • 决策引擎      │
                    │ • 数据存储      │
                    └─────────────────┘
```

## 🚀 快速开始

### 方法1: 一键启动 (推荐新手)
```bash
# 运行启动脚本，自动完成所有设置
python start.py
```

### 方法2: 手动设置
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 复制配置文件
cp config/config.example.json config/config.json

# 3. 编辑配置文件，填入你的API密钥
# 使用任何文本编辑器打开 config/config.json

# 4. 测试系统
python test_system.py

# 5. 启动系统
python start.py
```

### 方法3: 直接启动组件
```bash
# 启动主程序
python main.py

# 启动Web监控面板
python web_dashboard.py
```

## ⚙️ 配置说明

### API配置
- **MetaAPI**: MT4/MT5连接API
- **Telegram Bot**: 信号抓取机器人
- **Myfxbook**: 交易师数据API

### 风险参数
- **最大跟单比例**: 单个交易师最大资金分配比例
- **总体风险限制**: 账户总体风险控制
- **止损设置**: 各级止损参数

### 信号过滤
- **最小胜率**: 交易师最低胜率要求
- **最大回撤**: 可接受的最大历史回撤
- **最小交易次数**: 交易师最少历史交易数

## 📊 监控面板

访问 `http://localhost:8080` 查看实时监控面板：

- **实时PnL**: 当前盈亏状况
- **跟单状态**: 所有交易师跟单状态
- **风险指标**: 实时风险监控
- **交易历史**: 详细交易记录

## 🔧 高级功能

### 自定义策略
- 支持自定义信号过滤规则
- 可配置资金分配算法
- 灵活的风险控制策略

### 多账户管理
- 同时管理多个MT4账户
- 不同账户不同风险参数
- 统一监控和管理

### 智能学习
- 基于历史数据优化参数
- 自动调整跟单比例
- 机器学习信号质量评估

## ⚠️ 风险提示

1. **外汇交易有风险**: 请确保你了解外汇交易的风险
2. **资金管理**: 建议只用闲置资金进行跟单
3. **系统监控**: 定期检查系统运行状态
4. **参数调整**: 根据市场情况及时调整风险参数

## 📁 项目结构

```
mt4-auto-copier/
├── main.py                 # 主程序入口
├── start.py                # 一键启动脚本
├── web_dashboard.py        # Web监控面板
├── test_system.py          # 系统测试工具
├── requirements.txt        # Python依赖包
├── README.md              # 项目说明
├── INSTALL.md             # 详细安装指南
├── config/                # 配置文件目录
│   ├── config.example.json
│   └── config.json
├── src/                   # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── signal_manager.py
│   │   ├── trading_engine.py
│   │   ├── risk_manager.py
│   │   └── portfolio_manager.py
│   ├── sources/           # 信号源模块
│   │   ├── telegram_source.py
│   │   └── myfxbook_source.py
│   ├── utils/             # 工具模块
│   │   ├── config_loader.py
│   │   ├── logger.py
│   │   ├── signal_parser.py
│   │   └── signal_validator.py
│   └── database/          # 数据库模块
│       └── db_manager.py
├── templates/             # Web模板
├── logs/                  # 日志文件
└── data/                  # 数据文件
```

## 🎯 核心特性详解

### 🤖 智能信号识别
- **多语言支持**: 支持中英文交易信号识别
- **格式自适应**: 自动识别各种信号格式
- **置信度评估**: AI算法评估信号质量
- **实时过滤**: 多层过滤确保信号有效性

### 🛡️ 全方位风险控制
- **多级止损**: 账户级、交易师级、单笔交易级
- **动态仓位**: 根据风险自动调整仓位大小
- **相关性管理**: 避免过度集中投资
- **紧急保护**: 极端情况自动停止交易

### 📊 专业级监控
- **实时数据**: 毫秒级数据更新
- **可视化图表**: 直观的盈亏和风险展示
- **历史分析**: 详细的交易历史和统计
- **移动端适配**: 支持手机和平板访问

## 🔧 系统要求

### 最低配置
- **操作系统**: Windows 10/11
- **Python**: 3.8+
- **内存**: 4GB RAM
- **硬盘**: 2GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Windows 11
- **Python**: 3.10+
- **内存**: 8GB RAM
- **硬盘**: 5GB 可用空间
- **网络**: 光纤宽带

## 📞 技术支持

### 获取帮助
1. **查看文档**: 详细阅读 [INSTALL.md](INSTALL.md)
2. **运行测试**: `python test_system.py`
3. **查看日志**: `logs/trading.log`
4. **社区支持**: 加入讨论群组

### 常见问题
- **MT5连接失败**: 确保MT5正在运行且已登录
- **信号无法识别**: 检查信号格式和语言设置
- **风险控制过严**: 调整风险参数配置
- **Web面板无法访问**: 检查防火墙和端口设置

## ⚠️ 免责声明

1. **投资风险**: 外汇交易存在高风险，可能导致资金损失
2. **系统风险**: 软件可能存在bug，请充分测试后使用
3. **网络风险**: 网络中断可能影响交易执行
4. **法律合规**: 请确保在您的地区使用是合法的

**请只投资您能承受损失的资金！**

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

## 🎉 开始你的智能跟单之旅！

```bash
# 一键启动，开始赚钱！
python start.py
```

**祝您交易顺利，收益满满！** 💰
