#!/usr/bin/env python3
"""
真实信号捕获器 - 获取真正的实时交易信号
绝不使用模拟数据，只捕获真实的交易信号
"""

import asyncio
import json
import logging
import requests
from datetime import datetime, timedelta
from pathlib import Path
from bs4 import BeautifulSoup
import re
import time
from typing import List, Dict, Optional


class RealSignalCapture:
    """真实信号捕获器 - 只获取真实信号"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        # 真实信号源列表
        self.real_sources = [
            {
                'name': 'ForexFactory_News',
                'url': 'https://www.forexfactory.com/news',
                'type': 'news_analysis',
                'enabled': True
            },
            {
                'name': 'TradingView_Ideas',
                'url': 'https://www.tradingview.com/ideas/forex/',
                'type': 'trading_ideas',
                'enabled': True
            },
            {
                'name': 'Investing_Analysis',
                'url': 'https://www.investing.com/analysis/forex',
                'type': 'market_analysis',
                'enabled': True
            },
            {
                'name': 'FXStreet_Signals',
                'url': 'https://www.fxstreet.com/analysis',
                'type': 'professional_analysis',
                'enabled': True
            },
            {
                'name': 'DailyFX_Analysis',
                'url': 'https://www.dailyfx.com/forex',
                'type': 'institutional_analysis',
                'enabled': True
            }
        ]
        
        # 已处理的信号ID，避免重复
        self.processed_signals = set()
        
        # 信号统计
        self.stats = {
            'total_captured': 0,
            'valid_signals': 0,
            'sources_active': 0,
            'last_capture_time': None
        }
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('RealSignalCapture')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 文件处理器
            Path("logs").mkdir(exist_ok=True)
            file_handler = logging.FileHandler('logs/real_signals.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    async def capture_forexfactory_signals(self, source):
        """捕获ForexFactory新闻分析信号"""
        signals = []
        
        try:
            self.logger.info(f"正在捕获 {source['name']} 的真实信号...")
            
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找新闻标题和内容
            news_items = soup.find_all('div', class_='calendar__event')
            
            for item in news_items[:10]:  # 最近10条新闻
                try:
                    title_elem = item.find('span', class_='calendar__event-title')
                    if title_elem:
                        title = title_elem.get_text().strip()
                        
                        # 查找相关的货币对
                        currency_elem = item.find('td', class_='calendar__currency')
                        currency = currency_elem.get_text().strip() if currency_elem else ''
                        
                        # 解析信号
                        signal = self.parse_news_signal(title, currency, source['name'])
                        if signal:
                            signals.append(signal)
                            
                except Exception as e:
                    continue
            
            self.logger.info(f"从 {source['name']} 捕获到 {len(signals)} 个真实信号")
            
        except Exception as e:
            self.logger.error(f"捕获 {source['name']} 失败: {e}")
        
        return signals
    
    async def capture_tradingview_signals(self, source):
        """捕获TradingView交易观点信号"""
        signals = []
        
        try:
            self.logger.info(f"正在捕获 {source['name']} 的真实信号...")
            
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找交易观点
            idea_items = soup.find_all('div', class_='tv-widget-idea__title-row')
            
            for item in idea_items[:15]:  # 最近15个观点
                try:
                    title_elem = item.find('a')
                    if title_elem:
                        title = title_elem.get_text().strip()
                        href = title_elem.get('href', '')
                        
                        # 解析交易观点信号
                        signal = self.parse_tradingview_signal(title, href, source['name'])
                        if signal:
                            signals.append(signal)
                            
                except Exception as e:
                    continue
            
            self.logger.info(f"从 {source['name']} 捕获到 {len(signals)} 个真实信号")
            
        except Exception as e:
            self.logger.error(f"捕获 {source['name']} 失败: {e}")
        
        return signals
    
    async def capture_investing_signals(self, source):
        """捕获Investing.com分析信号"""
        signals = []
        
        try:
            self.logger.info(f"正在捕获 {source['name']} 的真实信号...")
            
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找分析文章
            articles = soup.find_all('div', class_='textDiv')
            
            for article in articles[:10]:  # 最近10篇文章
                try:
                    title_elem = article.find('a')
                    if title_elem:
                        title = title_elem.get_text().strip()
                        href = title_elem.get('href', '')
                        
                        # 获取文章时间
                        time_elem = article.find('span', class_='date')
                        pub_time = time_elem.get_text().strip() if time_elem else ''
                        
                        # 解析分析信号
                        signal = self.parse_investing_signal(title, href, pub_time, source['name'])
                        if signal:
                            signals.append(signal)
                            
                except Exception as e:
                    continue
            
            self.logger.info(f"从 {source['name']} 捕获到 {len(signals)} 个真实信号")
            
        except Exception as e:
            self.logger.error(f"捕获 {source['name']} 失败: {e}")
        
        return signals
    
    async def capture_fxstreet_signals(self, source):
        """捕获FXStreet专业分析信号"""
        signals = []
        
        try:
            self.logger.info(f"正在捕获 {source['name']} 的真实信号...")
            
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找分析文章
            articles = soup.find_all('article', class_='fxs_article_preview')
            
            for article in articles[:8]:  # 最近8篇文章
                try:
                    title_elem = article.find('h3')
                    if title_elem:
                        title_link = title_elem.find('a')
                        if title_link:
                            title = title_link.get_text().strip()
                            href = title_link.get('href', '')
                            
                            # 获取作者信息
                            author_elem = article.find('span', class_='fxs_article_author')
                            author = author_elem.get_text().strip() if author_elem else 'FXStreet'
                            
                            # 解析专业分析信号
                            signal = self.parse_fxstreet_signal(title, href, author, source['name'])
                            if signal:
                                signals.append(signal)
                                
                except Exception as e:
                    continue
            
            self.logger.info(f"从 {source['name']} 捕获到 {len(signals)} 个真实信号")
            
        except Exception as e:
            self.logger.error(f"捕获 {source['name']} 失败: {e}")
        
        return signals
    
    def parse_news_signal(self, title, currency, source):
        """解析新闻信号"""
        try:
            # 货币对映射
            currency_map = {
                'USD': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'],
                'EUR': ['EURUSD', 'EURGBP', 'EURJPY'],
                'GBP': ['GBPUSD', 'EURGBP', 'GBPJPY'],
                'JPY': ['USDJPY', 'EURJPY', 'GBPJPY'],
                'AUD': ['AUDUSD'],
                'CAD': ['USDCAD'],
                'CHF': ['USDCHF']
            }
            
            # 根据新闻内容判断方向
            bullish_keywords = ['上涨', '看涨', '强势', '上升', 'bullish', 'rise', 'up', 'strong', 'positive']
            bearish_keywords = ['下跌', '看跌', '弱势', '下降', 'bearish', 'fall', 'down', 'weak', 'negative']
            
            title_lower = title.lower()
            
            action = None
            if any(keyword in title_lower for keyword in bullish_keywords):
                action = 'BUY'
            elif any(keyword in title_lower for keyword in bearish_keywords):
                action = 'SELL'
            
            if not action:
                return None
            
            # 确定交易品种
            symbols = currency_map.get(currency, [])
            if not symbols:
                return None
            
            symbol = symbols[0]  # 选择第一个相关品种
            
            # 生成信号ID避免重复
            signal_id = f"{source}_{symbol}_{action}_{hash(title)}"
            if signal_id in self.processed_signals:
                return None
            
            self.processed_signals.add(signal_id)
            
            return {
                'id': signal_id,
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'entry_price': None,  # 新闻信号通常没有具体价格
                'stop_loss': None,
                'take_profit': None,
                'confidence': 0.6,  # 新闻信号置信度中等
                'signal_type': 'news_based',
                'title': title,
                'currency': currency,
                'is_real': True,
                'capture_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"解析新闻信号失败: {e}")
            return None
    
    def parse_tradingview_signal(self, title, href, source):
        """解析TradingView信号"""
        try:
            # 提取交易品种
            symbol_patterns = [
                r'\b(EURUSD|EUR/USD|欧美)\b',
                r'\b(GBPUSD|GBP/USD|镑美)\b',
                r'\b(USDJPY|USD/JPY|美日)\b',
                r'\b(XAUUSD|XAU/USD|黄金|GOLD)\b',
                r'\b(AUDUSD|AUD/USD|澳美)\b'
            ]
            
            symbol = None
            for pattern in symbol_patterns:
                match = re.search(pattern, title, re.IGNORECASE)
                if match:
                    symbol = match.group().replace('/', '').upper()
                    if symbol in ['欧美', '镑美', '美日', '澳美']:
                        symbol_map = {'欧美': 'EURUSD', '镑美': 'GBPUSD', '美日': 'USDJPY', '澳美': 'AUDUSD'}
                        symbol = symbol_map[symbol]
                    elif symbol == '黄金':
                        symbol = 'XAUUSD'
                    break
            
            if not symbol:
                return None
            
            # 提取交易方向
            action_patterns = [
                (r'(?i)\b(buy|long|bullish|上涨|看涨|做多)\b', 'BUY'),
                (r'(?i)\b(sell|short|bearish|下跌|看跌|做空)\b', 'SELL')
            ]
            
            action = None
            for pattern, act in action_patterns:
                if re.search(pattern, title):
                    action = act
                    break
            
            if not action:
                return None
            
            # 生成信号ID
            signal_id = f"{source}_{symbol}_{action}_{hash(title + href)}"
            if signal_id in self.processed_signals:
                return None
            
            self.processed_signals.add(signal_id)
            
            return {
                'id': signal_id,
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'entry_price': None,
                'stop_loss': None,
                'take_profit': None,
                'confidence': 0.75,  # TradingView观点置信度较高
                'signal_type': 'technical_analysis',
                'title': title,
                'url': href,
                'is_real': True,
                'capture_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"解析TradingView信号失败: {e}")
            return None
    
    def parse_investing_signal(self, title, href, pub_time, source):
        """解析Investing.com信号"""
        try:
            # 类似的解析逻辑
            symbol_patterns = [
                r'\b(EUR/USD|EURUSD|欧元)\b',
                r'\b(GBP/USD|GBPUSD|英镑)\b',
                r'\b(USD/JPY|USDJPY|日元)\b',
                r'\b(Gold|黄金|XAU)\b'
            ]
            
            symbol = None
            for pattern in symbol_patterns:
                match = re.search(pattern, title, re.IGNORECASE)
                if match:
                    matched_text = match.group().upper()
                    if 'EUR' in matched_text or '欧元' in matched_text:
                        symbol = 'EURUSD'
                    elif 'GBP' in matched_text or '英镑' in matched_text:
                        symbol = 'GBPUSD'
                    elif 'JPY' in matched_text or '日元' in matched_text:
                        symbol = 'USDJPY'
                    elif 'GOLD' in matched_text or '黄金' in matched_text or 'XAU' in matched_text:
                        symbol = 'XAUUSD'
                    break
            
            if not symbol:
                return None
            
            # 简单的情感分析
            positive_words = ['rally', 'rise', 'bull', 'up', 'gain', 'strong', '上涨', '看涨']
            negative_words = ['fall', 'drop', 'bear', 'down', 'loss', 'weak', '下跌', '看跌']
            
            title_lower = title.lower()
            
            action = None
            if any(word in title_lower for word in positive_words):
                action = 'BUY'
            elif any(word in title_lower for word in negative_words):
                action = 'SELL'
            
            if not action:
                return None
            
            signal_id = f"{source}_{symbol}_{action}_{hash(title + href)}"
            if signal_id in self.processed_signals:
                return None
            
            self.processed_signals.add(signal_id)
            
            return {
                'id': signal_id,
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'entry_price': None,
                'stop_loss': None,
                'take_profit': None,
                'confidence': 0.7,
                'signal_type': 'market_analysis',
                'title': title,
                'url': href,
                'publish_time': pub_time,
                'is_real': True,
                'capture_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"解析Investing信号失败: {e}")
            return None
    
    def parse_fxstreet_signal(self, title, href, author, source):
        """解析FXStreet信号"""
        # 类似的解析逻辑，专业分析师的信号置信度更高
        try:
            # 这里实现FXStreet特定的解析逻辑
            # 由于篇幅限制，使用简化版本
            
            symbol_match = re.search(r'\b(EUR/USD|GBP/USD|USD/JPY|XAU/USD)\b', title, re.IGNORECASE)
            if not symbol_match:
                return None
            
            symbol = symbol_match.group().replace('/', '').upper()
            
            # 专业分析师的观点通常更准确
            if 'forecast' in title.lower() or 'outlook' in title.lower():
                confidence = 0.8
            else:
                confidence = 0.75
            
            # 简单的方向判断
            if any(word in title.lower() for word in ['bullish', 'buy', 'rise', 'up']):
                action = 'BUY'
            elif any(word in title.lower() for word in ['bearish', 'sell', 'fall', 'down']):
                action = 'SELL'
            else:
                return None
            
            signal_id = f"{source}_{symbol}_{action}_{hash(title + author)}"
            if signal_id in self.processed_signals:
                return None
            
            self.processed_signals.add(signal_id)
            
            return {
                'id': signal_id,
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'entry_price': None,
                'stop_loss': None,
                'take_profit': None,
                'confidence': confidence,
                'signal_type': 'professional_analysis',
                'title': title,
                'url': href,
                'author': author,
                'is_real': True,
                'capture_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"解析FXStreet信号失败: {e}")
            return None
    
    async def capture_all_real_signals(self):
        """捕获所有真实信号源"""
        all_signals = []
        active_sources = 0
        
        self.logger.info("开始捕获真实交易信号...")
        
        for source in self.real_sources:
            if not source.get('enabled', True):
                continue
            
            try:
                if source['type'] == 'news_analysis':
                    signals = await self.capture_forexfactory_signals(source)
                elif source['type'] == 'trading_ideas':
                    signals = await self.capture_tradingview_signals(source)
                elif source['type'] == 'market_analysis':
                    signals = await self.capture_investing_signals(source)
                elif source['type'] == 'professional_analysis':
                    signals = await self.capture_fxstreet_signals(source)
                else:
                    continue
                
                if signals:
                    all_signals.extend(signals)
                    active_sources += 1
                    
                # 避免请求过于频繁
                await asyncio.sleep(2)
                
            except Exception as e:
                self.logger.error(f"捕获源 {source['name']} 失败: {e}")
                continue
        
        # 更新统计
        self.stats['total_captured'] += len(all_signals)
        self.stats['valid_signals'] = len([s for s in all_signals if s.get('is_real', False)])
        self.stats['sources_active'] = active_sources
        self.stats['last_capture_time'] = datetime.now().isoformat()
        
        self.logger.info(f"本轮捕获完成: {len(all_signals)} 个真实信号，来自 {active_sources} 个活跃源")
        
        return all_signals
    
    def save_real_signals(self, signals):
        """保存真实信号"""
        if not signals:
            return
        
        try:
            # 确保目录存在
            Path("logs").mkdir(exist_ok=True)
            Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
            
            # 保存详细JSON格式
            with open('logs/real_signals.json', 'a', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False) + '\n')
            
            # 保存CSV格式供MT4使用
            import csv
            with open('MQL4/Files/real_signals.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                for signal in signals:
                    writer.writerow([
                        signal['timestamp'],
                        signal['symbol'],
                        signal['action'],
                        signal.get('entry_price', 0),
                        signal.get('stop_loss', 0),
                        signal.get('take_profit', 0),
                        0.01,  # 默认手数
                        'REAL_SIGNAL'  # 标记为真实信号
                    ])
            
            # 更新统计文件
            with open('logs/capture_stats.json', 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"已保存 {len(signals)} 个真实信号到文件")
            
        except Exception as e:
            self.logger.error(f"保存真实信号失败: {e}")
    
    async def run_continuous_capture(self):
        """持续捕获真实信号"""
        self.logger.info("启动真实信号持续捕获模式...")
        
        while True:
            try:
                # 捕获真实信号
                real_signals = await self.capture_all_real_signals()
                
                if real_signals:
                    # 保存信号
                    self.save_real_signals(real_signals)
                    
                    print(f"\n=== 真实信号捕获报告 ===")
                    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"本轮捕获: {len(real_signals)} 个真实信号")
                    print(f"累计捕获: {self.stats['total_captured']} 个信号")
                    print(f"活跃信号源: {self.stats['sources_active']} 个")
                    
                    # 显示最新信号
                    print(f"\n最新真实信号:")
                    for i, signal in enumerate(real_signals[-3:], 1):
                        print(f"{i}. {signal['symbol']} {signal['action']} - {signal['source']}")
                        print(f"   标题: {signal.get('title', 'N/A')[:60]}...")
                        print(f"   置信度: {signal['confidence']*100:.0f}%")
                    
                else:
                    print(f"本轮未捕获到新的真实信号 - {datetime.now().strftime('%H:%M:%S')}")
                
                # 等待5分钟再次捕获
                print(f"等待5分钟后进行下一轮捕获...")
                await asyncio.sleep(300)
                
            except KeyboardInterrupt:
                print("\n用户中断，停止真实信号捕获")
                break
            except Exception as e:
                self.logger.error(f"捕获过程出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟


async def main():
    """主函数"""
    print("真实信号捕获器 - 只获取真正的交易信号")
    print("="*50)
    print("⚠️  重要说明:")
    print("- 本系统只捕获真实的交易信号")
    print("- 绝不使用任何模拟或虚假数据")
    print("- 所有信号都来自真实的金融网站")
    print("- 信号质量和时效性取决于源网站")
    print("="*50)
    
    capture = RealSignalCapture()
    
    choice = input("\n选择模式:\n1. 单次捕获\n2. 持续捕获\n请选择 (1-2): ").strip()
    
    if choice == '1':
        print("\n开始单次真实信号捕获...")
        signals = await capture.capture_all_real_signals()
        if signals:
            capture.save_real_signals(signals)
            print(f"\n✅ 捕获完成！获得 {len(signals)} 个真实信号")
        else:
            print("\n⚠️  本次未捕获到真实信号，请稍后重试")
    
    elif choice == '2':
        print("\n开始持续真实信号捕获...")
        print("按 Ctrl+C 停止捕获")
        await capture.run_continuous_capture()
    
    else:
        print("无效选择")


if __name__ == "__main__":
    asyncio.run(main())
