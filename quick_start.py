#!/usr/bin/env python3
"""
快速启动脚本 - 一键启动监控面板并确保有数据显示
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from datetime import datetime


def create_test_data():
    """创建测试数据确保监控面板有内容显示"""
    print("正在生成测试数据...")
    
    # 确保目录存在
    Path("logs").mkdir(exist_ok=True)
    Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
    
    # 生成测试信号数据
    test_signals = [
        {
            'timestamp': datetime.now().isoformat(),
            'source': 'VIP外汇信号',
            'symbol': 'EURUSD',
            'action': 'BUY',
            'entry_price': 1.0850,
            'stop_loss': 1.0800,
            'take_profit': 1.0950,
            'confidence': 0.85,
            'text': 'EURUSD BUY @ 1.0850, SL: 1.0800, TP: 1.0950 - 高质量信号',
            'validated': True
        },
        {
            'timestamp': datetime.now().isoformat(),
            'source': '专业交易师',
            'symbol': 'XAUUSD',
            'action': 'SELL',
            'entry_price': 1950.00,
            'stop_loss': 1960.00,
            'take_profit': 1930.00,
            'confidence': 0.92,
            'text': '黄金 卖出 @ 1950, 止损 1960, 止盈 1930 - 顶级信号',
            'validated': True
        },
        {
            'timestamp': datetime.now().isoformat(),
            'source': 'Myfxbook顶级交易师',
            'symbol': 'GBPUSD',
            'action': 'BUY',
            'entry_price': 1.2500,
            'stop_loss': 1.2450,
            'take_profit': 1.2600,
            'confidence': 0.78,
            'text': 'GBPUSD BUY @ 1.2500, SL: 1.2450, TP: 1.2600 - 付费信号',
            'validated': True
        }
    ]
    
    # 保存信号数据
    with open('logs/validated_signals.json', 'w', encoding='utf-8') as f:
        for signal in test_signals:
            f.write(json.dumps(signal, ensure_ascii=False) + '\n')
    
    # 生成账户信息
    with open('MQL4/Files/test_account_info.txt', 'w', encoding='utf-8') as f:
        f.write(f"Account: ********\n")
        f.write(f"Balance: 10000.00\n")
        f.write(f"Equity: 10235.50\n")
        f.write(f"Margin: 180.00\n")
        f.write(f"Free Margin: 10055.50\n")
        f.write(f"Today PnL: 235.50\n")
        f.write(f"Today Trades: 3\n")
        f.write(f"Open Positions: 2\n")
        f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 生成CSV格式信号文件
    import csv
    with open('MQL4/Files/validated_signals.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        for signal in test_signals:
            writer.writerow([
                signal['timestamp'],
                signal['symbol'],
                signal['action'],
                signal.get('entry_price', 0),
                signal.get('stop_loss', 0),
                signal.get('take_profit', 0),
                0.01,  # 默认手数
                'VALIDATED'
            ])
    
    print("测试数据生成完成！")
    print("- 3个高质量测试信号")
    print("- 模拟账户信息")
    print("- CSV格式信号文件")


def check_dependencies():
    """检查必要的依赖"""
    required_packages = ['flask', 'requests', 'beautifulsoup4']
    missing = []
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
            else:
                __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"正在安装缺失的依赖: {', '.join(missing)}")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + missing, 
                          check=True, capture_output=True)
            print("依赖安装完成！")
        except subprocess.CalledProcessError:
            print("依赖安装失败，请手动安装:")
            print(f"pip install {' '.join(missing)}")
            return False
    
    return True


def start_dashboard():
    """启动监控面板"""
    print("\n启动增强版监控面板...")
    print("访问地址: http://localhost:8080")
    print("按 Ctrl+C 停止服务")
    
    try:
        subprocess.run([sys.executable, "enhanced_dashboard.py"])
    except KeyboardInterrupt:
        print("\n监控面板已停止")
    except FileNotFoundError:
        print("错误: enhanced_dashboard.py 文件不存在")
        print("请确保所有文件都已正确下载")
    except Exception as e:
        print(f"启动失败: {e}")


def show_status():
    """显示系统状态"""
    print("\n系统状态检查:")
    print("="*40)
    
    # 检查文件
    files_to_check = [
        ("enhanced_dashboard.py", "监控面板"),
        ("logs/validated_signals.json", "信号数据"),
        ("MQL4/Files/test_account_info.txt", "账户信息")
    ]
    
    for file_path, description in files_to_check:
        if Path(file_path).exists():
            print(f"✓ {description}: 存在")
        else:
            print(f"✗ {description}: 缺失")
    
    # 检查信号数量
    signals_file = Path("logs/validated_signals.json")
    if signals_file.exists():
        try:
            with open(signals_file, 'r', encoding='utf-8') as f:
                signal_count = len(f.readlines())
            print(f"✓ 信号数量: {signal_count} 个")
        except:
            print("✗ 信号文件读取失败")
    
    print("="*40)


def main():
    """主函数"""
    print("MT4智能跟单系统 - 快速启动")
    print("="*40)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 创建测试数据
    create_test_data()
    
    # 显示状态
    show_status()
    
    # 启动监控面板
    print("\n准备启动监控面板...")
    input("按回车键继续...")
    
    start_dashboard()


if __name__ == "__main__":
    main()
