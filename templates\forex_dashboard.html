<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外汇监控系统 - ForexFactory跟单</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .stat-card .value {
            font-size: 2.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-card .change {
            font-size: 0.9em;
            padding: 5px 10px;
            border-radius: 20px;
        }
        
        .positive { background: #e8f5e8; color: #2e7d32; }
        .negative { background: #ffebee; color: #c62828; }
        .neutral { background: #f5f5f5; color: #666; }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .panel h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .signals-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        
        .signal-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .signal-item.buy { border-left-color: #28a745; }
        .signal-item.sell { border-left-color: #dc3545; }
        
        .signal-info h4 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .signal-info p {
            color: #666;
            font-size: 0.9em;
        }
        
        .signal-meta {
            text-align: right;
        }
        
        .confidence {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 20px;
            color: white;
            font-size: 0.8em;
        }
        
        .confidence.high { background: #28a745; }
        .confidence.medium { background: #ffc107; color: #333; }
        .confidence.low { background: #dc3545; }
        
        .traders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .trader-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .trader-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .trader-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        
        .trader-score {
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        
        .trader-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .trader-stat {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .trader-stat .label {
            font-size: 0.8em;
            color: #666;
            text-transform: uppercase;
        }
        
        .trader-stat .value {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-top: 5px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #999;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        .last-update {
            color: #666;
            font-size: 0.8em;
            text-align: center;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💱 外汇监控系统</h1>
            <p>ForexFactory实时跟单 | 专注主要外汇对</p>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>总账户数</h3>
                <div class="value" id="total-accounts">-</div>
                <div class="change neutral" id="accounts-change">初始化中</div>
            </div>
            <div class="stat-card">
                <h3>总权益</h3>
                <div class="value" id="total-equity">$-</div>
                <div class="change" id="equity-change">-</div>
            </div>
            <div class="stat-card">
                <h3>总盈亏</h3>
                <div class="value" id="total-profit">$-</div>
                <div class="change" id="profit-change">-</div>
            </div>
            <div class="stat-card">
                <h3>实时信号</h3>
                <div class="value" id="total-signals">-</div>
                <div class="change neutral" id="signals-change">监控中</div>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 最新信号 -->
            <div class="panel">
                <h2>🎯 最新外汇信号</h2>
                <button class="refresh-btn" onclick="refreshSignals()">刷新信号</button>
                <div class="signals-list" id="signals-list">
                    <div class="loading">正在加载信号...</div>
                </div>
                <div class="last-update" id="signals-update">-</div>
            </div>
            
            <!-- 监控统计 -->
            <div class="panel">
                <h2>📊 监控统计</h2>
                <div id="monitor-stats">
                    <div class="loading">正在加载统计...</div>
                </div>
            </div>
        </div>
        
        <!-- 交易员表现 -->
        <div class="panel">
            <h2>👥 交易员表现排行</h2>
            <button class="refresh-btn" onclick="refreshTraders()">刷新数据</button>
            <div class="traders-grid" id="traders-grid">
                <div class="loading">正在加载交易员数据...</div>
            </div>
            <div class="last-update" id="traders-update">-</div>
        </div>
    </div>

    <script>
        // WebSocket连接
        const socket = io();
        
        // 全局数据
        let accountData = {};
        let signalsData = [];
        let tradersData = [];
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            
            // 设置定时刷新
            setInterval(loadAllData, 30000); // 30秒刷新一次
        });
        
        // WebSocket事件监听
        socket.on('new_signals', function(data) {
            console.log('收到新信号:', data);
            signalsData = data.signals.concat(signalsData).slice(0, 20);
            updateSignalsDisplay();
        });
        
        socket.on('account_update', function(data) {
            console.log('账户更新:', data);
            accountData = data.accounts;
            updateAccountStats();
        });
        
        // 加载所有数据
        async function loadAllData() {
            try {
                await Promise.all([
                    loadAccountInfo(),
                    loadSignals(),
                    loadTraderPerformance(),
                    loadMonitorStats()
                ]);
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }
        
        // 加载账户信息
        async function loadAccountInfo() {
            try {
                const response = await fetch('/api/account_info');
                const data = await response.json();
                accountData = data.accounts;
                updateAccountStats();
            } catch (error) {
                console.error('加载账户信息失败:', error);
            }
        }
        
        // 加载信号
        async function loadSignals() {
            try {
                const response = await fetch('/api/signals');
                const data = await response.json();
                signalsData = data.signals;
                updateSignalsDisplay();
                document.getElementById('signals-update').textContent = `最后更新: ${new Date(data.last_update).toLocaleString()}`;
            } catch (error) {
                console.error('加载信号失败:', error);
            }
        }
        
        // 加载交易员表现
        async function loadTraderPerformance() {
            try {
                const response = await fetch('/api/trader_performance');
                const data = await response.json();
                tradersData = data.traders;
                updateTradersDisplay();
                document.getElementById('traders-update').textContent = `最后更新: ${new Date(data.last_update).toLocaleString()}`;
            } catch (error) {
                console.error('加载交易员表现失败:', error);
            }
        }
        
        // 加载监控统计
        async function loadMonitorStats() {
            try {
                const response = await fetch('/api/monitor_stats');
                const data = await response.json();
                updateMonitorStats(data);
            } catch (error) {
                console.error('加载监控统计失败:', error);
            }
        }
        
        // 更新账户统计
        function updateAccountStats() {
            const totalAccounts = Object.keys(accountData).length;
            const totalEquity = Object.values(accountData).reduce((sum, acc) => sum + acc.equity, 0);
            const totalProfit = Object.values(accountData).reduce((sum, acc) => sum + acc.total_profit, 0);
            
            document.getElementById('total-accounts').textContent = totalAccounts;
            document.getElementById('total-equity').textContent = `$${totalEquity.toFixed(2)}`;
            document.getElementById('total-profit').textContent = `$${totalProfit.toFixed(2)}`;
            
            // 更新变化指示器
            const equityChange = document.getElementById('equity-change');
            const profitChange = document.getElementById('profit-change');
            
            if (totalProfit > 0) {
                profitChange.textContent = `+${totalProfit.toFixed(2)}`;
                profitChange.className = 'change positive';
            } else if (totalProfit < 0) {
                profitChange.textContent = `${totalProfit.toFixed(2)}`;
                profitChange.className = 'change negative';
            } else {
                profitChange.textContent = '0.00';
                profitChange.className = 'change neutral';
            }
        }
        
        // 更新信号显示
        function updateSignalsDisplay() {
            const signalsList = document.getElementById('signals-list');
            
            if (signalsData.length === 0) {
                signalsList.innerHTML = '<div class="no-data">暂无外汇信号</div>';
                document.getElementById('total-signals').textContent = '0';
                return;
            }
            
            document.getElementById('total-signals').textContent = signalsData.length;
            
            const signalsHtml = signalsData.map(signal => {
                const confidenceClass = signal.confidence > 0.7 ? 'high' : signal.confidence > 0.5 ? 'medium' : 'low';
                const actionClass = signal.action.toLowerCase();
                const time = new Date(signal.timestamp).toLocaleTimeString();
                
                return `
                    <div class="signal-item ${actionClass}">
                        <div class="signal-info">
                            <h4>${signal.symbol} ${signal.action}</h4>
                            <p>来源: ${signal.trader_name} | ${time}</p>
                        </div>
                        <div class="signal-meta">
                            <div class="confidence ${confidenceClass}">${(signal.confidence * 100).toFixed(0)}%</div>
                            <p style="margin-top: 5px; font-size: 0.8em; color: #666;">
                                ${signal.minutes_since_entry}分钟前
                            </p>
                        </div>
                    </div>
                `;
            }).join('');
            
            signalsList.innerHTML = signalsHtml;
        }
        
        // 更新交易员显示
        function updateTradersDisplay() {
            const tradersGrid = document.getElementById('traders-grid');
            
            if (tradersData.length === 0) {
                tradersGrid.innerHTML = '<div class="no-data">暂无交易员数据</div>';
                return;
            }
            
            const tradersHtml = tradersData.map((trader, index) => {
                const profitClass = trader.profit > 0 ? 'positive' : trader.profit < 0 ? 'negative' : 'neutral';
                const rankEmoji = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}`;
                
                return `
                    <div class="trader-card">
                        <div class="trader-header">
                            <div class="trader-name">${rankEmoji} ${trader.name}</div>
                            <div class="trader-score">评分: ${trader.score}</div>
                        </div>
                        <div class="trader-stats">
                            <div class="trader-stat">
                                <div class="label">权益</div>
                                <div class="value">$${trader.equity.toFixed(2)}</div>
                            </div>
                            <div class="trader-stat">
                                <div class="label">盈亏</div>
                                <div class="value ${profitClass}">$${trader.profit.toFixed(2)}</div>
                            </div>
                            <div class="trader-stat">
                                <div class="label">胜率</div>
                                <div class="value">${trader.win_rate.toFixed(1)}%</div>
                            </div>
                            <div class="trader-stat">
                                <div class="label">交易数</div>
                                <div class="value">${trader.total_trades}</div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            tradersGrid.innerHTML = tradersHtml;
        }
        
        // 更新监控统计
        function updateMonitorStats(stats) {
            const monitorStatsDiv = document.getElementById('monitor-stats');
            
            const statsHtml = `
                <div class="trader-stats">
                    <div class="trader-stat">
                        <div class="label">监控周期</div>
                        <div class="value">${stats.monitoring_cycles || 0}</div>
                    </div>
                    <div class="trader-stat">
                        <div class="label">新交易检测</div>
                        <div class="value">${stats.new_trades_detected || 0}</div>
                    </div>
                    <div class="trader-stat">
                        <div class="label">生成信号</div>
                        <div class="value">${stats.signals_generated || 0}</div>
                    </div>
                    <div class="trader-stat">
                        <div class="label">外汇信号</div>
                        <div class="value">${stats.forex_signals_only || 0}</div>
                    </div>
                </div>
            `;
            
            monitorStatsDiv.innerHTML = statsHtml;
        }
        
        // 刷新函数
        function refreshSignals() {
            loadSignals();
        }
        
        function refreshTraders() {
            loadTraderPerformance();
        }
        
        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });
    </script>
</body>
</html>
