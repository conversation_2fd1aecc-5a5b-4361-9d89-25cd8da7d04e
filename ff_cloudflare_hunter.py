#!/usr/bin/env python3
"""
ForexFactory Cloudflare猎手 - 专门处理Cloudflare验证
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup


class FFCloudflareHunter:
    """专门处理Cloudflare验证的ForexFactory猎手"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.base_url = "https://www.forexfactory.com"
        self.trades_url = "https://www.forexfactory.com/trades"
        
        # Cloudflare特殊配置
        self.cf_wait_time = 300  # 5分钟等待时间
        self.cf_check_interval = 2  # 每2秒检查一次
        
        self.captured_data = []
        self.session_stats = {
            'cloudflare_encounters': 0,
            'verification_attempts': 0,
            'successful_bypasses': 0,
            'data_extracted': 0
        }
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('FFCloudflareHunter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/ff_cloudflare_hunter.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def create_cloudflare_resistant_driver(self):
        """创建抗Cloudflare检测的驱动"""
        try:
            options = Options()
            
            # 关键：使用真实用户配置
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 真实浏览器特征
            options.add_argument('--no-first-run')
            options.add_argument('--no-service-autorun')
            options.add_argument('--no-default-browser-check')
            
            # 用户代理 - 使用最新的Chrome
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 窗口设置
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--start-maximized')
            
            # 禁用一些可能被检测的功能
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--no-sandbox')
            
            # 语言设置
            options.add_argument('--lang=zh-CN')
            
            driver = webdriver.Chrome(options=options)
            
            # 执行反检测脚本
            driver.execute_script("""
                // 移除webdriver属性
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                
                // 修改plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
                
                // 修改languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en']
                });
                
                // 添加chrome对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };
                
                // 修改权限查询
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // 伪造一些浏览器特征
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 4
                });
                
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8
                });
            """)
            
            return driver
            
        except Exception as e:
            self.logger.error(f"创建Cloudflare抗检测驱动失败: {e}")
            return None
    
    async def handle_cloudflare_challenge(self, driver):
        """处理Cloudflare挑战"""
        self.logger.info("🛡️ 开始处理Cloudflare挑战...")
        self.session_stats['cloudflare_encounters'] += 1
        
        max_wait_time = self.cf_wait_time
        waited_time = 0
        
        while waited_time < max_wait_time:
            try:
                # 检查当前页面状态
                cf_status = self.detect_cloudflare_status(driver)
                
                if cf_status == 'challenge_page':
                    if waited_time == 0:  # 第一次检测到
                        self.show_cloudflare_instructions()
                        self.session_stats['verification_attempts'] += 1
                    
                    # 尝试自动处理
                    auto_handled = await self.try_auto_handle_cloudflare(driver)
                    
                    if auto_handled:
                        self.logger.info("🤖 尝试自动处理Cloudflare...")
                        await asyncio.sleep(5)  # 等待处理完成
                    
                    # 继续等待
                    await asyncio.sleep(self.cf_check_interval)
                    waited_time += self.cf_check_interval
                    
                    # 显示进度
                    remaining = max_wait_time - waited_time
                    print(f"\r⏰ 等待Cloudflare验证... 剩余: {remaining}秒", end="", flush=True)
                
                elif cf_status == 'checking':
                    self.logger.info("🔄 Cloudflare正在检查...")
                    await asyncio.sleep(self.cf_check_interval)
                    waited_time += self.cf_check_interval
                    print(f"\r🔄 Cloudflare检查中... 剩余: {max_wait_time - waited_time}秒", end="", flush=True)
                
                elif cf_status == 'passed':
                    print("\n✅ Cloudflare验证通过！")
                    self.logger.info("✅ Cloudflare验证通过")
                    self.session_stats['successful_bypasses'] += 1
                    
                    # 等待页面完全加载
                    await asyncio.sleep(random.uniform(3, 6))
                    return True
                
                elif cf_status == 'blocked':
                    print("\n❌ 被Cloudflare阻止")
                    self.logger.error("❌ 被Cloudflare阻止")
                    return False
                
                elif cf_status == 'normal_page':
                    print("\n✅ 已在正常页面")
                    self.logger.info("✅ 已在正常页面")
                    return True
                
                else:
                    # 未知状态，继续等待
                    await asyncio.sleep(self.cf_check_interval)
                    waited_time += self.cf_check_interval
                
            except Exception as e:
                self.logger.error(f"处理Cloudflare挑战时出错: {e}")
                await asyncio.sleep(self.cf_check_interval)
                waited_time += self.cf_check_interval
        
        print(f"\n⏰ Cloudflare验证等待超时 ({max_wait_time}秒)")
        self.logger.warning(f"Cloudflare验证等待超时")
        return False
    
    def detect_cloudflare_status(self, driver):
        """检测Cloudflare状态"""
        try:
            current_url = driver.current_url.lower()
            page_source = driver.page_source.lower()
            page_title = driver.title.lower()
            
            # 检查是否是Cloudflare挑战页面
            cf_indicators = [
                'cloudflare',
                '请完成以下操作',
                '验证您是真人',
                'checking your browser',
                'ddos protection',
                'ray id'
            ]
            
            if any(indicator in page_source or indicator in page_title for indicator in cf_indicators):
                # 进一步判断具体状态
                if '正在检查' in page_source or 'checking' in page_source:
                    return 'checking'
                elif '验证您是真人' in page_source or 'verify you are human' in page_source:
                    return 'challenge_page'
                elif 'blocked' in page_source or '被阻止' in page_source:
                    return 'blocked'
                else:
                    return 'challenge_page'
            
            # 检查是否已经到达目标页面
            elif 'forexfactory.com' in current_url and ('trades' in current_url or 'forum' in current_url):
                return 'normal_page'
            
            # 检查是否通过验证
            elif 'forexfactory.com' in current_url:
                return 'passed'
            
            else:
                return 'unknown'
                
        except Exception as e:
            self.logger.error(f"检测Cloudflare状态出错: {e}")
            return 'unknown'
    
    def show_cloudflare_instructions(self):
        """显示Cloudflare处理指导"""
        print("\n" + "="*80)
        print("🛡️ 检测到Cloudflare安全验证")
        print("="*80)
        print("📋 这是ForexFactory的安全保护机制，请按以下步骤操作:")
        print()
        print("🔸 如果看到复选框 '确认您是真人':")
        print("   1. 点击复选框")
        print("   2. 等待绿色勾号出现")
        print("   3. 如果出现图片验证，请完成")
        print()
        print("🔸 如果看到 '正在检查您的浏览器':")
        print("   1. 请耐心等待（通常5-15秒）")
        print("   2. 不要刷新页面")
        print("   3. 不要关闭浏览器")
        print()
        print("🔸 如果页面一直在加载:")
        print("   1. 等待自动跳转")
        print("   2. 可能需要1-2分钟")
        print("   3. 系统会自动检测完成状态")
        print()
        print("💡 提示:")
        print("   - Cloudflare验证是正常的安全机制")
        print("   - 验证通过后会自动跳转到目标页面")
        print("   - 如果多次失败，可能需要更换网络环境")
        print("="*80)
    
    async def try_auto_handle_cloudflare(self, driver):
        """尝试自动处理Cloudflare"""
        try:
            # 查找并点击验证复选框
            checkbox_selectors = [
                'input[type="checkbox"]',
                '.cf-turnstile',
                '#challenge-form input',
                'iframe[src*="challenges.cloudflare.com"]'
            ]
            
            for selector in checkbox_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        self.logger.info(f"🤖 找到验证元素: {selector}")
                        
                        # 如果是iframe，需要切换到iframe
                        if 'iframe' in selector:
                            driver.switch_to.frame(elements[0])
                            # 在iframe中查找复选框
                            checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                            if checkbox and checkbox.is_enabled():
                                checkbox.click()
                                self.logger.info("🤖 自动点击了iframe中的验证复选框")
                            driver.switch_to.default_content()
                        else:
                            # 直接点击复选框
                            element = elements[0]
                            if element.is_enabled() and element.is_displayed():
                                driver.execute_script("arguments[0].click();", element)
                                self.logger.info("🤖 自动点击了验证复选框")
                        
                        return True
                        
                except Exception as e:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"自动处理Cloudflare失败: {e}")
            return False
    
    async def extract_page_data(self, driver):
        """提取页面数据"""
        self.logger.info("🔍 开始提取页面数据...")
        
        try:
            # 等待页面完全加载
            WebDriverWait(driver, 20).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            # 保存页面源码
            page_source = driver.page_source
            with open('logs/ff_final_page.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            # 使用BeautifulSoup解析
            soup = BeautifulSoup(page_source, 'html.parser')
            
            extracted_data = []
            
            # 提取页面基本信息
            title = soup.find('title')
            if title:
                extracted_data.append({
                    'type': 'page_info',
                    'title': title.get_text().strip(),
                    'url': driver.current_url,
                    'extraction_time': datetime.now().isoformat()
                })
            
            # 查找交易员相关链接
            user_links = soup.find_all('a', href=lambda x: x and ('member' in x or 'showthread' in x))
            for link in user_links[:10]:
                href = link.get('href', '')
                text = link.get_text().strip()
                if text and len(text) > 2:
                    extracted_data.append({
                        'type': 'trader_link',
                        'username': text,
                        'profile_url': href,
                        'extraction_time': datetime.now().isoformat()
                    })
            
            # 查找表格数据
            tables = soup.find_all('table')
            for table in tables[:3]:  # 限制表格数量
                rows = table.find_all('tr')
                for row in rows[:5]:  # 每个表格限制行数
                    cells = row.find_all(['td', 'th'])
                    if len(cells) > 1:
                        row_data = [cell.get_text().strip() for cell in cells]
                        if any(len(cell) > 0 for cell in row_data):
                            extracted_data.append({
                                'type': 'table_data',
                                'content': row_data,
                                'extraction_time': datetime.now().isoformat()
                            })
            
            # 查找包含交易关键词的文本
            trading_keywords = ['pips', 'profit', 'loss', 'trade', 'buy', 'sell', 'EUR', 'USD', 'GBP']
            all_text = soup.get_text()
            lines = all_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if len(line) > 20 and any(keyword.lower() in line.lower() for keyword in trading_keywords):
                    extracted_data.append({
                        'type': 'trading_content',
                        'content': line[:200],
                        'extraction_time': datetime.now().isoformat()
                    })
                    
                    if len([item for item in extracted_data if item['type'] == 'trading_content']) >= 5:
                        break
            
            self.session_stats['data_extracted'] = len(extracted_data)
            self.logger.info(f"✅ 提取完成: {len(extracted_data)} 条数据")
            
            return extracted_data
            
        except Exception as e:
            self.logger.error(f"提取页面数据失败: {e}")
            return []
    
    def save_results(self, data):
        """保存结果"""
        try:
            Path("data").mkdir(exist_ok=True)
            
            # 保存提取的数据
            with open('data/ff_cloudflare_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # 保存会话统计
            with open('logs/ff_cloudflare_stats.json', 'w', encoding='utf-8') as f:
                json.dump(self.session_stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 结果保存完成")
            
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    async def run_cloudflare_hunt(self):
        """运行Cloudflare猎取"""
        self.logger.info("🚀 启动ForexFactory Cloudflare猎取...")
        
        driver = self.create_cloudflare_resistant_driver()
        if not driver:
            print("❌ 无法创建浏览器驱动")
            return
        
        try:
            # 访问目标页面
            self.logger.info(f"🌐 访问: {self.trades_url}")
            driver.get(self.trades_url)
            
            # 处理Cloudflare挑战
            cf_passed = await self.handle_cloudflare_challenge(driver)
            
            if cf_passed:
                # 提取数据
                extracted_data = await self.extract_page_data(driver)
                
                if extracted_data:
                    self.captured_data = extracted_data
                    self.save_results(extracted_data)
                    self.display_success_results()
                else:
                    print("⚠️  Cloudflare验证通过但未提取到数据")
                    self.display_partial_success()
            else:
                print("❌ Cloudflare验证未通过")
                self.display_failure_results()
        
        except Exception as e:
            self.logger.error(f"运行过程出错: {e}")
            print(f"❌ 运行异常: {e}")
        
        finally:
            try:
                driver.quit()
            except:
                pass
    
    def display_success_results(self):
        """显示成功结果"""
        print(f"\n{'='*60}")
        print(f"🎉 ForexFactory Cloudflare猎取成功！")
        print(f"{'='*60}")
        print(f"📊 会话统计:")
        print(f"   Cloudflare遭遇: {self.session_stats['cloudflare_encounters']}")
        print(f"   验证尝试: {self.session_stats['verification_attempts']}")
        print(f"   成功绕过: {self.session_stats['successful_bypasses']}")
        print(f"   提取数据: {self.session_stats['data_extracted']} 条")
        
        if self.captured_data:
            print(f"\n🔍 数据类型分布:")
            type_counts = {}
            for item in self.captured_data:
                item_type = item.get('type', 'unknown')
                type_counts[item_type] = type_counts.get(item_type, 0) + 1
            
            for data_type, count in type_counts.items():
                print(f"   {data_type}: {count} 条")
        
        print(f"\n📁 文件保存:")
        print(f"   数据文件: data/ff_cloudflare_data.json")
        print(f"   页面源码: logs/ff_final_page.html")
        print(f"   统计信息: logs/ff_cloudflare_stats.json")
        print(f"   日志文件: logs/ff_cloudflare_hunter.log")
        print(f"{'='*60}")
    
    def display_partial_success(self):
        """显示部分成功结果"""
        print(f"\n{'='*60}")
        print(f"⚠️  ForexFactory访问成功，数据提取不完整")
        print(f"{'='*60}")
        print(f"✅ Cloudflare验证已通过")
        print(f"⚠️  可能原因:")
        print(f"   - 页面结构发生变化")
        print(f"   - 需要登录才能查看完整内容")
        print(f"   - 页面加载不完整")
        print(f"\n💡 建议:")
        print(f"   - 查看保存的页面源码: logs/ff_final_page.html")
        print(f"   - 检查是否需要登录ForexFactory账户")
        print(f"   - 尝试访问其他页面")
        print(f"{'='*60}")
    
    def display_failure_results(self):
        """显示失败结果"""
        print(f"\n{'='*60}")
        print(f"❌ ForexFactory Cloudflare验证失败")
        print(f"{'='*60}")
        print(f"📊 尝试统计:")
        print(f"   Cloudflare遭遇: {self.session_stats['cloudflare_encounters']}")
        print(f"   验证尝试: {self.session_stats['verification_attempts']}")
        
        print(f"\n💡 可能原因:")
        print(f"   1. 网络环境被Cloudflare识别为可疑")
        print(f"   2. IP地址被临时限制")
        print(f"   3. 浏览器特征被检测")
        print(f"   4. 验证超时")
        
        print(f"\n🔧 建议解决方案:")
        print(f"   1. 更换网络环境（如使用VPN）")
        print(f"   2. 稍后重试（等待IP限制解除）")
        print(f"   3. 使用真实浏览器手动访问一次")
        print(f"   4. 检查系统时间是否正确")
        print(f"{'='*60}")


async def main():
    """主函数"""
    print("🛡️ ForexFactory Cloudflare专用猎手")
    print("="*50)
    print("🎯 专门处理Cloudflare安全验证")
    print("🤖 智能检测和自动处理")
    print("⏰ 最长等待5分钟完成验证")
    print("="*50)
    
    hunter = FFCloudflareHunter()
    
    print("\n🚀 开始Cloudflare猎取...")
    print("💡 如果遇到验证，请按提示操作")
    print("⏰ 整个过程可能需要2-10分钟")
    
    try:
        await hunter.run_cloudflare_hunt()
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
