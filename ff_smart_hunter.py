#!/usr/bin/env python3
"""
ForexFactory智能猎手 - 专门处理验证和等待的优化版本
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from bs4 import BeautifulSoup


class FFSmartHunter:
    """ForexFactory智能猎手 - 专门处理验证问题"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.base_url = "https://www.forexfactory.com"
        self.trades_url = "https://www.forexfactory.com/trades"
        
        # 智能重试配置
        self.max_retries = 3
        self.retry_delay = 30  # 重试间隔30秒
        
        # 数据存储
        self.captured_data = []
        self.session_stats = {
            'attempts': 0,
            'successful_loads': 0,
            'verification_encounters': 0,
            'data_extracted': 0
        }
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('FFSmartHunter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/ff_smart_hunter.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def create_smart_driver(self):
        """创建智能Chrome驱动"""
        try:
            options = Options()
            
            # 基础设置
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 用户代理
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
            ]
            options.add_argument(f'--user-agent={random.choice(user_agents)}')
            
            # 其他设置
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')
            options.add_argument('--disable-extensions')
            
            # 窗口大小
            options.add_argument('--window-size=1920,1080')
            
            driver = webdriver.Chrome(options=options)
            
            # 反检测脚本
            driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
                window.chrome = {runtime: {}};
            """)
            
            return driver
            
        except Exception as e:
            self.logger.error(f"创建驱动失败: {e}")
            return None
    
    async def smart_page_load(self, driver, url, max_wait_minutes=5):
        """智能页面加载 - 处理验证和等待"""
        self.logger.info(f"🌐 智能加载页面: {url}")
        self.session_stats['attempts'] += 1
        
        try:
            # 加载页面
            driver.get(url)
            
            # 第一阶段：检测验证
            verification_handled = await self.handle_verification_smart(driver, max_wait_minutes)
            
            if not verification_handled:
                self.logger.warning("⚠️  验证处理可能未完成")
            
            # 第二阶段：等待内容加载
            content_loaded = await self.wait_for_content_smart(driver)
            
            if content_loaded:
                self.session_stats['successful_loads'] += 1
                self.logger.info("✅ 页面智能加载成功")
                return True
            else:
                self.logger.warning("⚠️  内容加载可能不完整")
                return False
                
        except Exception as e:
            self.logger.error(f"智能页面加载失败: {e}")
            return False
    
    async def handle_verification_smart(self, driver, max_wait_minutes=5):
        """智能处理验证"""
        max_wait_seconds = max_wait_minutes * 60
        check_interval = 3
        total_waited = 0
        
        self.logger.info("🤖 开始智能验证检测...")
        
        while total_waited < max_wait_seconds:
            try:
                # 检测验证状态
                verification_status = self.detect_verification_status(driver)
                
                if verification_status == 'none':
                    self.logger.info("✅ 未检测到验证，继续执行")
                    return True
                
                elif verification_status == 'active':
                    if total_waited == 0:  # 第一次检测到验证
                        self.session_stats['verification_encounters'] += 1
                        self.show_verification_instructions()
                    
                    # 等待用户完成验证
                    await asyncio.sleep(check_interval)
                    total_waited += check_interval
                    
                    # 显示进度
                    remaining_minutes = (max_wait_seconds - total_waited) // 60
                    remaining_seconds = (max_wait_seconds - total_waited) % 60
                    print(f"\r⏰ 等待验证完成... 剩余时间: {remaining_minutes:02d}:{remaining_seconds:02d}", end="", flush=True)
                
                elif verification_status == 'completed':
                    print("\n✅ 验证完成！")
                    self.logger.info("✅ 验证完成，等待页面稳定...")
                    await asyncio.sleep(random.uniform(3, 6))
                    return True
                
                elif verification_status == 'failed':
                    self.logger.warning("❌ 验证可能失败，尝试继续...")
                    return False
                
            except Exception as e:
                self.logger.error(f"验证检测出错: {e}")
                await asyncio.sleep(check_interval)
                total_waited += check_interval
        
        print(f"\n⏰ 验证等待超时 ({max_wait_minutes}分钟)")
        self.logger.warning(f"验证等待超时: {max_wait_minutes}分钟")
        return False
    
    def detect_verification_status(self, driver):
        """检测验证状态"""
        try:
            page_source = driver.page_source.lower()
            page_title = driver.title.lower()
            current_url = driver.current_url.lower()
            
            # 验证关键词
            verification_keywords = [
                'verification', 'captcha', 'challenge', 'robot', 'human', 
                'cloudflare', 'checking', 'security', 'please wait'
            ]
            
            # 完成关键词
            completion_keywords = [
                'trades', 'forum', 'calendar', 'news', 'market'
            ]
            
            # 检查是否在验证中
            if any(keyword in page_source or keyword in page_title for keyword in verification_keywords):
                # 进一步检查是否是活跃验证
                if 'please wait' in page_source or 'checking' in page_source:
                    return 'active'
                elif 'challenge' in page_source or 'captcha' in page_source:
                    return 'active'
                else:
                    return 'active'  # 默认认为是活跃验证
            
            # 检查是否已完成
            elif any(keyword in page_source or keyword in page_title for keyword in completion_keywords):
                return 'completed'
            
            # 检查URL是否正确
            elif 'forexfactory.com' in current_url and 'trades' in current_url:
                return 'none'  # 在正确页面，无验证
            
            else:
                return 'none'  # 默认无验证
                
        except Exception as e:
            self.logger.error(f"检测验证状态出错: {e}")
            return 'none'
    
    def show_verification_instructions(self):
        """显示验证指导"""
        print("\n" + "="*70)
        print("🤖 检测到ForexFactory机器人验证")
        print("="*70)
        print("📋 请按以下步骤完成验证:")
        print("   1. 在浏览器窗口中找到验证框")
        print("   2. 点击 'I'm not a robot' 或类似按钮")
        print("   3. 如果出现图片验证，请完成拼图或选择")
        print("   4. 等待验证通过，页面会自动跳转")
        print("   5. 不要关闭浏览器窗口")
        print("="*70)
        print("💡 提示:")
        print("   - 验证通常需要10-30秒")
        print("   - 如果验证失败，会自动重新出现")
        print("   - 系统会自动检测验证完成状态")
        print("="*70)
    
    async def wait_for_content_smart(self, driver):
        """智能等待内容加载"""
        self.logger.info("⏰ 智能等待内容加载...")
        
        # 多阶段等待策略
        wait_stages = [
            {
                'name': '基础页面结构',
                'selectors': ['body', 'html', '#content', '.content', 'main'],
                'timeout': 10
            },
            {
                'name': '导航和头部',
                'selectors': ['nav', 'header', '.navbar', '.menu', '.navigation'],
                'timeout': 15
            },
            {
                'name': '主要内容区域',
                'selectors': ['table', '.forumline', '.trades', '.trade-list'],
                'timeout': 20
            },
            {
                'name': '交易员相关元素',
                'selectors': ['.trader', '.trade-row', 'a[href*="member"]', 'a[href*="showthread"]'],
                'timeout': 25
            }
        ]
        
        for stage in wait_stages:
            self.logger.info(f"⏰ 等待阶段: {stage['name']}")
            
            stage_success = False
            for selector in stage['selectors']:
                try:
                    WebDriverWait(driver, stage['timeout']).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    self.logger.info(f"✅ 找到 {stage['name']} 元素: {selector}")
                    stage_success = True
                    break
                except TimeoutException:
                    continue
                except Exception as e:
                    continue
            
            if stage_success:
                # 每个阶段成功后短暂等待
                await asyncio.sleep(random.uniform(1, 3))
            else:
                self.logger.warning(f"⚠️  {stage['name']} 阶段未完全加载")
        
        # 最终等待页面稳定
        self.logger.info("⏰ 最终等待页面稳定...")
        await asyncio.sleep(random.uniform(3, 6))
        
        # 检查页面是否有实际内容
        try:
            page_text = driver.find_element(By.TAG_NAME, "body").text
            if len(page_text) > 100:  # 页面有足够内容
                self.logger.info("✅ 页面内容检查通过")
                return True
            else:
                self.logger.warning("⚠️  页面内容较少，可能加载不完整")
                return False
        except:
            self.logger.warning("⚠️  无法检查页面内容")
            return False
    
    async def extract_data_smart(self, driver):
        """智能提取数据"""
        self.logger.info("🔍 开始智能数据提取...")
        
        extracted_data = []
        
        try:
            # 保存页面源码用于分析
            page_source = driver.page_source
            with open('logs/ff_current_page.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            # 使用BeautifulSoup分析
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 多种数据提取策略
            extraction_strategies = [
                self.extract_from_tables,
                self.extract_from_links,
                self.extract_from_text_patterns,
                self.extract_from_any_content
            ]
            
            for i, strategy in enumerate(extraction_strategies, 1):
                self.logger.info(f"🔍 尝试提取策略 {i}")
                try:
                    strategy_data = strategy(soup, driver)
                    if strategy_data:
                        extracted_data.extend(strategy_data)
                        self.logger.info(f"✅ 策略 {i} 提取到 {len(strategy_data)} 条数据")
                    else:
                        self.logger.info(f"⚠️  策略 {i} 未提取到数据")
                except Exception as e:
                    self.logger.error(f"❌ 策略 {i} 执行失败: {e}")
            
            # 去重
            unique_data = self.deduplicate_data(extracted_data)
            self.session_stats['data_extracted'] = len(unique_data)
            
            self.logger.info(f"✅ 智能提取完成: {len(unique_data)} 条唯一数据")
            return unique_data
            
        except Exception as e:
            self.logger.error(f"智能数据提取失败: {e}")
            return []
    
    def extract_from_tables(self, soup, driver):
        """从表格提取数据"""
        data = []
        tables = soup.find_all('table')
        
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) > 1:
                    row_text = ' '.join([cell.get_text().strip() for cell in cells])
                    if len(row_text) > 10:  # 有意义的内容
                        data.append({
                            'type': 'table_row',
                            'content': row_text,
                            'extraction_time': datetime.now().isoformat()
                        })
        
        return data[:20]  # 限制数量
    
    def extract_from_links(self, soup, driver):
        """从链接提取数据"""
        data = []
        
        # 查找用户相关链接
        user_links = soup.find_all('a', href=lambda x: x and ('member' in x or 'showthread' in x))
        
        for link in user_links[:15]:  # 限制数量
            href = link.get('href', '')
            text = link.get_text().strip()
            
            if text and len(text) > 2:
                data.append({
                    'type': 'user_link',
                    'username': text,
                    'profile_url': href,
                    'extraction_time': datetime.now().isoformat()
                })
        
        return data
    
    def extract_from_text_patterns(self, soup, driver):
        """从文本模式提取数据"""
        data = []
        
        # 查找包含交易相关关键词的文本
        trading_keywords = ['pips', 'profit', 'loss', 'trade', 'buy', 'sell', 'EUR', 'USD', 'GBP']
        
        all_text = soup.get_text()
        lines = all_text.split('\n')
        
        for line in lines:
            line = line.strip()
            if len(line) > 20 and any(keyword.lower() in line.lower() for keyword in trading_keywords):
                data.append({
                    'type': 'trading_text',
                    'content': line[:200],  # 限制长度
                    'extraction_time': datetime.now().isoformat()
                })
                
                if len(data) >= 10:  # 限制数量
                    break
        
        return data
    
    def extract_from_any_content(self, soup, driver):
        """从任何内容提取数据"""
        data = []
        
        # 提取页面基本信息
        title = soup.find('title')
        if title:
            data.append({
                'type': 'page_info',
                'title': title.get_text().strip(),
                'url': driver.current_url,
                'extraction_time': datetime.now().isoformat()
            })
        
        # 提取所有可见文本的摘要
        visible_text = soup.get_text()
        if len(visible_text) > 100:
            data.append({
                'type': 'page_summary',
                'content': visible_text[:500],  # 前500个字符
                'total_length': len(visible_text),
                'extraction_time': datetime.now().isoformat()
            })
        
        return data
    
    def deduplicate_data(self, data_list):
        """去重数据"""
        seen = set()
        unique_data = []
        
        for item in data_list:
            # 创建唯一标识
            if item.get('username'):
                identifier = f"user_{item['username']}"
            elif item.get('content'):
                identifier = f"content_{hash(item['content'][:50])}"
            else:
                identifier = f"item_{hash(str(item))}"
            
            if identifier not in seen:
                seen.add(identifier)
                unique_data.append(item)
        
        return unique_data
    
    def save_extracted_data(self, data):
        """保存提取的数据"""
        if not data:
            return
        
        try:
            Path("data").mkdir(exist_ok=True)
            Path("logs").mkdir(exist_ok=True)
            
            # 保存详细数据
            with open('data/ff_smart_extracted.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # 保存会话统计
            with open('logs/ff_smart_stats.json', 'w', encoding='utf-8') as f:
                json.dump(self.session_stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 数据保存完成: {len(data)} 条记录")
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
    
    async def run_smart_hunt(self):
        """运行智能猎取"""
        self.logger.info("🚀 启动ForexFactory智能猎取...")
        
        for attempt in range(self.max_retries):
            self.logger.info(f"🎯 尝试 {attempt + 1}/{self.max_retries}")
            
            driver = self.create_smart_driver()
            if not driver:
                self.logger.error("❌ 无法创建驱动，跳过此次尝试")
                continue
            
            try:
                # 智能页面加载
                load_success = await self.smart_page_load(driver, self.trades_url, max_wait_minutes=5)
                
                if load_success:
                    # 智能数据提取
                    extracted_data = await self.extract_data_smart(driver)
                    
                    if extracted_data:
                        self.captured_data.extend(extracted_data)
                        self.logger.info(f"✅ 尝试 {attempt + 1} 成功: 提取 {len(extracted_data)} 条数据")
                        break
                    else:
                        self.logger.warning(f"⚠️  尝试 {attempt + 1} 页面加载成功但未提取到数据")
                else:
                    self.logger.warning(f"⚠️  尝试 {attempt + 1} 页面加载失败")
                
            except Exception as e:
                self.logger.error(f"❌ 尝试 {attempt + 1} 出现异常: {e}")
            
            finally:
                try:
                    driver.quit()
                except:
                    pass
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries - 1:
                self.logger.info(f"⏰ 等待 {self.retry_delay} 秒后重试...")
                await asyncio.sleep(self.retry_delay)
        
        # 保存结果
        if self.captured_data:
            self.save_extracted_data(self.captured_data)
            self.display_results()
        else:
            self.logger.warning("❌ 所有尝试都失败，未获取到数据")
            self.display_failure_info()
    
    def display_results(self):
        """显示结果"""
        print(f"\n{'='*60}")
        print(f"🎉 ForexFactory智能猎取完成！")
        print(f"{'='*60}")
        print(f"📊 会话统计:")
        print(f"   尝试次数: {self.session_stats['attempts']}")
        print(f"   成功加载: {self.session_stats['successful_loads']}")
        print(f"   遇到验证: {self.session_stats['verification_encounters']}")
        print(f"   提取数据: {self.session_stats['data_extracted']} 条")
        
        if self.captured_data:
            print(f"\n🔍 数据类型分布:")
            type_counts = {}
            for item in self.captured_data:
                item_type = item.get('type', 'unknown')
                type_counts[item_type] = type_counts.get(item_type, 0) + 1
            
            for data_type, count in type_counts.items():
                print(f"   {data_type}: {count} 条")
            
            print(f"\n📁 数据文件:")
            print(f"   详细数据: data/ff_smart_extracted.json")
            print(f"   页面源码: logs/ff_current_page.html")
            print(f"   会话统计: logs/ff_smart_stats.json")
        
        print(f"{'='*60}")
    
    def display_failure_info(self):
        """显示失败信息"""
        print(f"\n{'='*60}")
        print(f"❌ ForexFactory智能猎取未成功")
        print(f"{'='*60}")
        print(f"📊 尝试统计:")
        print(f"   总尝试次数: {self.session_stats['attempts']}")
        print(f"   遇到验证次数: {self.session_stats['verification_encounters']}")
        
        print(f"\n💡 可能的原因:")
        print(f"   1. 网络连接问题")
        print(f"   2. ForexFactory网站结构变化")
        print(f"   3. 验证机制更新")
        print(f"   4. Chrome驱动兼容性问题")
        
        print(f"\n🔧 建议解决方案:")
        print(f"   1. 检查网络连接")
        print(f"   2. 更新Chrome浏览器和驱动")
        print(f"   3. 稍后重试")
        print(f"   4. 查看日志文件: logs/ff_smart_hunter.log")
        print(f"{'='*60}")


async def main():
    """主函数"""
    print("🎯 ForexFactory智能猎手")
    print("="*50)
    print("🤖 专门处理机器人验证和页面等待")
    print("⏰ 智能等待和重试机制")
    print("🔍 多策略数据提取")
    print("="*50)
    
    hunter = FFSmartHunter()
    
    print("\n🚀 开始智能猎取...")
    print("💡 如果遇到验证，请按提示完成")
    print("⏰ 整个过程可能需要5-15分钟")
    
    try:
        await hunter.run_smart_hunt()
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
