#!/usr/bin/env python3
"""
增强版监控面板 - 确保数据正确显示
"""

from flask import Flask, render_template, jsonify, send_from_directory
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
import csv

app = Flask(__name__)

class DataManager:
    """数据管理器"""

    def __init__(self):
        self.ensure_directories()

    def ensure_directories(self):
        """确保目录存在"""
        directories = ["MQL4/Files", "logs", "static", "templates"]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    def get_account_info(self):
        """获取账户信息"""
        account_files = [
            "MQL4/Files/test_account_info.txt",
            "MQL4/Files/premium_account_info.txt",
            "MQL4/Files/account_info.txt"
        ]

        for account_file in account_files:
            path = Path(account_file)
            if path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    account_data = {}
                    for line in lines:
                        if ':' in line:
                            key, value = line.strip().split(':', 1)
                            account_data[key.strip()] = value.strip()

                    # 添加数据源信息
                    account_data['data_source'] = account_file
                    account_data['last_update'] = datetime.fromtimestamp(path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')

                    return account_data
                except Exception as e:
                    continue

        # 返回默认数据
        return {
            'Account': '演示账户',
            'Balance': '10000.00',
            'Equity': '10000.00',
            'Margin': '0.00',
            'Free Margin': '10000.00',
            'Today PnL': '0.00',
            'Today Trades': '0',
            'Open Positions': '0',
            'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_source': '默认数据',
            'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def get_signals(self):
        """获取信号数据"""
        signal_files = [
            ("logs/real_signals.json", "真实信号"),
            ("logs/premium_signals.json", "付费信号"),
            ("logs/validated_signals.json", "测试信号")
        ]

        all_signals = []

        for signal_file, file_type in signal_files:
            path = Path(signal_file)
            if path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                signal = json.loads(line.strip())
                                signal['file_source'] = signal_file
                                signal['signal_type'] = file_type
                                signal['is_real'] = signal.get('is_real', file_type == '真实信号')
                                all_signals.append(signal)
                            except:
                                continue
                except Exception as e:
                    continue

        # 按时间排序，最新的在前
        all_signals.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

        return all_signals[:50]  # 返回最近50个信号

    def get_positions(self):
        """获取持仓数据"""
        # 模拟持仓数据
        positions = [
            {
                'symbol': 'EURUSD',
                'type': 'BUY',
                'volume': 0.01,
                'price_open': 1.0850,
                'price_current': 1.0865,
                'profit': 15.00,
                'time': '2024-01-15 10:30:00'
            },
            {
                'symbol': 'GBPUSD',
                'type': 'SELL',
                'volume': 0.02,
                'price_open': 1.2500,
                'price_current': 1.2485,
                'profit': 30.00,
                'time': '2024-01-15 11:15:00'
            }
        ]

        return positions

    def get_system_status(self):
        """获取系统状态"""
        status_files = [
            "MQL4/Files/premium_status.txt",
            "MQL4/Files/trading_status.txt"
        ]

        for status_file in status_files:
            path = Path(status_file)
            if path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        status = f.read().strip()
                    return {
                        'status': status,
                        'timestamp': datetime.fromtimestamp(path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'file_source': status_file
                    }
                except:
                    continue

        return {
            'status': '系统运行正常',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'file_source': '默认状态'
        }

    def get_statistics(self):
        """获取统计信息"""
        signals = self.get_signals()

        if not signals:
            return {
                'total_signals': 0,
                'today_signals': 0,
                'avg_confidence': 0,
                'sources': {},
                'symbols': {}
            }

        # 统计今日信号
        today = datetime.now().date()
        today_signals = [s for s in signals if s.get('timestamp', '').startswith(today.isoformat())]

        # 统计信号源
        sources = {}
        symbols = {}
        confidences = []

        for signal in signals:
            source = signal.get('source', 'Unknown')
            symbol = signal.get('symbol', 'Unknown')
            confidence = signal.get('confidence', 0)

            sources[source] = sources.get(source, 0) + 1
            symbols[symbol] = symbols.get(symbol, 0) + 1
            if confidence > 0:
                confidences.append(confidence)

        avg_confidence = sum(confidences) / len(confidences) if confidences else 0

        return {
            'total_signals': len(signals),
            'today_signals': len(today_signals),
            'avg_confidence': round(avg_confidence * 100, 1),
            'sources': dict(list(sources.items())[:5]),
            'symbols': dict(list(symbols.items())[:5])
        }

# 创建数据管理器实例
data_manager = DataManager()

@app.route('/')
def dashboard():
    """主页"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT4智能跟单系统 - 增强版监控面板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; min-height: 100vh; padding: 20px;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .status-indicator { display: inline-block; padding: 5px 15px; border-radius: 20px; font-size: 0.9em; margin: 5px; }
        .status-online { background: rgba(0,255,136,0.2); border: 1px solid #00ff88; }
        .status-offline { background: rgba(255,71,87,0.2); border: 1px solid #ff4757; }

        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card {
            background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px;
            backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card h3 { margin-bottom: 20px; color: #ffd700; font-size: 1.3em; }

        .metric { text-align: center; }
        .metric-value {
            font-size: 2.8em; font-weight: bold; margin-bottom: 10px;
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px rgba(0,255,136,0.3);
        }
        .metric-label { color: #ccc; font-size: 1.1em; }
        .metric-small { font-size: 0.9em; color: #aaa; margin-top: 5px; }

        .signal-item {
            background: rgba(255,255,255,0.05); padding: 15px; margin: 10px 0;
            border-radius: 10px; border-left: 4px solid #00ff88;
            transition: background 0.3s ease;
        }
        .signal-item:hover { background: rgba(255,255,255,0.1); }
        .signal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
        .signal-symbol { font-weight: bold; font-size: 1.2em; color: #00ff88; }
        .signal-action { padding: 3px 8px; border-radius: 5px; font-size: 0.9em; }
        .action-buy { background: rgba(0,255,136,0.2); color: #00ff88; }
        .action-sell { background: rgba(255,71,87,0.2); color: #ff4757; }
        .signal-confidence { font-size: 0.9em; color: #ffd700; }
        .signal-source { font-size: 0.8em; color: #aaa; }
        .signal-time { font-size: 0.8em; color: #aaa; }
        .signal-type-real { background: rgba(0,255,136,0.2); color: #00ff88; padding: 2px 6px; border-radius: 3px; font-size: 0.7em; }
        .signal-type-test { background: rgba(255,193,7,0.2); color: #ffc107; padding: 2px 6px; border-radius: 3px; font-size: 0.7em; }
        .signal-type-premium { background: rgba(138,43,226,0.2); color: #8a2be2; padding: 2px 6px; border-radius: 3px; font-size: 0.7em; }

        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.1); }
        th { background: rgba(255,255,255,0.1); font-weight: bold; color: #ffd700; }
        .profit-positive { color: #00ff88; font-weight: bold; }
        .profit-negative { color: #ff4757; font-weight: bold; }

        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
        .stat-item { text-align: center; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 10px; }
        .stat-value { font-size: 1.8em; font-weight: bold; color: #00d4ff; }
        .stat-label { font-size: 0.9em; color: #ccc; margin-top: 5px; }

        .refresh-btn {
            position: fixed; bottom: 30px; right: 30px;
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            border: none; border-radius: 50%; width: 60px; height: 60px;
            color: white; font-size: 1.5em; cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,255,136,0.3);
            transition: transform 0.3s ease;
        }
        .refresh-btn:hover { transform: scale(1.1); }

        .loading { opacity: 0.6; }
        .error { color: #ff4757; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MT4智能跟单系统</h1>
            <div>
                <span class="status-indicator status-online" id="system-status">系统在线</span>
                <span class="status-indicator" id="data-status">数据加载中...</span>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>💰 账户信息</h3>
                <div class="metric">
                    <div class="metric-value" id="balance">--</div>
                    <div class="metric-label">账户余额</div>
                    <div class="metric-small" id="account-source">数据源: --</div>
                </div>
            </div>

            <div class="card">
                <h3>📈 今日盈亏</h3>
                <div class="metric">
                    <div class="metric-value" id="daily-pnl">--</div>
                    <div class="metric-label">当日收益</div>
                    <div class="metric-small" id="daily-trades">交易次数: --</div>
                </div>
            </div>

            <div class="card">
                <h3>🎯 信号质量</h3>
                <div class="metric">
                    <div class="metric-value" id="signal-quality">--</div>
                    <div class="metric-label">平均置信度</div>
                    <div class="metric-small" id="signal-count">信号数量: --</div>
                </div>
            </div>

            <div class="card">
                <h3>📊 系统统计</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-signals">--</div>
                        <div class="stat-label">总信号</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="today-signals">--</div>
                        <div class="stat-label">今日信号</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="open-positions">--</div>
                        <div class="stat-label">持仓数</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>🔥 最新信号</h3>
            <div id="signals-container">
                <div class="signal-item">
                    <div class="signal-header">
                        <span class="signal-symbol">加载中...</span>
                        <span class="signal-confidence">--</span>
                    </div>
                    <div class="signal-source">正在获取信号数据...</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📈 当前持仓</h3>
            <table>
                <thead>
                    <tr><th>品种</th><th>方向</th><th>手数</th><th>开仓价</th><th>当前价</th><th>盈亏</th><th>时间</th></tr>
                </thead>
                <tbody id="positions-body">
                    <tr><td colspan="7" style="text-align: center; color: #aaa;">加载持仓数据...</td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <button class="refresh-btn" onclick="refreshAllData()" title="刷新数据">🔄</button>

    <script>
        let isLoading = false;

        function updateStatus(message, isError = false) {
            const statusEl = document.getElementById('data-status');
            statusEl.textContent = message;
            statusEl.className = 'status-indicator ' + (isError ? 'status-offline' : 'status-online');
        }

        function formatCurrency(value) {
            const num = parseFloat(value) || 0;
            return '$' + num.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
        }

        function formatTime(timestamp) {
            try {
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN');
            } catch {
                return timestamp;
            }
        }

        async function updateAccountInfo() {
            try {
                const response = await fetch('/api/account_info');
                const data = await response.json();

                if (data.error) {
                    document.getElementById('balance').textContent = 'Error';
                    document.getElementById('account-source').textContent = '数据源: ' + data.error;
                    return;
                }

                document.getElementById('balance').textContent = formatCurrency(data.Balance);
                document.getElementById('daily-pnl').textContent = formatCurrency(data['Today PnL']);
                document.getElementById('daily-trades').textContent = '交易次数: ' + (data['Today Trades'] || '0');
                document.getElementById('open-positions').textContent = data['Open Positions'] || '0';
                document.getElementById('account-source').textContent = '数据源: ' + (data.data_source || 'Unknown');

                // 设置盈亏颜色
                const pnl = parseFloat(data['Today PnL']) || 0;
                const pnlEl = document.getElementById('daily-pnl');
                pnlEl.className = 'metric-value ' + (pnl >= 0 ? 'profit-positive' : 'profit-negative');

            } catch (error) {
                console.error('获取账户信息失败:', error);
                updateStatus('账户数据获取失败', true);
            }
        }

        async function updateSignals() {
            try {
                const response = await fetch('/api/signals');
                const signals = await response.json();

                const container = document.getElementById('signals-container');

                if (!signals || signals.length === 0) {
                    container.innerHTML = '<div class="signal-item"><div class="signal-source">暂无信号数据</div></div>';
                    return;
                }

                container.innerHTML = '';

                signals.slice(0, 10).forEach(signal => {
                    const div = document.createElement('div');
                    div.className = 'signal-item';

                    const actionClass = signal.action === 'BUY' ? 'action-buy' : 'action-sell';
                    const confidence = Math.round((signal.confidence || 0) * 100);

                    // 确定信号类型样式
                    let typeClass = 'signal-type-test';
                    let typeText = '测试';
                    if (signal.is_real === true || signal.signal_type === '真实信号') {
                        typeClass = 'signal-type-real';
                        typeText = '真实';
                    } else if (signal.signal_type === '付费信号') {
                        typeClass = 'signal-type-premium';
                        typeText = '付费';
                    }

                    div.innerHTML = `
                        <div class="signal-header">
                            <span class="signal-symbol">${signal.symbol || 'N/A'}</span>
                            <span class="signal-action ${actionClass}">${signal.action || 'N/A'}</span>
                            <span class="${typeClass}">${typeText}</span>
                            <span class="signal-confidence">置信度: ${confidence}%</span>
                        </div>
                        <div class="signal-source">来源: ${signal.source || 'Unknown'}</div>
                        <div class="signal-time">时间: ${formatTime(signal.timestamp)}</div>
                        ${signal.text || signal.title ? '<div style="font-size: 0.9em; color: #ccc; margin-top: 5px;">' + (signal.text || signal.title || '').substring(0, 100) + '...</div>' : ''}
                    `;

                    container.appendChild(div);
                });

            } catch (error) {
                console.error('获取信号失败:', error);
                document.getElementById('signals-container').innerHTML =
                    '<div class="signal-item error">信号数据获取失败</div>';
            }
        }

        async function updateStatistics() {
            try {
                const response = await fetch('/api/statistics');
                const stats = await response.json();

                document.getElementById('total-signals').textContent = stats.total_signals || '0';
                document.getElementById('today-signals').textContent = stats.today_signals || '0';
                document.getElementById('signal-quality').textContent = (stats.avg_confidence || 0) + '%';
                document.getElementById('signal-count').textContent = '信号数量: ' + (stats.total_signals || '0');

            } catch (error) {
                console.error('获取统计信息失败:', error);
            }
        }

        async function updatePositions() {
            try {
                const response = await fetch('/api/positions');
                const positions = await response.json();

                const tbody = document.getElementById('positions-body');

                if (!positions || positions.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #aaa;">暂无持仓</td></tr>';
                    return;
                }

                tbody.innerHTML = '';

                positions.forEach(pos => {
                    const row = tbody.insertRow();
                    const profitClass = pos.profit >= 0 ? 'profit-positive' : 'profit-negative';

                    row.innerHTML = `
                        <td>${pos.symbol}</td>
                        <td><span class="signal-action ${pos.type === 'BUY' ? 'action-buy' : 'action-sell'}">${pos.type}</span></td>
                        <td>${pos.volume}</td>
                        <td>${pos.price_open}</td>
                        <td>${pos.price_current}</td>
                        <td class="${profitClass}">${formatCurrency(pos.profit)}</td>
                        <td>${formatTime(pos.time)}</td>
                    `;
                });

            } catch (error) {
                console.error('获取持仓失败:', error);
            }
        }

        async function refreshAllData() {
            if (isLoading) return;

            isLoading = true;
            updateStatus('刷新数据中...');
            document.body.classList.add('loading');

            try {
                await Promise.all([
                    updateAccountInfo(),
                    updateSignals(),
                    updateStatistics(),
                    updatePositions()
                ]);

                updateStatus('数据更新完成');

            } catch (error) {
                console.error('刷新数据失败:', error);
                updateStatus('数据刷新失败', true);
            } finally {
                isLoading = false;
                document.body.classList.remove('loading');
            }
        }

        // 页面加载完成后立即刷新数据
        document.addEventListener('DOMContentLoaded', function() {
            refreshAllData();

            // 每30秒自动刷新一次
            setInterval(refreshAllData, 30000);
        });
    </script>
</body>
</html>
    '''

@app.route('/api/account_info')
def get_account_info():
    """获取账户信息API"""
    return jsonify(data_manager.get_account_info())

@app.route('/api/signals')
def get_signals():
    """获取信号API"""
    return jsonify(data_manager.get_signals())

@app.route('/api/positions')
def get_positions():
    """获取持仓API"""
    return jsonify(data_manager.get_positions())

@app.route('/api/statistics')
def get_statistics():
    """获取统计信息API"""
    return jsonify(data_manager.get_statistics())

@app.route('/api/system_status')
def get_system_status():
    """获取系统状态API"""
    return jsonify(data_manager.get_system_status())

if __name__ == '__main__':
    print("🌐 启动增强版监控面板...")
    print("📊 确保数据正确显示")
    print("🔗 访问地址: http://localhost:8080")
    print("💡 提示: 如果数据为空，请先运行 python signal_validator.py 生成测试数据")

    app.run(host='localhost', port=8080, debug=False)
