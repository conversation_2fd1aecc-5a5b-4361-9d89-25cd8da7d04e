#!/usr/bin/env python3
"""
信号验证器 - 验证捕获信号的真实性和历史一致性
"""

import asyncio
import json
import logging
import requests
from datetime import datetime, timedelta
from pathlib import Path
from bs4 import BeautifulSoup
import re
from typing import List, Dict, Optional


class SignalValidator:
    """信号验证器"""

    def __init__(self):
        self.logger = self.setup_logger()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        # 已知的测试信号源
        self.test_sources = [
            {
                'name': '免费外汇信号测试',
                'url': 'https://t.me/s/forexsignalsfree',
                'type': 'telegram_public'
            },
            {
                'name': 'TradingView外汇观点',
                'url': 'https://cn.tradingview.com/markets/currencies/ideas/',
                'type': 'tradingview'
            },
            {
                'name': 'Investing.com信号',
                'url': 'https://cn.investing.com/analysis/forex',
                'type': 'investing'
            }
        ]

    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('SignalValidator')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    async def validate_signal_sources(self):
        """验证信号源并获取真实信号"""
        print("🔍 开始验证信号源真实性...")

        all_signals = []

        for source in self.test_sources:
            print(f"\n📡 测试信号源: {source['name']}")

            try:
                if source['type'] == 'telegram_public':
                    signals = await self.get_telegram_signals(source)
                elif source['type'] == 'tradingview':
                    signals = await self.get_tradingview_signals(source)
                elif source['type'] == 'investing':
                    signals = await self.get_investing_signals(source)
                else:
                    continue

                if signals:
                    print(f"✅ 成功获取 {len(signals)} 个信号")
                    all_signals.extend(signals)

                    # 显示前3个信号作为示例
                    print("📊 信号示例:")
                    for i, signal in enumerate(signals[:3], 1):
                        print(f"  {i}. {signal.get('symbol', 'N/A')} {signal.get('action', 'N/A')} - {signal.get('source', 'N/A')}")
                        print(f"     内容: {signal.get('text', '')[:100]}...")
                else:
                    print("❌ 未获取到信号")

            except Exception as e:
                print(f"❌ 获取失败: {e}")

        # 保存验证结果
        if all_signals:
            await self.save_validation_results(all_signals)
            print(f"\n✅ 总共验证获取 {len(all_signals)} 个真实信号")
            return all_signals
        else:
            print("\n❌ 未获取到任何真实信号")
            return []

    async def get_telegram_signals(self, source):
        """获取Telegram公开频道信号"""
        signals = []

        try:
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找消息内容
            messages = soup.find_all('div', class_='tgme_widget_message_text')

            for msg in messages[-10:]:  # 最近10条消息
                text = msg.get_text().strip()
                if len(text) > 20:  # 过滤太短的消息
                    signal = self.parse_signal_text(text, source['name'])
                    if signal:
                        signals.append(signal)

        except Exception as e:
            self.logger.error(f"获取Telegram信号失败: {e}")

        return signals

    async def get_tradingview_signals(self, source):
        """获取TradingView信号"""
        signals = []

        try:
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找观点内容
            ideas = soup.find_all('div', class_='tv-widget-idea__title-row')

            for idea in ideas[:5]:  # 前5个观点
                try:
                    title_elem = idea.find('a')
                    if title_elem:
                        title = title_elem.get_text().strip()
                        signal = self.parse_signal_text(title, source['name'])
                        if signal:
                            signals.append(signal)
                except:
                    continue

        except Exception as e:
            self.logger.error(f"获取TradingView信号失败: {e}")

        return signals

    async def get_investing_signals(self, source):
        """获取Investing.com信号"""
        signals = []

        try:
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找分析文章
            articles = soup.find_all('div', class_='textDiv')

            for article in articles[:5]:  # 前5篇文章
                try:
                    title_elem = article.find('a')
                    if title_elem:
                        title = title_elem.get_text().strip()
                        signal = self.parse_signal_text(title, source['name'])
                        if signal:
                            signals.append(signal)
                except:
                    continue

        except Exception as e:
            self.logger.error(f"获取Investing信号失败: {e}")

        return signals

    def parse_signal_text(self, text, source):
        """解析信号文本"""
        try:
            # 增强的信号解析模式
            symbol_patterns = [
                r'\b(EUR/USD|EURUSD|欧美|欧元美元)\b',
                r'\b(GBP/USD|GBPUSD|镑美|英镑美元)\b',
                r'\b(USD/JPY|USDJPY|美日|美元日元)\b',
                r'\b(USD/CHF|USDCHF|美瑞|美元瑞郎)\b',
                r'\b(AUD/USD|AUDUSD|澳美|澳元美元)\b',
                r'\b(USD/CAD|USDCAD|美加|美元加元)\b',
                r'\b(NZD/USD|NZDUSD|纽美|纽元美元)\b',
                r'\b(XAU/USD|XAUUSD|黄金|GOLD)\b',
                r'\b(XAG/USD|XAGUSD|白银|SILVER)\b'
            ]

            action_patterns = [
                r'(?i)\b(BUY|LONG|买入|做多|看涨|上涨|bullish)\b',
                r'(?i)\b(SELL|SHORT|卖出|做空|看跌|下跌|bearish)\b'
            ]

            # 提取交易品种
            symbol = None
            for pattern in symbol_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    symbol = match.group().replace('/', '').upper()
                    # 标准化符号
                    symbol_map = {
                        '欧美': 'EURUSD', '欧元美元': 'EURUSD',
                        '镑美': 'GBPUSD', '英镑美元': 'GBPUSD',
                        '美日': 'USDJPY', '美元日元': 'USDJPY',
                        '美瑞': 'USDCHF', '美元瑞郎': 'USDCHF',
                        '澳美': 'AUDUSD', '澳元美元': 'AUDUSD',
                        '美加': 'USDCAD', '美元加元': 'USDCAD',
                        '纽美': 'NZDUSD', '纽元美元': 'NZDUSD',
                        '黄金': 'XAUUSD', 'GOLD': 'XAUUSD',
                        '白银': 'XAGUSD', 'SILVER': 'XAGUSD'
                    }
                    symbol = symbol_map.get(symbol, symbol)
                    break

            if not symbol:
                return None

            # 提取交易方向
            action = None
            for pattern in action_patterns:
                match = re.search(pattern, text)
                if match:
                    action_text = match.group().upper()
                    if any(word in action_text for word in ['BUY', 'LONG', '买入', '做多', '看涨', '上涨', 'BULLISH']):
                        action = 'BUY'
                    elif any(word in action_text for word in ['SELL', 'SHORT', '卖出', '做空', '看跌', '下跌', 'BEARISH']):
                        action = 'SELL'
                    break

            if not action:
                return None

            # 提取价格信息
            price_patterns = [
                r'(?i)(?:price|价格|@|at).*?(\d+\.?\d*)',
                r'(?i)(?:target|目标|tp).*?(\d+\.?\d*)',
                r'(?i)(?:stop|止损|sl).*?(\d+\.?\d*)'
            ]

            prices = {}
            for pattern in price_patterns:
                match = re.search(pattern, text)
                if match:
                    try:
                        price = float(match.group(1))
                        if 'target' in pattern or 'tp' in pattern or '目标' in pattern:
                            prices['take_profit'] = price
                        elif 'stop' in pattern or 'sl' in pattern or '止损' in pattern:
                            prices['stop_loss'] = price
                        else:
                            prices['entry_price'] = price
                    except ValueError:
                        continue

            # 计算置信度
            confidence = 0.5
            if prices.get('entry_price'):
                confidence += 0.1
            if prices.get('stop_loss'):
                confidence += 0.1
            if prices.get('take_profit'):
                confidence += 0.1
            if len(text) > 50:
                confidence += 0.1

            return {
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'entry_price': prices.get('entry_price'),
                'stop_loss': prices.get('stop_loss'),
                'take_profit': prices.get('take_profit'),
                'confidence': min(1.0, confidence),
                'text': text[:200],  # 保留前200个字符
                'validated': True
            }

        except Exception as e:
            self.logger.error(f"解析信号失败: {e}")
            return None

    async def save_validation_results(self, signals):
        """保存验证结果"""
        try:
            # 创建目录
            Path("logs").mkdir(exist_ok=True)
            Path("MQL4/Files").mkdir(parents=True, exist_ok=True)

            # 保存详细JSON格式
            with open('logs/validated_signals.json', 'w', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False) + '\n')

            # 保存CSV格式供MT4使用
            import csv
            with open('MQL4/Files/validated_signals.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                for signal in signals:
                    writer.writerow([
                        signal['timestamp'],
                        signal['symbol'],
                        signal['action'],
                        signal.get('entry_price', 0),
                        signal.get('stop_loss', 0),
                        signal.get('take_profit', 0),
                        0.01,  # 默认手数
                        'VALIDATED'  # 状态
                    ])

            # 创建测试账户信息
            with open('MQL4/Files/test_account_info.txt', 'w', encoding='utf-8') as f:
                f.write(f"Account: ********\n")
                f.write(f"Balance: 10000.00\n")
                f.write(f"Equity: 10000.00\n")
                f.write(f"Margin: 0.00\n")
                f.write(f"Free Margin: 10000.00\n")
                f.write(f"Today PnL: 0.00\n")
                f.write(f"Today Trades: 0\n")
                f.write(f"Open Positions: 0\n")
                f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            print(f"✅ 验证结果已保存到:")
            print(f"   - logs/validated_signals.json (详细信息)")
            print(f"   - MQL4/Files/validated_signals.csv (MT4格式)")
            print(f"   - MQL4/Files/test_account_info.txt (测试账户)")

        except Exception as e:
            self.logger.error(f"保存验证结果失败: {e}")

    async def check_historical_consistency(self):
        """检查历史信号一致性"""
        print("\n🔍 检查历史信号一致性...")

        # 检查是否有历史数据
        history_files = [
            'logs/validated_signals.json',
            'logs/premium_signals.json',
            'logs/trading.log'
        ]

        found_history = False

        for file_path in history_files:
            path = Path(file_path)
            if path.exists():
                print(f"✅ 找到历史文件: {file_path}")

                try:
                    if file_path.endswith('.json'):
                        signals = []
                        with open(path, 'r', encoding='utf-8') as f:
                            for line in f:
                                try:
                                    signal = json.loads(line.strip())
                                    signals.append(signal)
                                except:
                                    continue

                        if signals:
                            print(f"   📊 包含 {len(signals)} 个历史信号")

                            # 分析信号质量
                            symbols = {}
                            actions = {}
                            sources = {}

                            for signal in signals:
                                symbol = signal.get('symbol', 'Unknown')
                                action = signal.get('action', 'Unknown')
                                source = signal.get('source', 'Unknown')

                                symbols[symbol] = symbols.get(symbol, 0) + 1
                                actions[action] = actions.get(action, 0) + 1
                                sources[source] = sources.get(source, 0) + 1

                            print(f"   📈 交易品种分布: {dict(list(symbols.items())[:5])}")
                            print(f"   📊 交易方向分布: {actions}")
                            print(f"   📡 信号源分布: {dict(list(sources.items())[:3])}")

                            found_history = True

                except Exception as e:
                    print(f"   ❌ 读取失败: {e}")
            else:
                print(f"❌ 未找到: {file_path}")

        if not found_history:
            print("⚠️  未找到历史信号数据，建议先运行信号验证")

        return found_history

    async def generate_test_data(self):
        """生成测试数据"""
        print("\n🧪 生成测试数据...")

        # 生成模拟信号数据
        test_signals = [
            {
                'timestamp': (datetime.now() - timedelta(minutes=30)).isoformat(),
                'source': '测试信号源1',
                'symbol': 'EURUSD',
                'action': 'BUY',
                'entry_price': 1.0850,
                'stop_loss': 1.0800,
                'take_profit': 1.0950,
                'confidence': 0.85,
                'text': 'EURUSD BUY @ 1.0850, SL: 1.0800, TP: 1.0950',
                'validated': True
            },
            {
                'timestamp': (datetime.now() - timedelta(minutes=25)).isoformat(),
                'source': '测试信号源2',
                'symbol': 'GBPUSD',
                'action': 'SELL',
                'entry_price': 1.2500,
                'stop_loss': 1.2550,
                'take_profit': 1.2400,
                'confidence': 0.78,
                'text': 'GBPUSD SELL @ 1.2500, SL: 1.2550, TP: 1.2400',
                'validated': True
            },
            {
                'timestamp': (datetime.now() - timedelta(minutes=20)).isoformat(),
                'source': '测试信号源3',
                'symbol': 'XAUUSD',
                'action': 'BUY',
                'entry_price': 1950.00,
                'stop_loss': 1940.00,
                'take_profit': 1970.00,
                'confidence': 0.92,
                'text': '黄金 买入 @ 1950, 止损 1940, 止盈 1970',
                'validated': True
            }
        ]

        await self.save_validation_results(test_signals)

        # 生成模拟账户状态
        with open('MQL4/Files/test_account_info.txt', 'w', encoding='utf-8') as f:
            f.write(f"Account: ********\n")
            f.write(f"Balance: 10000.00\n")
            f.write(f"Equity: 10125.50\n")
            f.write(f"Margin: 150.00\n")
            f.write(f"Free Margin: 9975.50\n")
            f.write(f"Today PnL: 125.50\n")
            f.write(f"Today Trades: 3\n")
            f.write(f"Open Positions: 2\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        print("✅ 测试数据生成完成")
        print("💡 现在可以启动监控面板查看数据了")


async def main():
    """主函数"""
    validator = SignalValidator()

    print("信号验证器 - 验证信号真实性")
    print("="*50)

    while True:
        print("\n选择操作:")
        print("1. 验证信号源真实性")
        print("2. 检查历史信号一致性")
        print("3. 生成测试数据")
        print("4. 执行完整验证流程")
        print("0. 退出")

        choice = input("\n请选择 (0-4): ").strip()

        if choice == '0':
            print("再见！")
            break
        elif choice == '1':
            await validator.validate_signal_sources()
        elif choice == '2':
            await validator.check_historical_consistency()
        elif choice == '3':
            await validator.generate_test_data()
        elif choice == '4':
            print("🔄 执行完整验证流程...")
            await validator.validate_signal_sources()
            await validator.check_historical_consistency()
            await validator.generate_test_data()
            print("\n完整验证流程完成！")
        else:
            print("无效选择")

        input("\n按回车键继续...")


if __name__ == "__main__":
    asyncio.run(main())
