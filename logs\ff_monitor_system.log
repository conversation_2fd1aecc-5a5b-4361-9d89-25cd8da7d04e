2025-07-28 13:32:45,919 - INFO - 🌐 创建持久化浏览器会话...
2025-07-28 13:32:48,586 - ERROR - 创建浏览器会话失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xd51af3+62339]
	GetHandleVerifier [0x0xd51b34+62404]
	(No symbol) [0x0xb92123]
	(No symbol) [0x0xbb9693]
	(No symbol) [0x0xbbae20]
	(No symbol) [0x0xbb5f5a]
	(No symbol) [0x0xc09782]
	(No symbol) [0x0xc0926c]
	(No symbol) [0x0xc0a960]
	(No symbol) [0x0xc0a76a]
	(No symbol) [0x0xbff1b6]
	(No symbol) [0x0xbce7a2]
	(No symbol) [0x0xbcf644]
	GetHandleVerifier [0x0xfc6683+2637587]
	GetHandleVerifier [0x0xfc1a8a+2618138]
	GetHandleVerifier [0x0xd7856a+220666]
	GetHandleVerifier [0x0xd68998+156200]
	GetHandleVerifier [0x0xd6f12d+182717]
	GetHandleVerifier [0x0xd59a38+94920]
	GetHandleVerifier [0x0xd59bc2+95314]
	GetHandleVerifier [0x0xd44d0a+9626]
	BaseThreadInitThunk [0x0x765f5d49+25]
	RtlInitializeExceptionChain [0x0x7736d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7736d281+561]

