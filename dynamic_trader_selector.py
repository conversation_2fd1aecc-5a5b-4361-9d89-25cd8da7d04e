#!/usr/bin/env python3
"""
动态交易员筛选器 - 自动发现和评估外汇交易员
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime, timedelta
from pathlib import Path
import re
from collections import defaultdict

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from bs4 import BeautifulSoup
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False


class DynamicTraderSelector:
    """动态交易员筛选器"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.trades_url = "https://www.forexfactory.com/trades"
        
        # 外汇交易对
        self.forex_pairs = {
            'EUR/USD': 'EURUSD',
            'GBP/USD': 'GBPUSD', 
            'USD/CAD': 'USDCAD',
            'USD/CHF': 'USDCHF',
            'USD/JPY': 'USDJPY',
            'AUD/USD': 'AUDUSD',
            'NZD/USD': 'NZDUSD'
        }
        
        # 交易员数据收集
        self.trader_data = defaultdict(lambda: {
            'name': '',
            'forex_trades': [],
            'non_forex_trades': [],
            'total_trades': 0,
            'forex_percentage': 0.0,
            'recent_activity': [],
            'score': 0.0,
            'last_seen': None,
            'win_rate': 0.0,
            'avg_profit': 0.0,
            'trading_frequency': 0.0,
            'is_forex_trader': False
        })
        
        # 筛选标准
        self.selection_criteria = {
            'min_forex_percentage': 70,  # 至少70%的交易是外汇
            'min_total_trades': 5,       # 至少5笔交易
            'min_recent_activity': 3,    # 最近3天有活动
            'min_score': 20.0           # 最低评分20
        }
        
        self.driver = None
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('DynamicTraderSelector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/dynamic_trader_selector.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def create_stealth_driver(self):
        """创建隐身驱动"""
        try:
            self.logger.info("🔍 创建交易员筛选隐身驱动...")
            
            options = uc.ChromeOptions()
            options.add_argument('--no-first-run')
            options.add_argument('--no-service-autorun') 
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--window-size=1366,768')
            
            driver = uc.Chrome(options=options, version_main=None)
            
            # 反检测脚本
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                    Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                    Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en-US', 'en']});
                    window.chrome = {runtime: {}};
                '''
            })
            
            self.logger.info("✅ 交易员筛选隐身驱动创建成功")
            return driver
            
        except Exception as e:
            self.logger.error(f"创建隐身驱动失败: {e}")
            return None
    
    async def initialize_session(self):
        """初始化会话"""
        print("🔍 初始化交易员筛选会话...")
        
        self.driver = self.create_stealth_driver()
        if not self.driver:
            return False
        
        try:
            # 访问主页
            self.driver.get("https://www.forexfactory.com")
            await asyncio.sleep(random.uniform(2, 4))
            
            # 访问交易页面
            self.driver.get(self.trades_url)
            await asyncio.sleep(random.uniform(3, 5))
            
            # 验证页面加载
            page_source = self.driver.page_source.lower()
            if 'trade' in page_source and 'forexfactory' in self.driver.current_url:
                print("✅ 交易员筛选会话初始化成功！")
                return True
            else:
                print("❌ 页面加载异常")
                return False
                
        except Exception as e:
            self.logger.error(f"会话初始化失败: {e}")
            return False
    
    def is_forex_pair(self, symbol):
        """检查是否为外汇交易对"""
        if not symbol:
            return False
        
        for forex_symbol in self.forex_pairs.keys():
            if symbol.upper() == forex_symbol or symbol.upper() == self.forex_pairs[forex_symbol]:
                return True
        
        return False
    
    async def scan_all_traders(self, scan_rounds=10):
        """扫描所有交易员"""
        print(f"🔍 开始扫描交易员 ({scan_rounds} 轮)...")
        
        for round_num in range(1, scan_rounds + 1):
            print(f"\n📊 第 {round_num} 轮扫描...")
            
            try:
                # 刷新页面获取最新数据
                self.driver.refresh()
                await asyncio.sleep(random.uniform(3, 5))
                
                # 检查Cloudflare
                page_source = self.driver.page_source.lower()
                if 'cloudflare' in page_source or '验证您是真人' in page_source:
                    print("⚠️  遇到Cloudflare验证，跳过本轮")
                    continue
                
                # 解析页面数据
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # 查找表格数据
                trades_found = 0
                tables = soup.find_all('table')
                for table in tables:
                    rows = table.find_all('tr')
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 4:
                            content = [cell.get_text().strip() for cell in cells]
                            
                            # 跳过表头
                            if content[0] == 'Trade' or 'Saving' in content[0]:
                                continue
                            
                            # 解析交易信息
                            trade_info = self.parse_trade_info(content)
                            if trade_info:
                                self.collect_trader_data(trade_info)
                                trades_found += 1
                
                print(f"   发现 {trades_found} 笔交易")
                
                # 等待下一轮
                if round_num < scan_rounds:
                    wait_time = random.uniform(30, 60)  # 30-60秒间隔
                    print(f"   等待 {wait_time:.0f} 秒...")
                    await asyncio.sleep(wait_time)
                
            except Exception as e:
                self.logger.error(f"第 {round_num} 轮扫描出错: {e}")
                continue
        
        print(f"\n✅ 扫描完成！发现 {len(self.trader_data)} 个交易员")
    
    def parse_trade_info(self, content):
        """解析交易信息"""
        try:
            trade_text = content[0]
            trader_name = content[1]
            return_text = content[2]
            pips_text = content[3]
            
            # 解析交易品种
            symbol = None
            
            # 检查外汇对
            forex_patterns = [
                r'(EUR/USD)', r'(GBP/USD)', r'(USD/CAD)', 
                r'(USD/CHF)', r'(USD/JPY)', r'(AUD/USD)', r'(NZD/USD)'
            ]
            
            for pattern in forex_patterns:
                match = re.search(pattern, trade_text, re.IGNORECASE)
                if match:
                    symbol = match.group(1).upper()
                    break
            
            # 检查其他品种
            if not symbol:
                other_patterns = [
                    r'(BTC/USD)', r'(ETH/USD)', r'(XAU/USD)', r'(Xauusdc)',
                    r'(WTI)', r'(GOLD)', r'(SILVER)', r'(OIL)'
                ]
                
                for pattern in other_patterns:
                    match = re.search(pattern, trade_text, re.IGNORECASE)
                    if match:
                        symbol = match.group(1).upper()
                        if symbol == 'XAUUSDC':
                            symbol = 'XAUUSD'
                        break
            
            if not symbol:
                return None
            
            # 解析交易方向
            action = None
            if re.search(r'\b(BUY|Long)\b', trade_text, re.IGNORECASE):
                action = 'BUY'
            elif re.search(r'\b(SELL|Short)\b', trade_text, re.IGNORECASE):
                action = 'SELL'
            
            # 解析状态
            status = 'unknown'
            if 'Opened' in trade_text:
                status = 'open'
            elif 'Closed' in trade_text:
                status = 'closed'
            
            # 解析时间
            time_match = re.search(r'(\d+)\s*min\s*ago', trade_text)
            minutes_ago = int(time_match.group(1)) if time_match else 0
            
            # 解析收益
            profit_pct = self.parse_return_value(return_text)
            pips_value = self.parse_pips_value(pips_text)
            
            if symbol and action and trader_name:
                return {
                    'symbol': symbol,
                    'action': action,
                    'trader': trader_name,
                    'status': status,
                    'minutes_ago': minutes_ago,
                    'timestamp': datetime.now() - timedelta(minutes=minutes_ago),
                    'profit_pct': profit_pct,
                    'pips': pips_value,
                    'raw_text': trade_text,
                    'is_forex': self.is_forex_pair(symbol)
                }
        
        except Exception as e:
            pass
        
        return None
    
    def parse_return_value(self, return_text):
        """解析收益百分比"""
        try:
            pct_matches = re.findall(r'([+-]?\d+\.?\d*)%', return_text)
            if pct_matches:
                for pct_str in reversed(pct_matches):
                    pct_value = float(pct_str)
                    if pct_value != 0:
                        return pct_value
                return 0.0
        except:
            pass
        return None
    
    def parse_pips_value(self, pips_text):
        """解析点数"""
        try:
            pip_matches = re.findall(r'([+-]?\d+)', pips_text)
            if pip_matches:
                for pip_str in reversed(pip_matches):
                    pip_value = int(pip_str)
                    if pip_value != 0:
                        return pip_value
                return 0
        except:
            pass
        return None
    
    def collect_trader_data(self, trade_info):
        """收集交易员数据"""
        trader_name = trade_info['trader']
        trader = self.trader_data[trader_name]
        
        # 更新基本信息
        trader['name'] = trader_name
        trader['last_seen'] = datetime.now()
        
        # 添加交易记录
        if trade_info['is_forex']:
            trader['forex_trades'].append(trade_info)
        else:
            trader['non_forex_trades'].append(trade_info)
        
        trader['total_trades'] = len(trader['forex_trades']) + len(trader['non_forex_trades'])
        
        # 计算外汇交易百分比
        if trader['total_trades'] > 0:
            trader['forex_percentage'] = (len(trader['forex_trades']) / trader['total_trades']) * 100
        
        # 添加到最近活动
        if trade_info['minutes_ago'] <= 1440:  # 24小时内
            trader['recent_activity'].append(trade_info)
        
        # 计算表现指标
        self.calculate_trader_performance(trader)
    
    def calculate_trader_performance(self, trader):
        """计算交易员表现"""
        forex_trades = trader['forex_trades']
        
        if not forex_trades:
            trader['score'] = 0.0
            return
        
        # 计算胜率
        closed_trades = [t for t in forex_trades if t['status'] == 'closed']
        if closed_trades:
            profitable_trades = [t for t in closed_trades if t.get('profit_pct', 0) > 0]
            trader['win_rate'] = (len(profitable_trades) / len(closed_trades)) * 100
            
            # 计算平均收益
            profits = [t['profit_pct'] for t in closed_trades if t.get('profit_pct') is not None]
            trader['avg_profit'] = sum(profits) / len(profits) if profits else 0
        
        # 计算交易频率（每天交易数）
        recent_days = 7
        recent_trades = [t for t in forex_trades if t['minutes_ago'] <= recent_days * 1440]
        trader['trading_frequency'] = len(recent_trades) / recent_days
        
        # 计算综合评分
        score = 0
        
        # 外汇专注度 (40%)
        score += (trader['forex_percentage'] / 100) * 40
        
        # 胜率 (30%)
        score += (trader['win_rate'] / 100) * 30
        
        # 交易活跃度 (20%)
        activity_score = min(trader['trading_frequency'] * 10, 20)
        score += activity_score
        
        # 平均收益 (10%)
        if trader['avg_profit'] > 0:
            profit_score = min(trader['avg_profit'] * 2, 10)
            score += profit_score
        
        trader['score'] = round(score, 1)
        
        # 判断是否为外汇交易员
        trader['is_forex_trader'] = (
            trader['forex_percentage'] >= self.selection_criteria['min_forex_percentage'] and
            trader['total_trades'] >= self.selection_criteria['min_total_trades'] and
            len(trader['recent_activity']) >= self.selection_criteria['min_recent_activity'] and
            trader['score'] >= self.selection_criteria['min_score']
        )
    
    def get_top_forex_traders(self, limit=10):
        """获取顶级外汇交易员"""
        # 筛选外汇交易员
        forex_traders = [
            trader for trader in self.trader_data.values() 
            if trader['is_forex_trader']
        ]
        
        # 按评分排序
        forex_traders.sort(key=lambda x: x['score'], reverse=True)
        
        return forex_traders[:limit]
    
    def save_trader_analysis(self):
        """保存交易员分析结果"""
        try:
            Path("data").mkdir(exist_ok=True)
            
            # 保存完整数据
            analysis_data = {
                'scan_time': datetime.now().isoformat(),
                'total_traders': len(self.trader_data),
                'selection_criteria': self.selection_criteria,
                'trader_data': dict(self.trader_data)
            }
            
            with open('data/trader_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)
            
            # 保存顶级外汇交易员
            top_traders = self.get_top_forex_traders()
            
            with open('data/top_forex_traders.json', 'w', encoding='utf-8') as f:
                json.dump(top_traders, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"✅ 交易员分析结果已保存")
            
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {e}")
    
    def display_analysis_results(self):
        """显示分析结果"""
        print(f"\n{'='*80}")
        print(f"🔍 交易员分析结果")
        print(f"{'='*80}")
        
        total_traders = len(self.trader_data)
        forex_traders = [t for t in self.trader_data.values() if t['is_forex_trader']]
        
        print(f"📊 总体统计:")
        print(f"   发现交易员: {total_traders}")
        print(f"   外汇交易员: {len(forex_traders)}")
        print(f"   筛选率: {(len(forex_traders)/total_traders*100):.1f}%" if total_traders > 0 else "   筛选率: 0%")
        
        print(f"\n🏆 顶级外汇交易员:")
        print("-" * 80)
        print(f"{'排名':<4} {'交易员':<15} {'评分':<6} {'外汇%':<8} {'胜率':<8} {'交易数':<8} {'频率'}")
        print("-" * 80)
        
        top_traders = self.get_top_forex_traders()
        for i, trader in enumerate(top_traders, 1):
            print(f"{i:<4} {trader['name'][:14]:<15} {trader['score']:<6} {trader['forex_percentage']:<7.1f}% {trader['win_rate']:<7.1f}% {trader['total_trades']:<8} {trader['trading_frequency']:.1f}/天")
        
        if not top_traders:
            print("   暂无符合条件的外汇交易员")
        
        print(f"{'='*80}")
    
    async def run_trader_selection(self, scan_rounds=10):
        """运行交易员筛选"""
        print("🔍 动态交易员筛选系统")
        print("="*50)
        print("🎯 专注外汇交易员发现")
        print("📊 智能评估和筛选")
        print("🏆 动态排行榜生成")
        print("="*50)
        
        # 初始化会话
        if not await self.initialize_session():
            print("❌ 交易员筛选会话初始化失败")
            return
        
        # 扫描交易员
        await self.scan_all_traders(scan_rounds)
        
        # 显示结果
        self.display_analysis_results()
        
        # 保存结果
        self.save_trader_analysis()
        
        # 生成推荐配置
        self.generate_monitor_config()
        
        # 清理
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
    
    def generate_monitor_config(self):
        """生成监控系统配置"""
        top_traders = self.get_top_forex_traders()
        
        if not top_traders:
            print("⚠️  未找到符合条件的外汇交易员")
            return
        
        # 生成配置
        config = {
            'target_traders': [trader['name'] for trader in top_traders],
            'trader_scores': {trader['name']: trader['score'] for trader in top_traders},
            'update_time': datetime.now().isoformat(),
            'selection_criteria': self.selection_criteria
        }
        
        try:
            with open('data/monitor_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ 监控配置已生成: data/monitor_config.json")
            print(f"🎯 推荐监控交易员: {', '.join(config['target_traders'])}")
            
        except Exception as e:
            self.logger.error(f"生成监控配置失败: {e}")


async def main():
    """主函数"""
    if not SELENIUM_AVAILABLE:
        print("❌ 需要安装依赖:")
        print("pip install undetected-chromedriver selenium beautifulsoup4")
        return
    
    selector = DynamicTraderSelector()
    
    print("🔍 动态交易员筛选配置")
    print("="*50)
    print("📊 筛选标准:")
    print(f"   外汇交易占比: ≥{selector.selection_criteria['min_forex_percentage']}%")
    print(f"   最少交易数: ≥{selector.selection_criteria['min_total_trades']}笔")
    print(f"   最近活跃度: ≥{selector.selection_criteria['min_recent_activity']}天")
    print(f"   最低评分: ≥{selector.selection_criteria['min_score']}")
    
    print("\n🚀 选择扫描轮数:")
    print("1. 快速扫描 (5轮)")
    print("2. 标准扫描 (10轮)")
    print("3. 深度扫描 (20轮)")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("❌ 请输入有效选项 (1-3)")
    
    scan_rounds = {'1': 5, '2': 10, '3': 20}[choice]
    
    print(f"✅ 选择了 {scan_rounds} 轮扫描")
    
    # 运行筛选
    await selector.run_trader_selection(scan_rounds)


if __name__ == "__main__":
    asyncio.run(main())
