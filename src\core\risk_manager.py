"""
风险管理器 - 负责风险控制和资金管理
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import MetaTrader5 as mt5


@dataclass
class RiskMetrics:
    """风险指标"""
    current_risk: float
    daily_risk: float
    total_risk: float
    max_drawdown: float
    win_rate: float
    profit_factor: float


class RiskManager:
    """风险管理器"""
    
    def __init__(self, risk_config: Dict):
        self.config = risk_config
        self.logger = logging.getLogger(__name__)
        
        # 风险限制
        self.max_risk_per_trade = risk_config['account_settings']['max_risk_per_trade']
        self.max_daily_risk = risk_config['account_settings']['max_daily_risk']
        self.max_total_risk = risk_config['account_settings']['max_total_risk']
        self.emergency_stop_loss = risk_config['account_settings']['emergency_stop_loss']
        
        # 交易师限制
        self.max_allocation_per_trader = risk_config['trader_limits']['max_allocation_per_trader']
        self.min_trader_history_days = risk_config['trader_limits']['min_trader_history_days']
        self.min_trader_trades = risk_config['trader_limits']['min_trader_trades']
        self.max_trader_drawdown = risk_config['trader_limits']['max_trader_drawdown']
        
        # 仓位管理
        self.base_lot_size = risk_config['position_sizing']['base_lot_size']
        self.max_lot_size = risk_config['position_sizing']['max_lot_size']
        self.risk_per_trade_percent = risk_config['position_sizing']['risk_per_trade_percent']
        self.use_dynamic_sizing = risk_config['position_sizing']['use_dynamic_sizing']
        
        # 风险统计
        self.daily_trades = 0
        self.daily_risk_used = 0.0
        self.total_risk_used = 0.0
        
    async def check_signal_risk(self, signal) -> bool:
        """检查信号风险是否可接受"""
        try:
            # 检查基本风险限制
            if not await self.check_basic_risk_limits():
                return False
            
            # 检查交易品种风险
            if not await self.check_symbol_risk(signal.symbol):
                return False
            
            # 检查信号质量
            if not self.check_signal_quality(signal):
                return False
            
            # 检查相关性风险
            if not await self.check_correlation_risk(signal.symbol):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查信号风险错误: {e}")
            return False
    
    async def check_basic_risk_limits(self) -> bool:
        """检查基本风险限制"""
        try:
            # 检查当日交易次数
            if self.daily_trades >= 20:  # 每日最大交易次数
                self.logger.warning("⚠️ 已达到每日最大交易次数")
                return False
            
            # 检查当日风险使用
            if self.daily_risk_used >= self.max_daily_risk:
                self.logger.warning("⚠️ 已达到每日最大风险限制")
                return False
            
            # 检查总体风险使用
            if self.total_risk_used >= self.max_total_risk:
                self.logger.warning("⚠️ 已达到总体最大风险限制")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查基本风险限制错误: {e}")
            return False
    
    async def check_symbol_risk(self, symbol: str) -> bool:
        """检查交易品种风险"""
        try:
            # 获取该品种的当前持仓
            positions = mt5.positions_get(symbol=symbol)
            if not positions:
                return True
            
            # 检查同一品种的持仓数量
            if len(positions) >= 3:  # 最多3个同品种持仓
                self.logger.warning(f"⚠️ {symbol}持仓数量过多")
                return False
            
            # 检查同一品种的风险敞口
            total_volume = sum(pos.volume for pos in positions)
            if total_volume >= 1.0:  # 最大1手同品种敞口
                self.logger.warning(f"⚠️ {symbol}风险敞口过大")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查品种风险错误: {e}")
            return True
    
    def check_signal_quality(self, signal) -> bool:
        """检查信号质量"""
        try:
            # 检查置信度
            if signal.confidence < 0.6:
                self.logger.warning(f"⚠️ 信号置信度过低: {signal.confidence}")
                return False
            
            # 检查是否有止损
            if not signal.stop_loss and self.config.get('require_stop_loss', True):
                self.logger.warning("⚠️ 信号缺少止损价格")
                return False
            
            # 检查风险回报比
            if signal.stop_loss and signal.take_profit:
                risk_reward = self.calculate_risk_reward_ratio(signal)
                if risk_reward < 1.0:  # 最小1:1风险回报比
                    self.logger.warning(f"⚠️ 风险回报比过低: {risk_reward}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查信号质量错误: {e}")
            return True
    
    def calculate_risk_reward_ratio(self, signal) -> float:
        """计算风险回报比"""
        try:
            if not signal.entry_price or not signal.stop_loss or not signal.take_profit:
                return 0.0
            
            if signal.action == 'BUY':
                risk = abs(signal.entry_price - signal.stop_loss)
                reward = abs(signal.take_profit - signal.entry_price)
            else:
                risk = abs(signal.stop_loss - signal.entry_price)
                reward = abs(signal.entry_price - signal.take_profit)
            
            return reward / risk if risk > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"计算风险回报比错误: {e}")
            return 0.0
    
    async def check_correlation_risk(self, symbol: str) -> bool:
        """检查相关性风险"""
        try:
            # 获取所有持仓
            positions = mt5.positions_get()
            if not positions:
                return True
            
            # 检查相关品种的持仓
            correlated_symbols = self.get_correlated_symbols(symbol)
            correlated_positions = [pos for pos in positions if pos.symbol in correlated_symbols]
            
            if len(correlated_positions) >= 2:  # 最多2个相关品种持仓
                self.logger.warning(f"⚠️ 相关品种持仓过多: {symbol}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查相关性风险错误: {e}")
            return True
    
    def get_correlated_symbols(self, symbol: str) -> List[str]:
        """获取相关交易品种"""
        correlation_groups = {
            'EUR': ['EURUSD', 'EURGBP', 'EURJPY', 'EURCHF'],
            'GBP': ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPCHF'],
            'JPY': ['USDJPY', 'EURJPY', 'GBPJPY', 'CHFJPY'],
            'USD': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD'],
            'GOLD': ['XAUUSD', 'XAGUSD'],
        }
        
        for currency, symbols in correlation_groups.items():
            if symbol in symbols:
                return [s for s in symbols if s != symbol]
        
        return []
    
    async def calculate_position_size(self, signal, account_balance: float) -> float:
        """计算仓位大小"""
        try:
            if not self.use_dynamic_sizing:
                return self.base_lot_size
            
            # 基于风险百分比计算
            risk_amount = account_balance * (self.risk_per_trade_percent / 100)
            
            # 计算每点价值
            if signal.stop_loss and signal.entry_price:
                if signal.action == 'BUY':
                    risk_pips = abs(signal.entry_price - signal.stop_loss)
                else:
                    risk_pips = abs(signal.stop_loss - signal.entry_price)
                
                # 获取品种信息
                symbol_info = mt5.symbol_info(signal.symbol)
                if symbol_info:
                    pip_value = symbol_info.trade_tick_value
                    position_size = risk_amount / (risk_pips * pip_value)
                    
                    # 限制仓位大小
                    position_size = max(self.base_lot_size, min(self.max_lot_size, position_size))
                    
                    # 根据置信度调整
                    confidence_factor = signal.confidence
                    position_size *= confidence_factor
                    
                    return round(position_size, 2)
            
            return self.base_lot_size
            
        except Exception as e:
            self.logger.error(f"计算仓位大小错误: {e}")
            return self.base_lot_size
    
    async def check_account_risk(self) -> Dict:
        """检查账户风险状态"""
        try:
            account_info = mt5.account_info()
            if not account_info:
                return {'emergency_stop': True, 'warning': True, 'message': 'MT5连接断开'}
            
            # 计算当前风险指标
            balance = account_info.balance
            equity = account_info.equity
            margin_level = account_info.margin_level if account_info.margin_level else 0
            
            # 检查紧急止损条件
            if equity / balance < (1 - self.emergency_stop_loss):
                return {
                    'emergency_stop': True,
                    'warning': True,
                    'message': f'账户亏损超过{self.emergency_stop_loss*100}%'
                }
            
            # 检查保证金水平
            if margin_level > 0 and margin_level < 200:  # 保证金水平低于200%
                return {
                    'emergency_stop': False,
                    'warning': True,
                    'message': f'保证金水平过低: {margin_level}%'
                }
            
            # 检查当日亏损
            daily_pnl = await self.get_daily_pnl()
            if daily_pnl / balance < -self.max_daily_risk:
                return {
                    'emergency_stop': False,
                    'warning': True,
                    'message': f'当日亏损超过限制: {daily_pnl/balance*100:.2f}%'
                }
            
            return {'emergency_stop': False, 'warning': False, 'message': '风险正常'}
            
        except Exception as e:
            self.logger.error(f"检查账户风险错误: {e}")
            return {'emergency_stop': True, 'warning': True, 'message': str(e)}
    
    async def get_daily_pnl(self) -> float:
        """获取当日盈亏"""
        try:
            today = datetime.now().date()
            from_date = datetime.combine(today, datetime.min.time())
            to_date = datetime.combine(today, datetime.max.time())
            
            deals = mt5.history_deals_get(from_date, to_date)
            if not deals:
                return 0.0
            
            return sum(deal.profit for deal in deals)
            
        except Exception as e:
            self.logger.error(f"获取当日盈亏错误: {e}")
            return 0.0
    
    async def get_risk_metrics(self) -> RiskMetrics:
        """获取风险指标"""
        try:
            account_info = mt5.account_info()
            if not account_info:
                return RiskMetrics(0, 0, 0, 0, 0, 0)
            
            balance = account_info.balance
            equity = account_info.equity
            
            # 计算当前风险
            current_risk = (balance - equity) / balance if balance > 0 else 0
            
            # 计算当日风险
            daily_pnl = await self.get_daily_pnl()
            daily_risk = abs(daily_pnl) / balance if balance > 0 else 0
            
            # 计算总体风险（这里简化处理）
            total_risk = current_risk
            
            # 计算最大回撤（需要历史数据）
            max_drawdown = await self.calculate_max_drawdown()
            
            # 计算胜率和盈利因子（需要历史交易数据）
            win_rate, profit_factor = await self.calculate_performance_metrics()
            
            return RiskMetrics(
                current_risk=current_risk,
                daily_risk=daily_risk,
                total_risk=total_risk,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor
            )
            
        except Exception as e:
            self.logger.error(f"获取风险指标错误: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0)
    
    async def calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        try:
            # 获取最近30天的交易历史
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            deals = mt5.history_deals_get(start_date, end_date)
            if not deals:
                return 0.0
            
            # 计算累计盈亏曲线
            cumulative_pnl = []
            running_total = 0
            
            for deal in deals:
                running_total += deal.profit
                cumulative_pnl.append(running_total)
            
            if not cumulative_pnl:
                return 0.0
            
            # 计算最大回撤
            peak = cumulative_pnl[0]
            max_drawdown = 0
            
            for pnl in cumulative_pnl:
                if pnl > peak:
                    peak = pnl
                drawdown = (peak - pnl) / abs(peak) if peak != 0 else 0
                max_drawdown = max(max_drawdown, drawdown)
            
            return max_drawdown
            
        except Exception as e:
            self.logger.error(f"计算最大回撤错误: {e}")
            return 0.0
    
    async def calculate_performance_metrics(self) -> Tuple[float, float]:
        """计算绩效指标"""
        try:
            # 获取最近30天的交易历史
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            deals = mt5.history_deals_get(start_date, end_date)
            if not deals:
                return 0.0, 0.0
            
            # 过滤出平仓交易
            closing_deals = [deal for deal in deals if deal.entry == mt5.DEAL_ENTRY_OUT]
            
            if not closing_deals:
                return 0.0, 0.0
            
            # 计算胜率
            winning_trades = [deal for deal in closing_deals if deal.profit > 0]
            win_rate = len(winning_trades) / len(closing_deals)
            
            # 计算盈利因子
            total_profit = sum(deal.profit for deal in winning_trades)
            losing_trades = [deal for deal in closing_deals if deal.profit < 0]
            total_loss = abs(sum(deal.profit for deal in losing_trades))
            
            profit_factor = total_profit / total_loss if total_loss > 0 else 0
            
            return win_rate, profit_factor
            
        except Exception as e:
            self.logger.error(f"计算绩效指标错误: {e}")
            return 0.0, 0.0
    
    def update_daily_stats(self, trade_result):
        """更新每日统计"""
        if trade_result.success:
            self.daily_trades += 1
            # 这里可以添加更多统计逻辑
