"""
交易引擎 - 负责执行交易信号和管理MT4连接
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import MetaTrader5 as mt5


@dataclass
class TradeResult:
    """交易结果"""
    success: bool
    order_id: Optional[int]
    symbol: str
    action: str
    volume: float
    price: float
    error_message: Optional[str] = None
    timestamp: datetime = None


class TradingEngine:
    """交易引擎"""
    
    def __init__(self, metaapi_config: Dict, trading_config: Dict, db_manager, risk_manager):
        self.metaapi_config = metaapi_config
        self.trading_config = trading_config
        self.db_manager = db_manager
        self.risk_manager = risk_manager
        self.logger = logging.getLogger(__name__)
        
        # MT5连接状态
        self.is_connected = False
        self.account_info = None
        
        # 交易统计
        self.trades_today = 0
        self.daily_pnl = 0.0
        
        # 允许的交易品种
        self.allowed_symbols = set(trading_config.get('allowed_symbols', []))
        
        # 交易设置
        self.max_spread = trading_config.get('max_spread', 5.0)
        self.slippage_tolerance = trading_config.get('slippage_tolerance', 3)
        self.execution_timeout = trading_config.get('execution_timeout', 30)
    
    async def connect(self):
        """连接到MT5"""
        try:
            # 初始化MT5
            if not mt5.initialize():
                raise Exception(f"MT5初始化失败: {mt5.last_error()}")
            
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info is None:
                raise Exception("无法获取账户信息")
            
            self.account_info = account_info._asdict()
            self.is_connected = True
            
            self.logger.info(f"✅ 已连接到MT5账户: {self.account_info['login']}")
            self.logger.info(f"💰 账户余额: {self.account_info['balance']}")
            
        except Exception as e:
            self.logger.error(f"❌ MT5连接失败: {e}")
            raise
    
    async def disconnect(self):
        """断开MT5连接"""
        try:
            mt5.shutdown()
            self.is_connected = False
            self.logger.info("✅ MT5连接已断开")
            
        except Exception as e:
            self.logger.error(f"断开MT5连接错误: {e}")
    
    async def execute_signal(self, signal, position_size: float) -> TradeResult:
        """执行交易信号"""
        try:
            if not self.is_connected:
                return TradeResult(
                    success=False,
                    order_id=None,
                    symbol=signal.symbol,
                    action=signal.action,
                    volume=position_size,
                    price=0.0,
                    error_message="MT5未连接"
                )
            
            # 检查交易品种
            if signal.symbol not in self.allowed_symbols:
                return TradeResult(
                    success=False,
                    order_id=None,
                    symbol=signal.symbol,
                    action=signal.action,
                    volume=position_size,
                    price=0.0,
                    error_message=f"不允许的交易品种: {signal.symbol}"
                )
            
            # 检查市场状态
            if not await self.is_market_open(signal.symbol):
                return TradeResult(
                    success=False,
                    order_id=None,
                    symbol=signal.symbol,
                    action=signal.action,
                    volume=position_size,
                    price=0.0,
                    error_message="市场未开放"
                )
            
            # 检查点差
            if not await self.check_spread(signal.symbol):
                return TradeResult(
                    success=False,
                    order_id=None,
                    symbol=signal.symbol,
                    action=signal.action,
                    volume=position_size,
                    price=0.0,
                    error_message="点差过大"
                )
            
            # 执行交易
            if signal.entry_price:
                # 挂单交易
                result = await self.place_pending_order(signal, position_size)
            else:
                # 市价交易
                result = await self.place_market_order(signal, position_size)
            
            # 记录交易
            if result.success:
                self.trades_today += 1
                await self.db_manager.save_trade({
                    'order_id': result.order_id,
                    'symbol': result.symbol,
                    'action': result.action,
                    'volume': result.volume,
                    'price': result.price,
                    'timestamp': result.timestamp,
                    'signal_source': signal.source,
                    'signal_confidence': signal.confidence
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"执行交易信号错误: {e}")
            return TradeResult(
                success=False,
                order_id=None,
                symbol=signal.symbol,
                action=signal.action,
                volume=position_size,
                price=0.0,
                error_message=str(e)
            )
    
    async def place_market_order(self, signal, volume: float) -> TradeResult:
        """下市价单"""
        try:
            symbol = signal.symbol
            action = mt5.ORDER_TYPE_BUY if signal.action == 'BUY' else mt5.ORDER_TYPE_SELL
            
            # 获取当前价格
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                raise Exception(f"无法获取{symbol}价格")
            
            price = tick.ask if signal.action == 'BUY' else tick.bid
            
            # 准备交易请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": action,
                "price": price,
                "deviation": self.slippage_tolerance,
                "magic": 123456,  # 可以设置为唯一标识
                "comment": f"Signal from {signal.source}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # 添加止损和止盈
            if signal.stop_loss:
                request["sl"] = signal.stop_loss
            
            if signal.take_profit:
                request["tp"] = signal.take_profit
            
            # 发送交易请求
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                raise Exception(f"交易失败: {result.comment}")
            
            self.logger.info(f"✅ 市价单执行成功: {symbol} {signal.action} {volume}")
            
            return TradeResult(
                success=True,
                order_id=result.order,
                symbol=symbol,
                action=signal.action,
                volume=volume,
                price=result.price,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"市价单执行失败: {e}")
            return TradeResult(
                success=False,
                order_id=None,
                symbol=signal.symbol,
                action=signal.action,
                volume=volume,
                price=0.0,
                error_message=str(e)
            )
    
    async def place_pending_order(self, signal, volume: float) -> TradeResult:
        """下挂单"""
        try:
            symbol = signal.symbol
            entry_price = signal.entry_price
            
            # 获取当前价格
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                raise Exception(f"无法获取{symbol}价格")
            
            current_price = tick.ask if signal.action == 'BUY' else tick.bid
            
            # 确定订单类型
            if signal.action == 'BUY':
                if entry_price > current_price:
                    order_type = mt5.ORDER_TYPE_BUY_STOP
                else:
                    order_type = mt5.ORDER_TYPE_BUY_LIMIT
            else:
                if entry_price < current_price:
                    order_type = mt5.ORDER_TYPE_SELL_STOP
                else:
                    order_type = mt5.ORDER_TYPE_SELL_LIMIT
            
            # 准备交易请求
            request = {
                "action": mt5.TRADE_ACTION_PENDING,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "price": entry_price,
                "magic": 123456,
                "comment": f"Pending order from {signal.source}",
                "type_time": mt5.ORDER_TIME_GTC,
            }
            
            # 添加止损和止盈
            if signal.stop_loss:
                request["sl"] = signal.stop_loss
            
            if signal.take_profit:
                request["tp"] = signal.take_profit
            
            # 发送交易请求
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                raise Exception(f"挂单失败: {result.comment}")
            
            self.logger.info(f"✅ 挂单设置成功: {symbol} {signal.action} @ {entry_price}")
            
            return TradeResult(
                success=True,
                order_id=result.order,
                symbol=symbol,
                action=signal.action,
                volume=volume,
                price=entry_price,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"挂单失败: {e}")
            return TradeResult(
                success=False,
                order_id=None,
                symbol=signal.symbol,
                action=signal.action,
                volume=volume,
                price=0.0,
                error_message=str(e)
            )
    
    async def close_position(self, position_id: int) -> bool:
        """关闭持仓"""
        try:
            # 获取持仓信息
            positions = mt5.positions_get(ticket=position_id)
            if not positions:
                return False
            
            position = positions[0]
            
            # 准备平仓请求
            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                "position": position_id,
                "magic": position.magic,
                "comment": "Position closed by system",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # 发送平仓请求
            result = mt5.order_send(close_request)
            
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                self.logger.info(f"✅ 持仓关闭成功: {position_id}")
                return True
            else:
                self.logger.error(f"❌ 持仓关闭失败: {result.comment}")
                return False
                
        except Exception as e:
            self.logger.error(f"关闭持仓错误: {e}")
            return False
    
    async def close_all_positions(self):
        """关闭所有持仓"""
        try:
            positions = mt5.positions_get()
            if not positions:
                return
            
            for position in positions:
                await self.close_position(position.ticket)
            
            self.logger.info(f"✅ 已关闭所有持仓，共{len(positions)}个")
            
        except Exception as e:
            self.logger.error(f"关闭所有持仓错误: {e}")
    
    async def is_market_open(self, symbol: str) -> bool:
        """检查市场是否开放"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return False
            
            # 检查交易时间
            now = datetime.now()
            
            # 简单检查：避开周末
            if now.weekday() >= 5:  # 周六、周日
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查市场状态错误: {e}")
            return False
    
    async def check_spread(self, symbol: str) -> bool:
        """检查点差是否合理"""
        try:
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return False
            
            spread = (tick.ask - tick.bid) / tick.bid * 100000  # 转换为点
            
            return spread <= self.max_spread
            
        except Exception as e:
            self.logger.error(f"检查点差错误: {e}")
            return False
    
    async def get_account_balance(self) -> float:
        """获取账户余额"""
        try:
            account_info = mt5.account_info()
            return account_info.balance if account_info else 0.0
        except Exception as e:
            self.logger.error(f"获取账户余额错误: {e}")
            return 0.0
    
    async def get_open_positions_count(self) -> int:
        """获取持仓数量"""
        try:
            positions = mt5.positions_get()
            return len(positions) if positions else 0
        except Exception as e:
            self.logger.error(f"获取持仓数量错误: {e}")
            return 0
    
    async def get_daily_pnl(self) -> float:
        """获取当日盈亏"""
        try:
            # 获取今日交易历史
            today = datetime.now().date()
            from_date = datetime.combine(today, datetime.min.time())
            to_date = datetime.combine(today, datetime.max.time())
            
            deals = mt5.history_deals_get(from_date, to_date)
            if not deals:
                return 0.0
            
            total_profit = sum(deal.profit for deal in deals)
            return total_profit
            
        except Exception as e:
            self.logger.error(f"获取当日盈亏错误: {e}")
            return 0.0
