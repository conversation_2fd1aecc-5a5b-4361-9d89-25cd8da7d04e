#!/usr/bin/env python3
"""
交易员发现调度器 - 定期自动发现和更新外汇交易员
"""

import asyncio
import json
import logging
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
import threading


class TraderDiscoveryScheduler:
    """交易员发现调度器"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.config_file = Path("data/monitor_config.json")
        self.discovery_log = Path("logs/discovery_schedule.log")
        
        # 调度配置
        self.schedule_config = {
            'daily_discovery': True,      # 每日发现
            'weekly_deep_scan': True,     # 每周深度扫描
            'discovery_time': '02:00',    # 发现时间（凌晨2点）
            'deep_scan_day': 'sunday',    # 深度扫描日（周日）
            'min_traders': 5,             # 最少交易员数量
            'max_traders': 15,            # 最多交易员数量
            'auto_update': True           # 自动更新配置
        }
        
        self.running = False
        self.scheduler_thread = None
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('TraderDiscoveryScheduler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/trader_discovery_scheduler.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def load_current_traders(self):
        """加载当前交易员配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config.get('target_traders', []), config.get('trader_scores', {})
        except Exception as e:
            self.logger.error(f"加载当前配置失败: {e}")
        
        return [], {}
    
    def analyze_trader_performance(self):
        """分析当前交易员表现"""
        try:
            # 加载虚拟账户数据
            accounts_file = Path("logs/virtual_accounts.json")
            if not accounts_file.exists():
                return {}
            
            with open(accounts_file, 'r', encoding='utf-8') as f:
                accounts = json.load(f)
            
            performance = {}
            for trader_name, account in accounts.items():
                performance[trader_name] = {
                    'profit': account.get('total_profit', 0),
                    'win_rate': account.get('win_rate', 0),
                    'total_trades': account.get('total_trades', 0),
                    'equity': account.get('equity', 10000),
                    'max_drawdown': account.get('max_drawdown', 0)
                }
            
            return performance
            
        except Exception as e:
            self.logger.error(f"分析交易员表现失败: {e}")
            return {}
    
    def should_discover_new_traders(self):
        """判断是否需要发现新交易员"""
        current_traders, _ = self.load_current_traders()
        performance = self.analyze_trader_performance()
        
        # 检查交易员数量
        if len(current_traders) < self.schedule_config['min_traders']:
            self.logger.info(f"交易员数量不足 ({len(current_traders)} < {self.schedule_config['min_traders']})")
            return True, "数量不足"
        
        # 检查表现不佳的交易员
        poor_performers = 0
        for trader in current_traders:
            if trader in performance:
                perf = performance[trader]
                # 判断标准：亏损超过5%或胜率低于30%
                if perf['profit'] < -500 or perf['win_rate'] < 30:
                    poor_performers += 1
        
        if poor_performers > len(current_traders) * 0.4:  # 超过40%表现不佳
            self.logger.info(f"表现不佳的交易员过多 ({poor_performers}/{len(current_traders)})")
            return True, "表现不佳"
        
        # 检查配置更新时间
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                update_time = config.get('update_time')
                if update_time:
                    last_update = datetime.fromisoformat(update_time)
                    if datetime.now() - last_update > timedelta(days=7):
                        self.logger.info("配置超过7天未更新")
                        return True, "配置过期"
        except:
            pass
        
        return False, "无需更新"
    
    async def run_discovery(self, scan_type="standard"):
        """运行交易员发现"""
        self.logger.info(f"🔍 开始{scan_type}交易员发现...")
        
        try:
            # 导入发现器
            from dynamic_trader_selector import DynamicTraderSelector
            
            selector = DynamicTraderSelector()
            
            # 根据扫描类型设置轮数
            scan_rounds = {
                'quick': 5,
                'standard': 10,
                'deep': 20
            }.get(scan_type, 10)
            
            # 运行发现
            await selector.run_trader_selection(scan_rounds)
            
            # 检查结果
            if self.config_file.exists():
                self.logger.info("✅ 交易员发现完成，配置已更新")
                
                # 记录发现日志
                self.log_discovery_result(scan_type)
                
                return True
            else:
                self.logger.warning("⚠️  发现完成但未生成配置")
                return False
                
        except Exception as e:
            self.logger.error(f"交易员发现失败: {e}")
            return False
    
    def log_discovery_result(self, scan_type):
        """记录发现结果"""
        try:
            current_traders, trader_scores = self.load_current_traders()
            
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'scan_type': scan_type,
                'traders_found': len(current_traders),
                'traders': current_traders,
                'avg_score': sum(trader_scores.values()) / len(trader_scores) if trader_scores else 0
            }
            
            # 追加到发现日志
            with open(self.discovery_log, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
            
        except Exception as e:
            self.logger.error(f"记录发现结果失败: {e}")
    
    def daily_discovery_job(self):
        """每日发现任务"""
        self.logger.info("🌅 执行每日交易员发现检查...")
        
        should_discover, reason = self.should_discover_new_traders()
        
        if should_discover:
            self.logger.info(f"需要发现新交易员: {reason}")
            
            # 在新线程中运行异步发现
            def run_async_discovery():
                asyncio.run(self.run_discovery("standard"))
            
            discovery_thread = threading.Thread(target=run_async_discovery)
            discovery_thread.start()
            discovery_thread.join(timeout=1800)  # 30分钟超时
            
        else:
            self.logger.info(f"无需发现新交易员: {reason}")
    
    def weekly_deep_scan_job(self):
        """每周深度扫描任务"""
        self.logger.info("📊 执行每周深度交易员扫描...")
        
        # 深度扫描总是执行
        def run_async_deep_scan():
            asyncio.run(self.run_discovery("deep"))
        
        scan_thread = threading.Thread(target=run_async_deep_scan)
        scan_thread.start()
        scan_thread.join(timeout=3600)  # 1小时超时
    
    def setup_schedule(self):
        """设置调度任务"""
        if self.schedule_config['daily_discovery']:
            schedule.every().day.at(self.schedule_config['discovery_time']).do(self.daily_discovery_job)
            self.logger.info(f"✅ 已设置每日发现任务: {self.schedule_config['discovery_time']}")
        
        if self.schedule_config['weekly_deep_scan']:
            getattr(schedule.every(), self.schedule_config['deep_scan_day']).at(self.schedule_config['discovery_time']).do(self.weekly_deep_scan_job)
            self.logger.info(f"✅ 已设置每周深度扫描: {self.schedule_config['deep_scan_day']} {self.schedule_config['discovery_time']}")
    
    def run_scheduler(self):
        """运行调度器"""
        self.logger.info("🚀 交易员发现调度器启动")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"调度器运行出错: {e}")
                time.sleep(300)  # 出错后等待5分钟
    
    def start(self):
        """启动调度器"""
        if self.running:
            self.logger.warning("调度器已在运行")
            return
        
        self.running = True
        self.setup_schedule()
        
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("✅ 交易员发现调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("⏹️  交易员发现调度器已停止")
    
    def manual_discovery(self, scan_type="standard"):
        """手动触发发现"""
        self.logger.info(f"🔍 手动触发{scan_type}发现...")
        
        def run_manual_discovery():
            asyncio.run(self.run_discovery(scan_type))
        
        discovery_thread = threading.Thread(target=run_manual_discovery)
        discovery_thread.start()
        
        return discovery_thread
    
    def get_discovery_history(self, days=30):
        """获取发现历史"""
        try:
            if not self.discovery_log.exists():
                return []
            
            history = []
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with open(self.discovery_log, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        entry_date = datetime.fromisoformat(entry['timestamp'])
                        
                        if entry_date >= cutoff_date:
                            history.append(entry)
                    except:
                        continue
            
            return sorted(history, key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"获取发现历史失败: {e}")
            return []
    
    def display_status(self):
        """显示调度器状态"""
        print(f"\n{'='*60}")
        print(f"📅 交易员发现调度器状态")
        print(f"{'='*60}")
        
        print(f"🔄 运行状态: {'✅ 运行中' if self.running else '❌ 已停止'}")
        
        current_traders, trader_scores = self.load_current_traders()
        print(f"👥 当前交易员: {len(current_traders)} 个")
        
        if trader_scores:
            avg_score = sum(trader_scores.values()) / len(trader_scores)
            print(f"📊 平均评分: {avg_score:.1f}")
        
        # 显示调度配置
        print(f"\n⏰ 调度配置:")
        print(f"   每日发现: {'✅' if self.schedule_config['daily_discovery'] else '❌'} ({self.schedule_config['discovery_time']})")
        print(f"   周深度扫描: {'✅' if self.schedule_config['weekly_deep_scan'] else '❌'} ({self.schedule_config['deep_scan_day']})")
        print(f"   交易员范围: {self.schedule_config['min_traders']}-{self.schedule_config['max_traders']} 个")
        
        # 显示最近发现历史
        history = self.get_discovery_history(7)
        if history:
            print(f"\n📋 最近发现记录:")
            for entry in history[:3]:
                timestamp = datetime.fromisoformat(entry['timestamp'])
                print(f"   {timestamp.strftime('%m-%d %H:%M')} - {entry['scan_type']} - 发现{entry['traders_found']}个交易员")
        
        print(f"{'='*60}")


def main():
    """主函数"""
    print("📅 交易员发现调度器")
    print("="*50)
    print("🔍 自动发现和更新外汇交易员")
    print("⏰ 定期执行，保持交易员列表最新")
    print("📊 智能分析，优化交易员配置")
    print("="*50)
    
    scheduler = TraderDiscoveryScheduler()
    
    print("\n🚀 选择操作:")
    print("1. 启动调度器")
    print("2. 查看状态")
    print("3. 手动发现")
    print("4. 查看历史")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            scheduler.start()
            print("✅ 调度器已启动，将在后台运行")
            print("💡 提示：调度器将在每日凌晨2点检查是否需要发现新交易员")
            
            try:
                while True:
                    time.sleep(10)
                    if not scheduler.running:
                        break
            except KeyboardInterrupt:
                print("\n⏹️  停止调度器...")
                scheduler.stop()
                break
                
        elif choice == '2':
            scheduler.display_status()
            
        elif choice == '3':
            print("\n🔍 手动发现选项:")
            print("1. 快速发现 (5轮)")
            print("2. 标准发现 (10轮)")
            print("3. 深度发现 (20轮)")
            
            scan_choice = input("请选择 (1-3): ").strip()
            scan_types = {'1': 'quick', '2': 'standard', '3': 'deep'}
            
            if scan_choice in scan_types:
                thread = scheduler.manual_discovery(scan_types[scan_choice])
                print("🔍 发现任务已启动，请等待...")
                thread.join()
                print("✅ 手动发现完成")
            else:
                print("❌ 无效选择")
                
        elif choice == '4':
            history = scheduler.get_discovery_history()
            if history:
                print(f"\n📋 发现历史 (最近30天):")
                print("-" * 60)
                for entry in history:
                    timestamp = datetime.fromisoformat(entry['timestamp'])
                    print(f"{timestamp.strftime('%Y-%m-%d %H:%M')} - {entry['scan_type']:<8} - {entry['traders_found']}个交易员 - 平均评分{entry['avg_score']:.1f}")
            else:
                print("📋 暂无发现历史")
                
        elif choice == '5':
            if scheduler.running:
                scheduler.stop()
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    # 安装schedule依赖
    try:
        import schedule
    except ImportError:
        print("❌ 需要安装依赖: pip install schedule")
        exit(1)
    
    main()
