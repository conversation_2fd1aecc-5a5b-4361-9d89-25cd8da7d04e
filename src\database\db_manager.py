"""
数据库管理器 - 负责数据存储和查询
"""

import sqlite3
import logging
import json
from datetime import datetime, date
from pathlib import Path
from typing import Dict, List, Any, Optional


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_config: Dict):
        self.config = db_config
        self.logger = logging.getLogger(__name__)
        self.db_path = Path(db_config['path'])
        self.connection = None
        
    async def initialize(self):
        """初始化数据库"""
        try:
            # 创建数据库目录
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 连接数据库
            self.connection = sqlite3.connect(
                self.db_path, 
                check_same_thread=False,
                timeout=30.0
            )
            self.connection.row_factory = sqlite3.Row
            
            # 创建表
            await self.create_tables()
            
            self.logger.info("✅ 数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 数据库初始化失败: {e}")
            raise
    
    async def create_tables(self):
        """创建数据库表"""
        try:
            cursor = self.connection.cursor()
            
            # 交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id INTEGER,
                    symbol TEXT NOT NULL,
                    action TEXT NOT NULL,
                    volume REAL NOT NULL,
                    price REAL NOT NULL,
                    profit REAL DEFAULT 0,
                    timestamp DATETIME NOT NULL,
                    signal_source TEXT,
                    signal_confidence REAL,
                    status TEXT DEFAULT 'open',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 信号记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    action TEXT NOT NULL,
                    entry_price REAL,
                    stop_loss REAL,
                    take_profit REAL,
                    confidence REAL,
                    raw_text TEXT,
                    processed BOOLEAN DEFAULT FALSE,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 系统状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    account_balance REAL,
                    account_equity REAL,
                    open_positions INTEGER,
                    daily_pnl REAL,
                    signals_processed INTEGER,
                    status_data TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 交易师表现表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trader_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trader_id TEXT NOT NULL,
                    source TEXT NOT NULL,
                    total_signals INTEGER DEFAULT 0,
                    successful_signals INTEGER DEFAULT 0,
                    total_profit REAL DEFAULT 0,
                    win_rate REAL DEFAULT 0,
                    max_drawdown REAL DEFAULT 0,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(trader_id, source)
                )
            ''')
            
            # 风险事件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    description TEXT,
                    account_balance REAL,
                    risk_level REAL,
                    action_taken TEXT,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.connection.commit()
            
        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")
            raise
    
    async def save_trade(self, trade_data: Dict):
        """保存交易记录"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                INSERT INTO trades (
                    order_id, symbol, action, volume, price, 
                    timestamp, signal_source, signal_confidence
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_data.get('order_id'),
                trade_data['symbol'],
                trade_data['action'],
                trade_data['volume'],
                trade_data['price'],
                trade_data['timestamp'],
                trade_data.get('signal_source'),
                trade_data.get('signal_confidence')
            ))
            
            self.connection.commit()
            
        except Exception as e:
            self.logger.error(f"保存交易记录失败: {e}")
    
    async def save_signal(self, signal_data: Dict):
        """保存信号记录"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                INSERT INTO signals (
                    source, symbol, action, entry_price, stop_loss,
                    take_profit, confidence, raw_text, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                signal_data['source'],
                signal_data['symbol'],
                signal_data['action'],
                signal_data.get('entry_price'),
                signal_data.get('stop_loss'),
                signal_data.get('take_profit'),
                signal_data.get('confidence'),
                signal_data.get('raw_text'),
                signal_data['timestamp']
            ))
            
            self.connection.commit()
            
        except Exception as e:
            self.logger.error(f"保存信号记录失败: {e}")
    
    async def save_system_status(self, status_data: Dict):
        """保存系统状态"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                INSERT INTO system_status (
                    timestamp, account_balance, account_equity,
                    open_positions, daily_pnl, signals_processed, status_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                status_data['timestamp'],
                status_data.get('account_balance'),
                status_data.get('account_equity'),
                status_data.get('open_positions'),
                status_data.get('daily_pnl'),
                status_data.get('signals_processed'),
                json.dumps(status_data)
            ))
            
            self.connection.commit()
            
        except Exception as e:
            self.logger.error(f"保存系统状态失败: {e}")
    
    async def get_signals_count_by_date(self, target_date: date) -> int:
        """获取指定日期的信号数量"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                SELECT COUNT(*) FROM signals 
                WHERE DATE(timestamp) = ?
            ''', (target_date.isoformat(),))
            
            result = cursor.fetchone()
            return result[0] if result else 0
            
        except Exception as e:
            self.logger.error(f"获取信号数量失败: {e}")
            return 0
    
    async def get_daily_trades(self, target_date: date) -> List[Dict]:
        """获取指定日期的交易记录"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                SELECT * FROM trades 
                WHERE DATE(timestamp) = ?
                ORDER BY timestamp DESC
            ''', (target_date.isoformat(),))
            
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
            
        except Exception as e:
            self.logger.error(f"获取每日交易失败: {e}")
            return []
    
    async def get_trader_performance(self, trader_id: str, source: str) -> Optional[Dict]:
        """获取交易师表现"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                SELECT * FROM trader_performance 
                WHERE trader_id = ? AND source = ?
            ''', (trader_id, source))
            
            row = cursor.fetchone()
            return dict(row) if row else None
            
        except Exception as e:
            self.logger.error(f"获取交易师表现失败: {e}")
            return None
    
    async def update_trader_performance(self, trader_id: str, source: str, performance_data: Dict):
        """更新交易师表现"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO trader_performance (
                    trader_id, source, total_signals, successful_signals,
                    total_profit, win_rate, max_drawdown, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trader_id,
                source,
                performance_data.get('total_signals', 0),
                performance_data.get('successful_signals', 0),
                performance_data.get('total_profit', 0.0),
                performance_data.get('win_rate', 0.0),
                performance_data.get('max_drawdown', 0.0),
                datetime.now()
            ))
            
            self.connection.commit()
            
        except Exception as e:
            self.logger.error(f"更新交易师表现失败: {e}")
    
    async def save_risk_event(self, event_data: Dict):
        """保存风险事件"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
                INSERT INTO risk_events (
                    event_type, severity, description, account_balance,
                    risk_level, action_taken, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                event_data['event_type'],
                event_data['severity'],
                event_data.get('description'),
                event_data.get('account_balance'),
                event_data.get('risk_level'),
                event_data.get('action_taken'),
                event_data['timestamp']
            ))
            
            self.connection.commit()
            
        except Exception as e:
            self.logger.error(f"保存风险事件失败: {e}")
    
    async def get_statistics(self) -> Dict:
        """获取统计信息"""
        try:
            cursor = self.connection.cursor()
            
            # 总交易数
            cursor.execute('SELECT COUNT(*) FROM trades')
            total_trades = cursor.fetchone()[0]
            
            # 总信号数
            cursor.execute('SELECT COUNT(*) FROM signals')
            total_signals = cursor.fetchone()[0]
            
            # 今日交易数
            today = date.today()
            cursor.execute('SELECT COUNT(*) FROM trades WHERE DATE(timestamp) = ?', (today.isoformat(),))
            today_trades = cursor.fetchone()[0]
            
            # 今日信号数
            cursor.execute('SELECT COUNT(*) FROM signals WHERE DATE(timestamp) = ?', (today.isoformat(),))
            today_signals = cursor.fetchone()[0]
            
            return {
                'total_trades': total_trades,
                'total_signals': total_signals,
                'today_trades': today_trades,
                'today_signals': today_signals
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    async def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        try:
            cursor = self.connection.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 清理旧的系统状态记录
            cursor.execute('''
                DELETE FROM system_status 
                WHERE created_at < ?
            ''', (cutoff_date,))
            
            # 清理旧的信号记录（保留交易记录）
            cursor.execute('''
                DELETE FROM signals 
                WHERE created_at < ? AND processed = TRUE
            ''', (cutoff_date,))
            
            self.connection.commit()
            
            self.logger.info(f"✅ 清理了{days}天前的旧数据")
            
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
    
    async def close(self):
        """关闭数据库连接"""
        try:
            if self.connection:
                self.connection.close()
                self.logger.info("✅ 数据库连接已关闭")
                
        except Exception as e:
            self.logger.error(f"关闭数据库连接失败: {e}")
