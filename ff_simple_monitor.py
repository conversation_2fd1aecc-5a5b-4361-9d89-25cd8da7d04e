#!/usr/bin/env python3
"""
ForexFactory简单监控系统 - 稳定版本
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
import time
import re

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from bs4 import BeautifulSoup
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False


class FFSimpleMonitor:
    """ForexFactory简单监控系统"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.trades_url = "https://www.forexfactory.com/trades"
        
        # 持久化浏览器会话
        self.driver = None
        self.session_active = False
        
        # 监控的顶级交易员
        self.target_traders = [
            'fusiongoldfx',    # 评分61.0, 100%胜率
            'theTrip75',       # 评分56.0, 100%胜率  
            'pipclubtrade',    # 评分20, 黄金专家
            'Dominicus',       # BTC交易员
            'geo1683'          # EUR/USD交易员
        ]
        
        # 历史交易记录
        self.last_trades_snapshot = {}
        
        # 监控统计
        self.monitor_stats = {
            'session_start': None,
            'monitoring_cycles': 0,
            'new_trades_detected': 0,
            'signals_generated': 0,
            'cloudflare_encounters': 0,
            'successful_captures': 0
        }
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('FFSimpleMonitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/ff_simple_monitor.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def create_simple_driver(self):
        """创建简单的Chrome驱动"""
        if not SELENIUM_AVAILABLE:
            print("❌ 需要安装依赖: pip install selenium beautifulsoup4")
            return None
        
        try:
            self.logger.info("🌐 创建Chrome浏览器会话...")
            
            options = Options()
            
            # 基础设置
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 窗口设置
            options.add_argument('--window-size=1200,800')
            
            # 用户代理
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 创建驱动
            driver = webdriver.Chrome(options=options)
            
            # 反检测脚本
            driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en-US', 'en']});
                window.chrome = {runtime: {}};
            """)
            
            self.logger.info("✅ Chrome浏览器会话创建成功")
            return driver
            
        except Exception as e:
            self.logger.error(f"创建浏览器会话失败: {e}")
            return None
    
    async def initialize_session(self):
        """初始化会话"""
        print("🚀 初始化ForexFactory会话...")
        
        # 创建驱动
        self.driver = self.create_simple_driver()
        if not self.driver:
            return False
        
        try:
            # 访问ForexFactory主页
            print("🌐 访问ForexFactory主页...")
            self.driver.get("https://www.forexfactory.com")
            await asyncio.sleep(3)
            
            # 检查是否遇到Cloudflare
            if await self.handle_cloudflare_verification():
                print("✅ Cloudflare验证处理完成")
            
            # 访问交易页面
            print("🎯 访问交易页面...")
            self.driver.get(self.trades_url)
            await asyncio.sleep(3)
            
            # 再次检查Cloudflare
            if await self.handle_cloudflare_verification():
                print("✅ 交易页面验证完成")
            
            # 验证页面是否正常加载
            page_source = self.driver.page_source.lower()
            if 'trade' in page_source and 'forexfactory' in self.driver.current_url:
                self.session_active = True
                self.monitor_stats['session_start'] = datetime.now().isoformat()
                print("🎉 会话初始化成功！")
                return True
            else:
                print("❌ 页面加载异常")
                return False
                
        except Exception as e:
            self.logger.error(f"会话初始化失败: {e}")
            return False
    
    async def handle_cloudflare_verification(self):
        """处理Cloudflare验证"""
        try:
            page_source = self.driver.page_source.lower()
            page_title = self.driver.title.lower()
            
            # 检查是否遇到Cloudflare
            cf_keywords = ['cloudflare', '验证您是真人', 'checking your browser', '请完成以下操作']
            
            if any(keyword in page_source or keyword in page_title for keyword in cf_keywords):
                self.monitor_stats['cloudflare_encounters'] += 1
                
                print("\n" + "🛡️" * 60)
                print("🛡️ 检测到Cloudflare验证")
                print("🛡️" * 60)
                print("📋 请在浏览器窗口中完成验证:")
                print("   1. 点击 '确认您是真人' 复选框")
                print("   2. 完成任何图片验证")
                print("   3. 等待页面自动跳转")
                print("   4. 不要关闭浏览器窗口")
                print("🛡️" * 60)
                print("⏰ 系统将等待验证完成...")
                
                # 等待验证完成
                max_wait = 300  # 5分钟
                waited = 0
                
                while waited < max_wait:
                    await asyncio.sleep(3)
                    waited += 3
                    
                    current_source = self.driver.page_source.lower()
                    current_url = self.driver.current_url.lower()
                    
                    # 检查是否验证完成
                    if not any(keyword in current_source for keyword in cf_keywords):
                        if 'forexfactory.com' in current_url:
                            print("✅ Cloudflare验证完成！")
                            await asyncio.sleep(2)  # 等待页面稳定
                            return True
                    
                    # 显示等待进度
                    remaining = max_wait - waited
                    print(f"\r⏰ 等待验证完成... 剩余: {remaining}秒", end="", flush=True)
                
                print("\n⏰ 验证等待超时")
                return False
            
            return True  # 没有遇到验证
            
        except Exception as e:
            self.logger.error(f"处理Cloudflare验证失败: {e}")
            return False
    
    async def capture_current_trades(self):
        """捕获当前交易数据"""
        if not self.session_active or not self.driver:
            return []
        
        try:
            # 刷新页面获取最新数据
            self.driver.refresh()
            await asyncio.sleep(3)
            
            # 检查是否又遇到Cloudflare
            page_source = self.driver.page_source.lower()
            if 'cloudflare' in page_source or '验证您是真人' in page_source:
                print("⚠️  遇到Cloudflare验证，尝试处理...")
                if await self.handle_cloudflare_verification():
                    print("✅ 验证处理完成，继续捕获")
                else:
                    print("❌ 验证处理失败，跳过本轮")
                    return []
            
            # 解析页面数据
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            current_trades = []
            
            # 查找表格数据
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 4:
                        content = [cell.get_text().strip() for cell in cells]
                        
                        # 跳过表头
                        if content[0] == 'Trade' or 'Saving' in content[0]:
                            continue
                        
                        # 解析交易信息
                        trade_info = self.parse_trade_info(content)
                        if trade_info and trade_info['trader'] in self.target_traders:
                            current_trades.append(trade_info)
            
            self.monitor_stats['successful_captures'] += 1
            return current_trades
            
        except Exception as e:
            self.logger.error(f"捕获交易数据失败: {e}")
            return []
    
    def parse_trade_info(self, content):
        """解析交易信息"""
        try:
            trade_text = content[0]
            trader_name = content[1]
            return_text = content[2]
            pips_text = content[3]
            
            # 解析交易品种
            symbol_match = re.search(r'(BTC/USD|EUR/USD|Xauusdc|GBP/USD|USD/JPY)', trade_text, re.IGNORECASE)
            symbol = symbol_match.group(1).upper() if symbol_match else None
            if symbol == 'XAUUSDC':
                symbol = 'XAUUSD'
            
            # 解析交易方向
            action = None
            if re.search(r'\b(BUY|Long)\b', trade_text, re.IGNORECASE):
                action = 'BUY'
            elif re.search(r'\b(SELL|Short)\b', trade_text, re.IGNORECASE):
                action = 'SELL'
            
            # 解析状态
            status = 'unknown'
            if 'Opened' in trade_text:
                status = 'open'
            elif 'Closed' in trade_text:
                status = 'closed'
            
            # 解析时间
            time_match = re.search(r'(\d+)\s*min\s*ago', trade_text)
            minutes_ago = int(time_match.group(1)) if time_match else 0
            
            if symbol and action and trader_name:
                return {
                    'symbol': symbol,
                    'action': action,
                    'trader': trader_name,
                    'status': status,
                    'minutes_ago': minutes_ago,
                    'timestamp': datetime.now() - timedelta(minutes=minutes_ago),
                    'raw_text': trade_text,
                    'trade_id': f"{trader_name}_{symbol}_{action}_{minutes_ago}"
                }
        
        except Exception as e:
            pass
        
        return None
    
    def detect_new_trades(self, current_trades):
        """检测新交易"""
        new_trades = []
        
        # 创建当前交易的快照
        current_snapshot = {}
        for trade in current_trades:
            trader = trade['trader']
            if trader not in current_snapshot:
                current_snapshot[trader] = []
            current_snapshot[trader].append(trade)
        
        # 与上次快照比较
        for trader, trades in current_snapshot.items():
            last_trades = self.last_trades_snapshot.get(trader, [])
            
            for trade in trades:
                # 检查是否是新交易（10分钟内且之前没有）
                if trade['minutes_ago'] <= 10:
                    is_new = True
                    for last_trade in last_trades:
                        if (last_trade.get('symbol') == trade['symbol'] and
                            last_trade.get('action') == trade['action'] and
                            abs(last_trade.get('minutes_ago', 999) - trade['minutes_ago']) <= 2):
                            is_new = False
                            break
                    
                    if is_new:
                        new_trades.append(trade)
        
        # 更新快照
        self.last_trades_snapshot = current_snapshot
        
        return new_trades
    
    def generate_signals(self, new_trades):
        """生成交易信号"""
        signals = []
        
        # 交易员评分
        trader_scores = {
            'fusiongoldfx': 61.0,
            'theTrip75': 56.0,
            'pipclubtrade': 20.0,
            'Dominicus': 15.0,
            'geo1683': 15.0
        }
        
        for trade in new_trades:
            if trade['status'] == 'open':  # 只为开仓交易生成信号
                trader_name = trade['trader']
                score = trader_scores.get(trader_name, 10.0)
                confidence = min(0.9, max(0.3, score / 100))
                
                signal = {
                    'id': f"ff_live_{trade['trade_id']}_{int(datetime.now().timestamp())}",
                    'timestamp': datetime.now().isoformat(),
                    'source': f'FF_Live_{trader_name}',
                    'symbol': trade['symbol'],
                    'action': trade['action'],
                    'confidence': confidence,
                    'signal_type': 'live_follow',
                    'trader_score': score,
                    'minutes_since_entry': trade['minutes_ago'],
                    'is_real': True,
                    'platform': 'ForexFactory_Live',
                    'urgency': 'high' if trade['minutes_ago'] < 5 else 'medium'
                }
                
                signals.append(signal)
        
        return signals
    
    def save_signals(self, signals):
        """保存信号"""
        if not signals:
            return
        
        try:
            Path("logs").mkdir(exist_ok=True)
            Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
            
            # 追加到实时信号文件
            with open('logs/ff_live_signals.json', 'a', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False, default=str) + '\n')
            
            # 保存最新信号
            with open('logs/ff_latest_signals.json', 'w', encoding='utf-8') as f:
                json.dump(signals, f, indent=2, ensure_ascii=False, default=str)
            
            # 保存到CSV格式供MT4使用
            import csv
            with open('MQL4/Files/ff_live_signals.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for signal in signals:
                    writer.writerow([
                        signal['timestamp'],
                        signal['symbol'],
                        signal['action'],
                        0,  # entry_price
                        0,  # stop_loss
                        0,  # take_profit
                        0.01,  # lot_size
                        f"FF_LIVE_{signal['source']}"
                    ])
            
            self.logger.info(f"✅ 已保存 {len(signals)} 个信号")
            
        except Exception as e:
            self.logger.error(f"保存信号失败: {e}")
    
    async def monitor_cycle(self):
        """单次监控周期"""
        self.monitor_stats['monitoring_cycles'] += 1
        cycle_num = self.monitor_stats['monitoring_cycles']
        
        print(f"🔄 第 {cycle_num} 轮监控...")
        
        try:
            # 捕获当前交易
            current_trades = await self.capture_current_trades()
            
            if current_trades:
                print(f"📊 捕获到 {len(current_trades)} 个目标交易员的交易")
                
                # 检测新交易
                new_trades = self.detect_new_trades(current_trades)
                
                if new_trades:
                    self.monitor_stats['new_trades_detected'] += len(new_trades)
                    
                    print(f"🆕 发现 {len(new_trades)} 个新交易:")
                    for trade in new_trades:
                        print(f"   {trade['trader']}: {trade['symbol']} {trade['action']} ({trade['minutes_ago']}分钟前)")
                    
                    # 生成信号
                    signals = self.generate_signals(new_trades)
                    
                    if signals:
                        self.monitor_stats['signals_generated'] += len(signals)
                        self.save_signals(signals)
                        
                        print(f"🎯 生成 {len(signals)} 个跟单信号:")
                        for signal in signals:
                            urgency = "🔥" if signal['urgency'] == 'high' else "⚡"
                            print(f"   {urgency} {signal['symbol']} {signal['action']} - 置信度: {signal['confidence']*100:.0f}%")
                
                else:
                    print("⚪ 无新交易")
            
            else:
                print("⚠️  数据捕获失败")
            
        except Exception as e:
            self.logger.error(f"监控周期 {cycle_num} 出错: {e}")
            print(f"❌ 第 {cycle_num} 轮监控出错: {e}")
    
    async def start_monitoring(self, interval_minutes=5):
        """启动监控"""
        print("🚀 ForexFactory简单监控系统")
        print("="*50)
        
        # 初始化会话
        if not await self.initialize_session():
            print("❌ 会话初始化失败")
            return
        
        print(f"📊 监控系统已启动")
        print(f"🎯 监控目标: {', '.join(self.target_traders)}")
        print(f"⏰ 监控间隔: {interval_minutes} 分钟")
        print(f"🔄 按 Ctrl+C 停止监控")
        print("="*50)
        
        try:
            while True:
                await self.monitor_cycle()
                
                # 显示统计
                stats = self.monitor_stats
                print(f"📊 统计: 周期{stats['monitoring_cycles']} | 新交易{stats['new_trades_detected']} | 信号{stats['signals_generated']} | CF验证{stats['cloudflare_encounters']}")
                
                print(f"⏰ 等待 {interval_minutes} 分钟...")
                await asyncio.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print(f"\n⏹️  停止监控")
            
            # 显示最终统计
            print(f"\n📊 监控统计:")
            print(f"   监控周期: {stats['monitoring_cycles']}")
            print(f"   新交易: {stats['new_trades_detected']}")
            print(f"   生成信号: {stats['signals_generated']}")
            print(f"   CF验证: {stats['cloudflare_encounters']}")
            print(f"   成功捕获: {stats['successful_captures']}")
        
        finally:
            if self.driver:
                try:
                    input("\n按回车键关闭浏览器...")
                    self.driver.quit()
                except:
                    pass


async def main():
    """主函数"""
    if not SELENIUM_AVAILABLE:
        print("❌ 需要安装依赖:")
        print("pip install selenium beautifulsoup4")
        return
    
    monitor = FFSimpleMonitor()
    
    print("🚀 选择监控间隔:")
    print("1. 快速监控 (2分钟)")
    print("2. 标准监控 (5分钟)")
    print("3. 慢速监控 (10分钟)")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("❌ 请输入有效选项 (1-3)")
    
    intervals = {'1': 2, '2': 5, '3': 10}
    interval = intervals[choice]
    
    print(f"✅ 选择了 {interval} 分钟间隔监控")
    await monitor.start_monitoring(interval)


if __name__ == "__main__":
    asyncio.run(main())
