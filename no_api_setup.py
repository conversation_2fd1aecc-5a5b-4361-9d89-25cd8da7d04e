#!/usr/bin/env python3
"""
无API版本设置 - 不需要付费API的MT4跟单系统
完全免费，无需任何付费API，直接与MT4通信
"""

import os
import json
import subprocess
import sys
from pathlib import Path


def create_no_api_config():
    """创建无API版本的配置"""
    print("🔧 创建无API版本配置...")

    config = {
        "mode": "no_api",
        "mt4_connection": {
            "type": "direct",  # 直接连接MT4
            "use_dll": True,   # 使用DLL连接
            "terminal_path": "C:\\Program Files\\MetaTrader 4\\terminal.exe"
        },

        "signal_sources": {
            "telegram_channels": [
                {
                    "name": "免费外汇信号",
                    "channel_id": "@forexsignalsfree",
                    "enabled": True,
                    "weight": 0.4,
                    "description": "免费的外汇信号频道"
                },
                {
                    "name": "黄金信号",
                    "channel_id": "@goldsignals",
                    "enabled": True,
                    "weight": 0.3,
                    "description": "黄金交易信号"
                }
            ],

            "web_scraping": {
                "enabled": True,
                "sources": [
                    {
                        "name": "investing_com",
                        "url": "https://cn.investing.com/analysis/forex",
                        "enabled": True,
                        "weight": 0.2
                    },
                    {
                        "name": "forexfactory",
                        "url": "https://www.forexfactory.com/calendar",
                        "enabled": True,
                        "weight": 0.1
                    }
                ]
            },

            "manual_signals": {
                "enabled": True,
                "file_path": "signals/manual_signals.txt",
                "description": "手动输入的交易信号"
            }
        },

        "mt4_integration": {
            "use_files": True,           # 使用文件通信
            "signals_file": "MT4_Data/signals.csv",
            "status_file": "MT4_Data/status.csv",
            "trades_file": "MT4_Data/trades.csv",
            "check_interval": 1          # 检查间隔(秒)
        },

        "risk_management": {
            "account_settings": {
                "max_risk_per_trade": 0.02,
                "max_daily_risk": 0.05,
                "max_total_risk": 0.15,
                "emergency_stop_loss": 0.20
            },
            "position_sizing": {
                "base_lot_size": 0.01,
                "max_lot_size": 0.5,
                "risk_per_trade_percent": 2.0,
                "use_dynamic_sizing": True
            }
        },

        "trading_settings": {
            "allowed_symbols": [
                "EURUSD", "GBPUSD", "USDJPY", "USDCHF",
                "AUDUSD", "USDCAD", "NZDUSD", "EURJPY",
                "GBPJPY", "EURGBP", "XAUUSD", "XAGUSD"
            ],
            "trading_hours": {
                "enabled": True,
                "start_time": "01:00",
                "end_time": "23:00",
                "timezone": "Asia/Shanghai"
            },
            "auto_trading": True,
            "max_spread": 3.0
        },

        "free_apis": {
            "exchange_rates": {
                "provider": "fixer.io",  # 免费汇率API
                "api_key": "free_tier",
                "url": "http://data.fixer.io/api/latest"
            },
            "news_api": {
                "provider": "newsapi.org",  # 免费新闻API
                "api_key": "free_tier",
                "url": "https://newsapi.org/v2/everything"
            }
        },

        "telegram_setup": {
            "use_userbot": True,  # 使用用户机器人而非Bot API
            "phone_number": "",   # 你的手机号
            "api_id": "",         # 从 my.telegram.org 获取
            "api_hash": "",       # 从 my.telegram.org 获取
            "session_name": "trading_session"
        },

        "monitoring": {
            "web_dashboard": {
                "enabled": True,
                "host": "localhost",
                "port": 8080
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/trading_no_api.log"
            }
        }
    }

    # 保存配置
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)

    with open(config_dir / "config_no_api.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

    print("✅ 无API配置文件已创建: config/config_no_api.json")


def setup_telegram_userbot():
    """设置Telegram用户机器人"""
    print("\n📱 设置Telegram用户机器人...")
    print("这样可以免费监控Telegram频道的信号")

    print("\n步骤1: 获取Telegram API凭据")
    print("1. 访问 https://my.telegram.org/apps")
    print("2. 登录你的Telegram账户")
    print("3. 创建新应用，获取 api_id 和 api_hash")

    print("\n步骤2: 配置用户机器人")
    api_id = input("请输入你的 API ID (按回车跳过): ").strip()
    api_hash = input("请输入你的 API Hash (按回车跳过): ").strip()
    phone = input("请输入你的手机号 (按回车跳过): ").strip()

    if api_id and api_hash:
        # 更新配置文件
        config_file = Path("config/config_no_api.json")
        if config_file.exists():
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)

            config["telegram_setup"]["api_id"] = api_id
            config["telegram_setup"]["api_hash"] = api_hash
            config["telegram_setup"]["phone_number"] = phone

            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            print("✅ Telegram配置已保存")

    print("\n📋 推荐的免费信号频道:")
    channels = [
        "@forexsignalsfree - 免费外汇信号",
        "@goldsignals - 黄金信号",
        "@cryptosignalsfree - 加密货币信号",
        "@forexvipgroup - VIP外汇群组",
        "@fxpremiumsignals - 高级外汇信号"
    ]

    for channel in channels:
        print(f"  • {channel}")


def setup_mt4_connection():
    """设置MT4连接"""
    print("\n🔌 设置MT4连接...")
    print("无API版本使用以下方式连接MT4:")

    print("\n方法1: 文件通信 (推荐)")
    print("- 通过共享文件与MT4通信")
    print("- 不需要任何API")
    print("- 稳定可靠")

    print("\n方法2: DLL连接")
    print("- 使用Windows DLL直接连接")
    print("- 速度更快")
    print("- 需要MT4允许DLL导入")

    print("\n方法3: 复制交易")
    print("- 监控MT4专家文件夹")
    print("- 自动复制交易")
    print("- 完全免费")

    # 创建MT4数据目录
    mt4_data_dir = Path("MT4_Data")
    mt4_data_dir.mkdir(exist_ok=True)

    # 创建信号文件
    signals_file = mt4_data_dir / "signals.csv"
    with open(signals_file, "w", encoding="utf-8") as f:
        f.write("timestamp,symbol,action,entry_price,stop_loss,take_profit,volume,status\n")

    print(f"✅ MT4数据目录已创建: {mt4_data_dir}")


def create_free_signal_sources():
    """创建免费信号源"""
    print("\n📊 设置免费信号源...")

    # 创建信号目录
    signals_dir = Path("signals")
    signals_dir.mkdir(exist_ok=True)

    # 创建手动信号文件
    manual_signals = signals_dir / "manual_signals.txt"
    with open(manual_signals, "w", encoding="utf-8") as f:
        f.write("# 手动交易信号文件\n")
        f.write("# 格式: SYMBOL ACTION ENTRY_PRICE STOP_LOSS TAKE_PROFIT\n")
        f.write("# 示例: EURUSD BUY 1.0850 1.0800 1.0950\n")
        f.write("\n")

    # 创建网页抓取脚本
    scraper_script = signals_dir / "web_scraper.py"
    with open(scraper_script, "w", encoding="utf-8") as f:
        f.write('''#!/usr/bin/env python3
"""
免费信号网页抓取器
"""

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime

def scrape_investing_com():
    """抓取investing.com的分析"""
    try:
        url = "https://cn.investing.com/analysis/forex"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        response = requests.get(url, headers=headers, timeout=10)
        soup = BeautifulSoup(response.content, 'html.parser')

        # 提取分析文章
        articles = soup.find_all('article', class_='articleItem')

        signals = []
        for article in articles[:5]:  # 只取前5篇
            title = article.find('a', class_='title')
            if title:
                text = title.get_text()
                # 简单的信号识别
                if any(word in text.lower() for word in ['买入', 'buy', '看涨', 'bull']):
                    signals.append({
                        'source': 'investing.com',
                        'text': text,
                        'action': 'BUY',
                        'timestamp': datetime.now()
                    })
                elif any(word in text.lower() for word in ['卖出', 'sell', '看跌', 'bear']):
                    signals.append({
                        'source': 'investing.com',
                        'text': text,
                        'action': 'SELL',
                        'timestamp': datetime.now()
                    })

        return signals

    except Exception as e:
        print(f"抓取investing.com失败: {e}")
        return []

if __name__ == "__main__":
    signals = scrape_investing_com()
    for signal in signals:
        print(f"发现信号: {signal}")
''')

    print(f"✅ 免费信号源已设置:")
    print(f"  • 手动信号文件: {manual_signals}")
    print(f"  • 网页抓取脚本: {scraper_script}")


def install_free_dependencies():
    """安装免费版本的依赖"""
    print("\n📦 安装免费版本依赖...")

    free_requirements = [
        "telethon",           # Telegram用户机器人
        "beautifulsoup4",     # 网页抓取
        "requests",           # HTTP请求
        "pandas",             # 数据处理
        "flask",              # Web界面
        "schedule",           # 任务调度
        "python-telegram-bot", # Telegram机器人
        "selenium",           # 浏览器自动化
        "lxml",              # XML解析
        "python-dotenv"       # 环境变量
    ]

    print("正在安装依赖包...")
    for package in free_requirements:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package],
                         check=True, capture_output=True)
            print(f"✅ {package}")
        except subprocess.CalledProcessError:
            print(f"❌ {package} - 安装失败")

    print("✅ 依赖安装完成")


def create_no_api_launcher():
    """创建无API版本启动器"""
    print("\n🚀 创建无API版本启动器...")

    launcher_content = '''#!/usr/bin/env python3
"""
无API版本启动器
"""

import asyncio
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

from no_api_main import NoAPITradingSystem

async def main():
    """主函数"""
    print("🚀 启动无API版本MT4跟单系统...")

    system = NoAPITradingSystem("config/config_no_api.json")

    try:
        await system.initialize()
        await system.start()
    except KeyboardInterrupt:
        print("\\n用户中断，正在关闭系统...")
    except Exception as e:
        print(f"系统错误: {e}")
    finally:
        await system.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
'''

    with open("start_no_api.py", "w", encoding="utf-8") as f:
        f.write(launcher_content)

    print("✅ 无API启动器已创建: start_no_api.py")


def show_setup_summary():
    """显示设置总结"""
    print("\n" + "="*60)
    print("🎉 无API版本设置完成！")
    print("="*60)

    print("\n📋 你现在拥有:")
    print("✅ 免费的Telegram信号监控")
    print("✅ 网页信号抓取功能")
    print("✅ 手动信号输入")
    print("✅ 完整的风险管理")
    print("✅ Web监控面板")
    print("✅ 无需任何付费API")

    print("\n🚀 启动方式:")
    print("python start_no_api.py")

    print("\n📱 Telegram设置:")
    print("1. 访问 https://my.telegram.org/apps 获取API凭据")
    print("2. 编辑 config/config_no_api.json 填入凭据")
    print("3. 加入免费信号频道开始跟单")

    print("\n💡 使用技巧:")
    print("• 先用模拟账户测试")
    print("• 关注多个免费信号源")
    print("• 设置合理的风险参数")
    print("• 定期检查系统状态")

    print("\n⚠️  重要提醒:")
    print("• 外汇交易有风险，请谨慎投资")
    print("• 建议先学习基础知识")
    print("• 只投资你能承受损失的资金")

    print("\n🎯 下一步:")
    print("1. 运行: python start_no_api.py")
    print("2. 配置Telegram凭据")
    print("3. 开始免费跟单！")


def main():
    """主函数"""
    print("🆓 MT4智能跟单系统 - 无API免费版设置")
    print("="*60)
    print("无需任何付费API，完全免费使用！")
    print("="*60)

    # 创建必要目录
    for directory in ["config", "logs", "signals", "MT4_Data", "src"]:
        Path(directory).mkdir(exist_ok=True)

    # 执行设置步骤
    create_no_api_config()
    setup_telegram_userbot()
    setup_mt4_connection()
    create_free_signal_sources()
    install_free_dependencies()
    create_no_api_launcher()

    # 显示总结
    show_setup_summary()


if __name__ == "__main__":
    main()
