#!/usr/bin/env python3
"""
ForexFactory交易员分析器 - 分析FF交易员的交易模式和策略
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter
import re


class FFTraderAnalyzer:
    """ForexFactory交易员分析器"""
    
    def __init__(self):
        self.traders_data = {}
        self.analysis_results = {}
        
    def load_traders_database(self):
        """加载交易员数据库"""
        database_file = Path("data/ff_traders_database.json")
        
        if not database_file.exists():
            print("❌ 交易员数据库不存在，请先运行 forexfactory_trader_hunter.py")
            return False
        
        try:
            with open(database_file, 'r', encoding='utf-8') as f:
                self.traders_data = json.load(f)
            
            print(f"✅ 已加载 {len(self.traders_data)} 个交易员数据")
            return True
            
        except Exception as e:
            print(f"❌ 加载数据库失败: {e}")
            return False
    
    def analyze_trader_performance(self):
        """分析交易员表现"""
        print("\n📊 分析交易员表现...")
        
        performance_data = []
        
        for trader_id, trader_info in self.traders_data.items():
            stats = trader_info.get('stats', {})
            trade_history = trader_info.get('trade_history', [])
            
            performance = {
                'trader_id': trader_id,
                'win_rate': stats.get('win_rate', 0),
                'total_trades': stats.get('total_trades', len(trade_history)),
                'total_profit': stats.get('total_profit', 'N/A'),
                'trade_count': len(trade_history),
                'discovery_time': trader_info.get('discovery_time', ''),
                'profile_url': trader_info.get('profile_url', '')
            }
            
            # 分析交易历史
            if trade_history:
                symbols = [trade.get('symbol') for trade in trade_history if trade.get('symbol')]
                actions = [trade.get('action') for trade in trade_history if trade.get('action')]
                
                performance['favorite_symbols'] = Counter(symbols).most_common(3)
                performance['action_distribution'] = Counter(actions)
                performance['avg_trades_per_symbol'] = len(symbols) / len(set(symbols)) if symbols else 0
            
            performance_data.append(performance)
        
        # 排序：按胜率和交易次数排序
        performance_data.sort(key=lambda x: (x['win_rate'], x['total_trades']), reverse=True)
        
        self.analysis_results['performance'] = performance_data
        
        # 显示顶级交易员
        print(f"\n🏆 顶级交易员 (按胜率排序):")
        print("-" * 80)
        print(f"{'排名':<4} {'交易员':<20} {'胜率':<8} {'交易次数':<8} {'偏好品种':<20}")
        print("-" * 80)
        
        for i, trader in enumerate(performance_data[:10], 1):
            favorite_symbols = ', '.join([f"{sym}({count})" for sym, count in trader.get('favorite_symbols', [])[:2]])
            print(f"{i:<4} {trader['trader_id'][:18]:<20} {trader['win_rate']:<8.1f}% {trader['total_trades']:<8} {favorite_symbols:<20}")
    
    def analyze_trading_patterns(self):
        """分析交易模式"""
        print("\n📈 分析交易模式...")
        
        all_trades = []
        symbol_stats = defaultdict(list)
        action_stats = defaultdict(int)
        hourly_stats = defaultdict(int)
        
        for trader_id, trader_info in self.traders_data.items():
            trade_history = trader_info.get('trade_history', [])
            
            for trade in trade_history:
                if trade.get('symbol') and trade.get('action'):
                    trade_data = {
                        'trader_id': trader_id,
                        'symbol': trade['symbol'],
                        'action': trade['action'],
                        'raw_text': trade.get('raw_text', ''),
                        'profit_info': trade.get('profit_info', ''),
                        'parsed_time': trade.get('parsed_time', '')
                    }
                    
                    all_trades.append(trade_data)
                    symbol_stats[trade['symbol']].append(trader_id)
                    action_stats[trade['action']] += 1
        
        # 分析最受欢迎的交易品种
        popular_symbols = {}
        for symbol, traders in symbol_stats.items():
            popular_symbols[symbol] = {
                'trader_count': len(set(traders)),
                'total_trades': len(traders),
                'avg_trades_per_trader': len(traders) / len(set(traders))
            }
        
        # 按交易员数量排序
        sorted_symbols = sorted(popular_symbols.items(), 
                               key=lambda x: x[1]['trader_count'], reverse=True)
        
        print(f"\n💎 最受欢迎的交易品种:")
        print("-" * 60)
        print(f"{'品种':<10} {'交易员数':<8} {'总交易数':<8} {'平均交易/人':<12}")
        print("-" * 60)
        
        for symbol, stats in sorted_symbols[:8]:
            print(f"{symbol:<10} {stats['trader_count']:<8} {stats['total_trades']:<8} {stats['avg_trades_per_trader']:<12.1f}")
        
        # 分析交易方向偏好
        total_actions = sum(action_stats.values())
        print(f"\n📊 交易方向分布:")
        for action, count in action_stats.items():
            percentage = (count / total_actions) * 100 if total_actions > 0 else 0
            print(f"   {action}: {count} 次 ({percentage:.1f}%)")
        
        self.analysis_results['patterns'] = {
            'popular_symbols': popular_symbols,
            'action_distribution': action_stats,
            'total_trades': len(all_trades)
        }
    
    def analyze_profit_patterns(self):
        """分析盈利模式"""
        print("\n💰 分析盈利模式...")
        
        profit_data = []
        
        for trader_id, trader_info in self.traders_data.items():
            trade_history = trader_info.get('trade_history', [])
            
            for trade in trade_history:
                profit_info = trade.get('profit_info', '')
                if profit_info:
                    # 解析盈利信息
                    profit_analysis = self.parse_profit_info(profit_info)
                    if profit_analysis:
                        profit_analysis['trader_id'] = trader_id
                        profit_analysis['symbol'] = trade.get('symbol', '')
                        profit_analysis['action'] = trade.get('action', '')
                        profit_data.append(profit_analysis)
        
        if profit_data:
            # 统计盈利交易 vs 亏损交易
            profitable_trades = [p for p in profit_data if p.get('is_profit', False)]
            losing_trades = [p for p in profit_data if not p.get('is_profit', False)]
            
            print(f"📈 盈利交易: {len(profitable_trades)} 次")
            print(f"📉 亏损交易: {len(losing_trades)} 次")
            
            if len(profit_data) > 0:
                win_rate = (len(profitable_trades) / len(profit_data)) * 100
                print(f"🎯 总体胜率: {win_rate:.1f}%")
            
            # 分析盈利品种
            if profitable_trades:
                profitable_symbols = Counter([p['symbol'] for p in profitable_trades if p['symbol']])
                print(f"\n💎 最盈利的品种:")
                for symbol, count in profitable_symbols.most_common(5):
                    print(f"   {symbol}: {count} 次盈利交易")
        
        else:
            print("⚠️  未找到详细的盈利信息")
        
        self.analysis_results['profit_patterns'] = profit_data
    
    def parse_profit_info(self, profit_info):
        """解析盈利信息"""
        try:
            # 查找数字和符号
            profit_match = re.search(r'([+-]?\$?\d+\.?\d*)\s*(pips?|points?|\$)?', profit_info, re.IGNORECASE)
            
            if profit_match:
                value_str = profit_match.group(1)
                unit = profit_match.group(2) or ''
                
                # 提取数值
                value = float(re.sub(r'[^\d.-]', '', value_str))
                
                return {
                    'value': value,
                    'unit': unit.lower(),
                    'is_profit': value > 0,
                    'raw_info': profit_info
                }
        
        except Exception as e:
            pass
        
        return None
    
    def generate_trader_signals(self):
        """生成基于分析的交易信号"""
        print("\n🎯 生成基于分析的交易信号...")
        
        signals = []
        
        # 基于顶级交易员的最新交易生成信号
        performance_data = self.analysis_results.get('performance', [])
        top_traders = [p for p in performance_data if p['win_rate'] > 60 and p['total_trades'] > 5][:5]
        
        for trader in top_traders:
            trader_id = trader['trader_id']
            trader_info = self.traders_data.get(trader_id, {})
            trade_history = trader_info.get('trade_history', [])
            
            # 获取最近的交易
            for trade in trade_history[-2:]:  # 最近2个交易
                if trade.get('symbol') and trade.get('action'):
                    signal = {
                        'id': f"ff_analysis_{trader_id}_{trade['symbol']}_{trade['action']}_{hash(trade['raw_text'])}",
                        'timestamp': datetime.now().isoformat(),
                        'source': f'FF_TopTrader_{trader_id}',
                        'symbol': trade['symbol'],
                        'action': trade['action'],
                        'confidence': min(0.9, trader['win_rate'] / 100 + 0.1),  # 基于胜率调整置信度
                        'signal_type': 'top_trader_analysis',
                        'trader_win_rate': trader['win_rate'],
                        'trader_total_trades': trader['total_trades'],
                        'original_trade': trade,
                        'is_real': True,
                        'cost_type': 'free',
                        'quality': 'very_high',
                        'platform': 'ForexFactory_Analysis'
                    }
                    signals.append(signal)
        
        # 保存信号
        if signals:
            Path("logs").mkdir(exist_ok=True)
            with open('logs/ff_analysis_signals.json', 'w', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False) + '\n')
            
            print(f"✅ 生成了 {len(signals)} 个高质量分析信号")
            
            # 显示信号
            print(f"\n🎯 顶级交易员信号:")
            for i, signal in enumerate(signals, 1):
                print(f"{i}. {signal['symbol']} {signal['action']} - 置信度: {signal['confidence']*100:.0f}%")
                print(f"   来源: {signal['source']} (胜率: {signal['trader_win_rate']:.1f}%)")
        
        return signals
    
    def save_analysis_report(self):
        """保存分析报告"""
        try:
            Path("reports").mkdir(exist_ok=True)
            
            # 保存详细分析结果
            with open('reports/ff_trader_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
            
            # 生成简化报告
            report = {
                'analysis_time': datetime.now().isoformat(),
                'total_traders': len(self.traders_data),
                'top_performers': self.analysis_results.get('performance', [])[:5],
                'popular_symbols': list(self.analysis_results.get('patterns', {}).get('popular_symbols', {}).keys())[:5],
                'summary': {
                    'best_win_rate': max([p['win_rate'] for p in self.analysis_results.get('performance', [])], default=0),
                    'total_trades_analyzed': self.analysis_results.get('patterns', {}).get('total_trades', 0),
                    'most_active_trader': max(self.analysis_results.get('performance', []), 
                                            key=lambda x: x['total_trades'], default={}).get('trader_id', 'N/A')
                }
            }
            
            with open('reports/ff_analysis_summary.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\n📋 分析报告已保存:")
            print(f"   详细报告: reports/ff_trader_analysis.json")
            print(f"   摘要报告: reports/ff_analysis_summary.json")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🔍 ForexFactory交易员完整分析")
        print("="*50)
        
        # 加载数据
        if not self.load_traders_database():
            return
        
        # 执行各项分析
        self.analyze_trader_performance()
        self.analyze_trading_patterns()
        self.analyze_profit_patterns()
        
        # 生成信号
        signals = self.generate_trader_signals()
        
        # 保存报告
        self.save_analysis_report()
        
        print(f"\n🎉 ForexFactory交易员分析完成！")
        print(f"📊 分析了 {len(self.traders_data)} 个交易员")
        print(f"🎯 生成了 {len(signals)} 个高质量信号")
        print(f"📋 报告已保存到 reports/ 目录")


def main():
    """主函数"""
    analyzer = FFTraderAnalyzer()
    analyzer.run_full_analysis()


if __name__ == "__main__":
    main()
