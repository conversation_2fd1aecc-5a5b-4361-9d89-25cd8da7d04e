#!/usr/bin/env python3
"""
ForexFactory隐身猎手 - 完全隐蔽的版本，让Cloudflare无法检测
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime
from pathlib import Path

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException
    from bs4 import BeautifulSoup
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False


class FFStealthHunter:
    """完全隐蔽的ForexFactory猎手"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.base_url = "https://www.forexfactory.com"
        self.trades_url = "https://www.forexfactory.com/trades"
        
        self.captured_data = []
        self.session_stats = {
            'stealth_level': 'maximum',
            'detection_attempts': 0,
            'successful_access': 0,
            'data_extracted': 0
        }
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('FFStealthHunter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/ff_stealth_hunter.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def create_stealth_driver(self):
        """创建完全隐蔽的驱动"""
        if not SELENIUM_AVAILABLE:
            print("❌ 需要安装依赖: pip install undetected-chromedriver selenium beautifulsoup4")
            return None
        
        try:
            self.logger.info("🥷 创建隐身驱动...")
            
            # 使用undetected-chromedriver，这是专门绕过检测的
            options = uc.ChromeOptions()
            
            # 最小化检测特征
            options.add_argument('--no-first-run')
            options.add_argument('--no-service-autorun') 
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-dev-shm-usage')
            
            # 真实用户行为模拟
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions-except')
            options.add_argument('--disable-plugins-discovery')
            
            # 语言和地区设置
            options.add_argument('--lang=zh-CN,zh,en-US,en')
            options.add_argument('--accept-lang=zh-CN,zh;q=0.9,en;q=0.8')
            
            # 窗口设置 - 使用常见分辨率
            options.add_argument('--window-size=1366,768')  # 最常见的分辨率
            
            # 创建驱动 - undetected_chromedriver会自动处理很多反检测
            driver = uc.Chrome(options=options, version_main=None)
            
            # 额外的反检测脚本
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    // 完全移除webdriver痕迹
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                    
                    // 修复chrome对象
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {
                            return {
                                commitLoadTime: Date.now() - Math.random() * 1000,
                                finishDocumentLoadTime: Date.now() - Math.random() * 500,
                                finishLoadTime: Date.now() - Math.random() * 200,
                                firstPaintAfterLoadTime: Date.now() - Math.random() * 100,
                                firstPaintTime: Date.now() - Math.random() * 50,
                                navigationType: "Other",
                                numTabsInWindow: Math.floor(Math.random() * 5) + 1,
                                requestTime: Date.now() - Math.random() * 2000,
                                startLoadTime: Date.now() - Math.random() * 1500,
                                tabId: Math.floor(Math.random() * 1000),
                                wasAlternateProtocolAvailable: false,
                                wasFetchedViaSpdy: false,
                                wasNpnNegotiated: false
                            };
                        },
                        csi: function() {
                            return {
                                onloadT: Date.now(),
                                pageT: Math.random() * 1000,
                                startE: Date.now() - Math.random() * 2000,
                                tran: Math.floor(Math.random() * 20)
                            };
                        },
                        app: {
                            isInstalled: false,
                            InstallState: {
                                DISABLED: "disabled",
                                INSTALLED: "installed",
                                NOT_INSTALLED: "not_installed"
                            },
                            RunningState: {
                                CANNOT_RUN: "cannot_run",
                                READY_TO_RUN: "ready_to_run",
                                RUNNING: "running"
                            }
                        }
                    };
                    
                    // 修复plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => {
                            return [
                                {
                                    0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: Plugin},
                                    description: "Portable Document Format",
                                    filename: "internal-pdf-viewer",
                                    length: 1,
                                    name: "Chrome PDF Plugin"
                                },
                                {
                                    0: {type: "application/pdf", suffixes: "pdf", description: "", enabledPlugin: Plugin},
                                    description: "",
                                    filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                                    length: 1,
                                    name: "Chrome PDF Viewer"
                                }
                            ];
                        },
                    });
                    
                    // 修复languages
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                    });
                    
                    // 修复hardwareConcurrency
                    Object.defineProperty(navigator, 'hardwareConcurrency', {
                        get: () => 4,
                    });
                    
                    // 修复deviceMemory
                    Object.defineProperty(navigator, 'deviceMemory', {
                        get: () => 8,
                    });
                    
                    // 修复permissions
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                            Promise.resolve({ state: Notification.permission }) :
                            originalQuery(parameters)
                    );
                    
                    // 添加一些真实浏览器才有的属性
                    Object.defineProperty(navigator, 'connection', {
                        get: () => ({
                            downlink: 10,
                            effectiveType: "4g",
                            onchange: null,
                            rtt: 100,
                            saveData: false
                        }),
                    });
                    
                    // 修复getTimezoneOffset
                    Date.prototype.getTimezoneOffset = function() {
                        return -480; // 中国时区
                    };
                '''
            })
            
            self.logger.info("✅ 隐身驱动创建成功")
            return driver
            
        except Exception as e:
            self.logger.error(f"创建隐身驱动失败: {e}")
            return None
    
    async def human_like_navigation(self, driver, url):
        """模拟人类导航行为"""
        self.logger.info("🚶 模拟人类导航行为...")
        
        try:
            # 1. 先访问主页，模拟真实用户行为
            self.logger.info("🏠 先访问ForexFactory主页...")
            driver.get("https://www.forexfactory.com")
            
            # 随机等待，模拟阅读时间
            await asyncio.sleep(random.uniform(2, 5))
            
            # 2. 模拟鼠标移动
            self.logger.info("🖱️ 模拟鼠标活动...")
            driver.execute_script("""
                // 模拟鼠标移动事件
                function simulateMouseMove() {
                    const event = new MouseEvent('mousemove', {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight
                    });
                    document.dispatchEvent(event);
                }
                
                // 执行几次鼠标移动
                for(let i = 0; i < 3; i++) {
                    setTimeout(simulateMouseMove, i * 500);
                }
            """)
            
            await asyncio.sleep(random.uniform(1, 3))
            
            # 3. 现在访问目标页面
            self.logger.info(f"🎯 访问目标页面: {url}")
            driver.get(url)
            
            # 4. 模拟页面交互
            await asyncio.sleep(random.uniform(1, 2))
            
            # 模拟滚动
            driver.execute_script("""
                window.scrollTo({
                    top: Math.random() * 300,
                    behavior: 'smooth'
                });
            """)
            
            await asyncio.sleep(random.uniform(0.5, 1.5))
            
            return True
            
        except Exception as e:
            self.logger.error(f"人类导航模拟失败: {e}")
            return False
    
    async def wait_for_cloudflare_with_guidance(self, driver):
        """等待Cloudflare验证，提供人工指导"""
        self.logger.info("🛡️ 检查Cloudflare状态...")
        
        max_wait = 300  # 5分钟
        check_interval = 3
        waited = 0
        
        guidance_shown = False
        
        while waited < max_wait:
            try:
                current_url = driver.current_url.lower()
                page_source = driver.page_source.lower()
                page_title = driver.title.lower()
                
                # 检查是否遇到Cloudflare
                cf_keywords = ['cloudflare', '验证您是真人', 'checking your browser', '请完成以下操作']
                
                if any(keyword in page_source or keyword in page_title for keyword in cf_keywords):
                    if not guidance_shown:
                        self.show_manual_guidance()
                        guidance_shown = True
                    
                    # 显示等待进度
                    remaining = max_wait - waited
                    print(f"\r⏰ 等待人工完成验证... 剩余时间: {remaining}秒 (请在浏览器中操作)", end="", flush=True)
                    
                    await asyncio.sleep(check_interval)
                    waited += check_interval
                
                elif 'forexfactory.com' in current_url and ('trades' in current_url or 'forum' in current_url):
                    if guidance_shown:
                        print("\n✅ 验证完成！页面已加载")
                    self.logger.info("✅ 成功访问目标页面")
                    return True
                
                else:
                    # 可能还在加载或其他状态
                    await asyncio.sleep(check_interval)
                    waited += check_interval
                    
            except Exception as e:
                self.logger.error(f"检查Cloudflare状态出错: {e}")
                await asyncio.sleep(check_interval)
                waited += check_interval
        
        if guidance_shown:
            print(f"\n⏰ 等待超时 ({max_wait}秒)")
        
        return False
    
    def show_manual_guidance(self):
        """显示人工操作指导"""
        print("\n" + "🔥" * 80)
        print("🛡️ 检测到Cloudflare验证 - 需要人工操作")
        print("🔥" * 80)
        print()
        print("📋 请按以下步骤在浏览器窗口中操作:")
        print()
        print("🔸 步骤1: 找到验证框")
        print("   - 寻找 '确认您是真人' 或 'Verify you are human' 复选框")
        print("   - 或者等待 '正在检查您的浏览器' 自动完成")
        print()
        print("🔸 步骤2: 完成验证")
        print("   - 点击复选框（如果有的话）")
        print("   - 如果出现图片验证，请完成拼图或选择")
        print("   - 耐心等待验证完成")
        print()
        print("🔸 步骤3: 等待跳转")
        print("   - 验证成功后页面会自动跳转")
        print("   - 不要手动刷新或关闭页面")
        print("   - 系统会自动检测跳转完成")
        print()
        print("💡 重要提示:")
        print("   ✅ 这个浏览器窗口是特殊优化的，更容易通过验证")
        print("   ✅ 请在这个窗口中操作，不要开新窗口")
        print("   ✅ 如果验证失败，会自动重新出现，请重试")
        print("   ✅ 系统最多等待5分钟")
        print()
        print("🔥" * 80)
    
    async def extract_data_after_verification(self, driver):
        """验证通过后提取数据"""
        self.logger.info("🔍 开始提取数据...")
        
        try:
            # 等待页面完全加载
            await asyncio.sleep(random.uniform(3, 6))
            
            # 保存页面源码
            page_source = driver.page_source
            with open('logs/ff_stealth_page.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            # 解析页面
            soup = BeautifulSoup(page_source, 'html.parser')
            
            extracted_data = []
            
            # 提取页面标题和URL
            title = soup.find('title')
            if title:
                extracted_data.append({
                    'type': 'page_info',
                    'title': title.get_text().strip(),
                    'url': driver.current_url,
                    'extraction_time': datetime.now().isoformat(),
                    'method': 'stealth_extraction'
                })
            
            # 查找交易员链接
            trader_links = soup.find_all('a', href=lambda x: x and ('member' in x or 'showthread' in x))
            for link in trader_links[:15]:  # 限制数量
                href = link.get('href', '')
                text = link.get_text().strip()
                if text and len(text) > 2 and len(text) < 50:
                    extracted_data.append({
                        'type': 'trader_profile',
                        'username': text,
                        'profile_url': href,
                        'extraction_time': datetime.now().isoformat(),
                        'method': 'stealth_extraction'
                    })
            
            # 查找表格数据
            tables = soup.find_all('table')
            for table_idx, table in enumerate(tables[:5]):
                rows = table.find_all('tr')
                for row_idx, row in enumerate(rows[:10]):
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        cell_texts = [cell.get_text().strip() for cell in cells]
                        if any(len(text) > 0 for text in cell_texts):
                            extracted_data.append({
                                'type': 'table_content',
                                'table_index': table_idx,
                                'row_index': row_idx,
                                'content': cell_texts,
                                'extraction_time': datetime.now().isoformat(),
                                'method': 'stealth_extraction'
                            })
            
            # 查找包含交易关键词的内容
            trading_keywords = ['pips', 'profit', 'loss', 'trade', 'buy', 'sell', 'long', 'short', 'EUR', 'USD', 'GBP', 'JPY']
            all_text = soup.get_text()
            paragraphs = all_text.split('\n')
            
            for para in paragraphs:
                para = para.strip()
                if len(para) > 30 and len(para) < 200:
                    if any(keyword.lower() in para.lower() for keyword in trading_keywords):
                        extracted_data.append({
                            'type': 'trading_content',
                            'content': para,
                            'extraction_time': datetime.now().isoformat(),
                            'method': 'stealth_extraction'
                        })
                        
                        # 限制数量
                        trading_contents = [item for item in extracted_data if item['type'] == 'trading_content']
                        if len(trading_contents) >= 10:
                            break
            
            self.session_stats['data_extracted'] = len(extracted_data)
            self.logger.info(f"✅ 数据提取完成: {len(extracted_data)} 条")
            
            return extracted_data
            
        except Exception as e:
            self.logger.error(f"数据提取失败: {e}")
            return []
    
    def save_stealth_results(self, data):
        """保存隐身结果"""
        try:
            Path("data").mkdir(exist_ok=True)
            
            # 保存提取的数据
            with open('data/ff_stealth_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # 保存会话统计
            with open('logs/ff_stealth_stats.json', 'w', encoding='utf-8') as f:
                json.dump(self.session_stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info("✅ 隐身结果保存完成")
            
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    async def run_stealth_hunt(self):
        """运行隐身猎取"""
        self.logger.info("🥷 启动ForexFactory隐身猎取...")
        
        # 创建隐身驱动
        driver = self.create_stealth_driver()
        if not driver:
            print("❌ 无法创建隐身驱动")
            return
        
        try:
            print("🥷 隐身驱动已启动，开始访问ForexFactory...")
            
            # 人类化导航
            nav_success = await self.human_like_navigation(driver, self.trades_url)
            if not nav_success:
                print("❌ 导航失败")
                return
            
            # 等待Cloudflare验证（人工操作）
            cf_passed = await self.wait_for_cloudflare_with_guidance(driver)
            
            if cf_passed:
                print("🎉 成功通过验证！开始提取数据...")
                
                # 提取数据
                extracted_data = await self.extract_data_after_verification(driver)
                
                if extracted_data:
                    self.captured_data = extracted_data
                    self.save_stealth_results(extracted_data)
                    self.display_stealth_success()
                else:
                    print("⚠️  验证通过但未提取到数据")
                    self.display_partial_success()
            else:
                print("❌ Cloudflare验证未通过或超时")
                self.display_stealth_failure()
        
        except Exception as e:
            self.logger.error(f"隐身猎取过程出错: {e}")
            print(f"❌ 运行异常: {e}")
        
        finally:
            try:
                input("\n按回车键关闭浏览器...")
                driver.quit()
            except:
                pass
    
    def display_stealth_success(self):
        """显示隐身成功结果"""
        print(f"\n{'🎉' * 20}")
        print(f"🥷 ForexFactory隐身猎取成功！")
        print(f"{'🎉' * 20}")
        print(f"📊 提取统计:")
        print(f"   隐身等级: {self.session_stats['stealth_level']}")
        print(f"   数据条数: {self.session_stats['data_extracted']}")
        
        if self.captured_data:
            print(f"\n🔍 数据类型:")
            type_counts = {}
            for item in self.captured_data:
                item_type = item.get('type', 'unknown')
                type_counts[item_type] = type_counts.get(item_type, 0) + 1
            
            for data_type, count in type_counts.items():
                print(f"   {data_type}: {count} 条")
        
        print(f"\n📁 文件位置:")
        print(f"   数据文件: data/ff_stealth_data.json")
        print(f"   页面源码: logs/ff_stealth_page.html")
        print(f"   统计信息: logs/ff_stealth_stats.json")
        print(f"   日志文件: logs/ff_stealth_hunter.log")
        print(f"{'🎉' * 20}")
    
    def display_partial_success(self):
        """显示部分成功"""
        print(f"\n⚠️  部分成功 - 验证通过但数据提取不完整")
        print(f"💡 可能原因:")
        print(f"   - 页面结构变化")
        print(f"   - 需要登录账户")
        print(f"   - 内容动态加载")
        print(f"📋 建议查看: logs/ff_stealth_page.html")
    
    def display_stealth_failure(self):
        """显示隐身失败"""
        print(f"\n❌ 隐身猎取失败")
        print(f"💡 可能原因:")
        print(f"   1. Cloudflare验证超时")
        print(f"   2. 网络环境被限制")
        print(f"   3. 验证步骤未正确完成")
        print(f"🔧 建议:")
        print(f"   - 更换网络环境")
        print(f"   - 稍后重试")
        print(f"   - 检查系统时间")


async def main():
    """主函数"""
    print("🥷 ForexFactory隐身猎手")
    print("="*50)
    print("🛡️ 专门绕过Cloudflare检测")
    print("🤖 使用undetected-chromedriver")
    print("👤 需要人工完成验证步骤")
    print("="*50)
    
    if not SELENIUM_AVAILABLE:
        print("\n❌ 缺少必要依赖，请安装:")
        print("pip install undetected-chromedriver selenium beautifulsoup4")
        return
    
    hunter = FFStealthHunter()
    
    print("\n🚀 准备启动隐身猎取...")
    print("💡 系统会打开一个特殊优化的浏览器")
    print("🤖 如果遇到Cloudflare验证，请按提示人工完成")
    print("⏰ 整个过程可能需要5-15分钟")
    
    confirm = input("\n确认开始? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消")
        return
    
    try:
        await hunter.run_stealth_hunt()
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
