# 🚀 MT4智能跟单系统 - 安装指南

## 📋 系统要求

### 基础环境
- **操作系统**: Windows 10/11 (推荐)
- **Python**: 3.8 或更高版本
- **内存**: 最少 4GB RAM
- **硬盘**: 至少 2GB 可用空间

### 必需软件
- **MetaTrader 4/5**: 已安装并可正常运行
- **Python**: [下载地址](https://www.python.org/downloads/)
- **Git**: [下载地址](https://git-scm.com/downloads) (可选)

## 🔧 快速安装

### 方法1: 使用启动脚本 (推荐)

1. **下载项目文件**
   ```bash
   # 如果有Git
   git clone https://github.com/your-repo/mt4-auto-copier.git
   cd mt4-auto-copier
   
   # 或者直接下载ZIP文件并解压
   ```

2. **运行启动脚本**
   ```bash
   python start.py
   ```
   
3. **按照提示完成设置**
   - 脚本会自动检查环境
   - 安装必要的依赖包
   - 创建配置文件
   - 引导你完成配置

### 方法2: 手动安装

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **创建配置文件**
   ```bash
   cp config/config.example.json config/config.json
   ```

3. **编辑配置文件**
   ```bash
   # 使用文本编辑器打开 config/config.json
   # 填入你的API密钥和设置
   ```

## 🔑 API配置

### 1. MetaAPI 配置

1. **注册MetaAPI账户**
   - 访问: https://app.metaapi.cloud/sign-up
   - 注册免费账户

2. **获取API Token**
   - 登录后访问: https://app.metaapi.cloud/token
   - 复制你的API Token

3. **添加MT4账户**
   - 访问: https://app.metaapi.cloud/accounts
   - 添加你的MT4账户信息
   - 获取Account ID

### 2. Telegram 配置

1. **创建Telegram Bot**
   - 在Telegram中搜索 @BotFather
   - 发送 `/newbot` 创建新机器人
   - 获取Bot Token

2. **获取API凭据**
   - 访问: https://my.telegram.org/apps
   - 创建新应用获取 API ID 和 API Hash

### 3. Myfxbook 配置 (可选)

1. **注册Myfxbook账户**
   - 访问: https://www.myfxbook.com/
   - 注册账户

2. **获取API访问权限**
   - 联系Myfxbook获取API访问权限
   - 或使用网页抓取方式

## ⚙️ 配置文件说明

### 基本配置
```json
{
  "api_keys": {
    "metaapi": {
      "token": "你的MetaAPI Token",
      "account_id": "你的MT4账户ID"
    },
    "telegram": {
      "bot_token": "你的Telegram Bot Token",
      "api_id": "你的Telegram API ID",
      "api_hash": "你的Telegram API Hash"
    }
  }
}
```

### 风险管理配置
```json
{
  "risk_management": {
    "account_settings": {
      "max_risk_per_trade": 0.02,    // 单笔最大风险2%
      "max_daily_risk": 0.05,        // 每日最大风险5%
      "max_total_risk": 0.15,        // 总体最大风险15%
      "emergency_stop_loss": 0.20    // 紧急止损20%
    }
  }
}
```

### 信号源配置
```json
{
  "signal_sources": {
    "telegram_channels": [
      {
        "name": "ForexSignals_Pro",
        "channel_id": "@forexsignals_pro",
        "enabled": true,
        "weight": 0.3
      }
    ]
  }
}
```

## 🚀 启动系统

### 使用启动脚本
```bash
python start.py
```

选择菜单选项:
- `1` - 启动主程序 (跟单系统)
- `2` - 启动Web监控面板
- `3` - 同时启动主程序和监控面板

### 手动启动

1. **启动主程序**
   ```bash
   python main.py
   ```

2. **启动Web监控面板**
   ```bash
   python web_dashboard.py
   ```

3. **访问监控面板**
   - 打开浏览器访问: http://localhost:8080

## 📊 监控面板功能

### 实时数据显示
- 账户余额和盈亏
- 当前持仓状态
- 今日交易统计
- 风险指标监控

### 交易历史
- 最近交易记录
- 信号处理统计
- 交易师表现排名

## ⚠️ 重要注意事项

### 安全提醒
1. **保护API密钥**: 不要泄露你的API密钥
2. **备份配置**: 定期备份配置文件
3. **监控风险**: 密切关注风险指标

### 风险管理
1. **小额测试**: 先用小额资金测试
2. **逐步增加**: 确认系统稳定后再增加资金
3. **定期检查**: 定期检查系统运行状态

### 法律合规
1. **遵守法规**: 确保在你的地区使用是合法的
2. **风险自负**: 交易有风险，盈亏自负
3. **理性投资**: 只投资你能承受损失的资金

## 🔧 故障排除

### 常见问题

1. **MT5连接失败**
   ```
   解决方案:
   - 确保MT4/MT5正在运行
   - 检查账户登录状态
   - 验证MetaAPI配置
   ```

2. **Telegram连接失败**
   ```
   解决方案:
   - 检查Bot Token是否正确
   - 验证API ID和API Hash
   - 确保网络连接正常
   ```

3. **依赖包安装失败**
   ```
   解决方案:
   - 升级pip: pip install --upgrade pip
   - 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
   - 手动安装问题包
   ```

### 日志查看
```bash
# 查看系统日志
python start.py
# 选择选项 6 查看日志

# 或直接查看日志文件
cat logs/trading.log
```

### 获取帮助
- **GitHub Issues**: 提交问题和建议
- **文档**: 查看详细文档
- **社区**: 加入Telegram讨论群

## 📈 性能优化

### 系统优化
1. **关闭不必要的程序**: 释放系统资源
2. **稳定网络**: 确保网络连接稳定
3. **定期重启**: 定期重启系统和程序

### 配置优化
1. **调整信号过滤**: 根据实际情况调整过滤条件
2. **优化风险参数**: 根据账户大小调整风险设置
3. **监控性能**: 定期查看系统性能指标

## 🔄 更新升级

### 检查更新
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

### 备份数据
```bash
# 备份配置文件
cp config/config.json config/config.backup.json

# 备份数据库
cp data/trading.db data/trading.backup.db
```

---

## 📞 技术支持

如果遇到问题，请按以下顺序寻求帮助：

1. **查看文档**: 仔细阅读本安装指南
2. **检查日志**: 查看系统日志了解错误信息
3. **搜索问题**: 在GitHub Issues中搜索类似问题
4. **提交Issue**: 如果问题未解决，请提交详细的问题报告

**祝你交易顺利！** 🎉
