"""
投资组合管理器 - 负责资金分配和投资组合优化
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import MetaTrader5 as mt5


class PortfolioManager:
    """投资组合管理器"""
    
    def __init__(self, config: Dict, db_manager, risk_manager):
        self.config = config
        self.db_manager = db_manager
        self.risk_manager = risk_manager
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.max_allocation_per_trader = config['risk_management']['trader_limits']['max_allocation_per_trader']
        self.base_lot_size = config['risk_management']['position_sizing']['base_lot_size']
        self.max_lot_size = config['risk_management']['position_sizing']['max_lot_size']
        self.risk_per_trade_percent = config['risk_management']['position_sizing']['risk_per_trade_percent']
        
        # 投资组合状态
        self.trader_allocations = {}
        self.symbol_exposures = {}
        self.correlation_matrix = {}
        
    async def calculate_position_size(self, signal) -> float:
        """计算仓位大小"""
        try:
            # 获取账户信息
            account_info = mt5.account_info()
            if not account_info:
                return self.base_lot_size
            
            balance = account_info.balance
            
            # 基础仓位计算
            base_position = await self.calculate_base_position_size(signal, balance)
            
            # 根据信号质量调整
            quality_adjusted = self.adjust_for_signal_quality(base_position, signal)
            
            # 根据交易师表现调整
            trader_adjusted = await self.adjust_for_trader_performance(quality_adjusted, signal)
            
            # 根据相关性调整
            correlation_adjusted = await self.adjust_for_correlation(trader_adjusted, signal)
            
            # 应用风险限制
            final_position = self.apply_risk_limits(correlation_adjusted, signal, balance)
            
            return final_position
            
        except Exception as e:
            self.logger.error(f"计算仓位大小错误: {e}")
            return self.base_lot_size
    
    async def calculate_base_position_size(self, signal, balance: float) -> float:
        """计算基础仓位大小"""
        try:
            # 基于风险百分比计算
            risk_amount = balance * (self.risk_per_trade_percent / 100)
            
            # 如果有止损价格，基于止损计算仓位
            if signal.stop_loss and signal.entry_price:
                if signal.action == 'BUY':
                    risk_pips = abs(signal.entry_price - signal.stop_loss)
                else:
                    risk_pips = abs(signal.stop_loss - signal.entry_price)
                
                # 获取品种信息
                symbol_info = mt5.symbol_info(signal.symbol)
                if symbol_info and risk_pips > 0:
                    pip_value = symbol_info.trade_tick_value
                    position_size = risk_amount / (risk_pips * pip_value)
                    return max(self.base_lot_size, min(self.max_lot_size, position_size))
            
            # 默认返回基础仓位
            return self.base_lot_size
            
        except Exception as e:
            self.logger.error(f"计算基础仓位错误: {e}")
            return self.base_lot_size
    
    def adjust_for_signal_quality(self, base_position: float, signal) -> float:
        """根据信号质量调整仓位"""
        try:
            # 置信度调整
            confidence_factor = signal.confidence
            
            # 风险回报比调整
            rr_factor = 1.0
            if signal.stop_loss and signal.take_profit and signal.entry_price:
                rr_ratio = self.risk_manager.calculate_risk_reward_ratio(signal)
                if rr_ratio > 2.0:
                    rr_factor = 1.2  # 高风险回报比增加仓位
                elif rr_ratio < 1.0:
                    rr_factor = 0.8  # 低风险回报比减少仓位
            
            # 综合调整
            adjusted_position = base_position * confidence_factor * rr_factor
            
            return max(self.base_lot_size, min(self.max_lot_size, adjusted_position))
            
        except Exception as e:
            self.logger.error(f"信号质量调整错误: {e}")
            return base_position
    
    async def adjust_for_trader_performance(self, position: float, signal) -> float:
        """根据交易师表现调整仓位"""
        try:
            if not signal.trader_id:
                return position
            
            # 获取交易师历史表现
            performance = await self.db_manager.get_trader_performance(
                signal.trader_id, signal.source
            )
            
            if not performance:
                return position * 0.8  # 新交易师减少仓位
            
            # 根据胜率调整
            win_rate = performance.get('win_rate', 0.5)
            win_rate_factor = 0.5 + win_rate  # 0.5-1.5倍调整
            
            # 根据盈利因子调整
            profit_factor = performance.get('profit_factor', 1.0)
            if profit_factor > 1.5:
                profit_factor_adjustment = 1.2
            elif profit_factor < 0.8:
                profit_factor_adjustment = 0.8
            else:
                profit_factor_adjustment = 1.0
            
            # 根据最大回撤调整
            max_drawdown = performance.get('max_drawdown', 0.0)
            if max_drawdown > 0.2:  # 回撤超过20%
                drawdown_factor = 0.7
            elif max_drawdown > 0.1:  # 回撤超过10%
                drawdown_factor = 0.85
            else:
                drawdown_factor = 1.0
            
            # 综合调整
            adjusted_position = position * win_rate_factor * profit_factor_adjustment * drawdown_factor
            
            return max(self.base_lot_size, min(self.max_lot_size, adjusted_position))
            
        except Exception as e:
            self.logger.error(f"交易师表现调整错误: {e}")
            return position
    
    async def adjust_for_correlation(self, position: float, signal) -> float:
        """根据相关性调整仓位"""
        try:
            # 获取当前持仓
            positions = mt5.positions_get()
            if not positions:
                return position
            
            # 检查相关品种持仓
            correlated_symbols = self.get_correlated_symbols(signal.symbol)
            correlated_exposure = 0.0
            
            for pos in positions:
                if pos.symbol in correlated_symbols:
                    correlated_exposure += pos.volume
            
            # 如果相关品种持仓过多，减少新仓位
            if correlated_exposure > 0.5:  # 相关品种超过0.5手
                correlation_factor = 0.7
            elif correlated_exposure > 0.3:
                correlation_factor = 0.85
            else:
                correlation_factor = 1.0
            
            adjusted_position = position * correlation_factor
            
            return max(self.base_lot_size, min(self.max_lot_size, adjusted_position))
            
        except Exception as e:
            self.logger.error(f"相关性调整错误: {e}")
            return position
    
    def get_correlated_symbols(self, symbol: str) -> List[str]:
        """获取相关交易品种"""
        correlation_groups = {
            'EUR': ['EURUSD', 'EURGBP', 'EURJPY', 'EURCHF'],
            'GBP': ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPCHF'],
            'JPY': ['USDJPY', 'EURJPY', 'GBPJPY', 'CHFJPY'],
            'USD': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD'],
            'GOLD': ['XAUUSD', 'XAGUSD'],
        }
        
        for currency, symbols in correlation_groups.items():
            if symbol in symbols:
                return [s for s in symbols if s != symbol]
        
        return []
    
    def apply_risk_limits(self, position: float, signal, balance: float) -> float:
        """应用风险限制"""
        try:
            # 单笔交易最大风险限制
            max_risk_amount = balance * self.risk_manager.max_risk_per_trade
            
            if signal.stop_loss and signal.entry_price:
                if signal.action == 'BUY':
                    risk_per_lot = abs(signal.entry_price - signal.stop_loss)
                else:
                    risk_per_lot = abs(signal.stop_loss - signal.entry_price)
                
                symbol_info = mt5.symbol_info(signal.symbol)
                if symbol_info and risk_per_lot > 0:
                    pip_value = symbol_info.trade_tick_value
                    max_position_by_risk = max_risk_amount / (risk_per_lot * pip_value)
                    position = min(position, max_position_by_risk)
            
            # 应用最大最小仓位限制
            position = max(self.base_lot_size, min(self.max_lot_size, position))
            
            # 四舍五入到合理精度
            return round(position, 2)
            
        except Exception as e:
            self.logger.error(f"应用风险限制错误: {e}")
            return self.base_lot_size
    
    async def optimize_portfolio(self):
        """优化投资组合"""
        try:
            # 获取当前持仓
            positions = mt5.positions_get()
            if not positions:
                return
            
            # 分析持仓分布
            symbol_distribution = {}
            total_volume = 0
            
            for pos in positions:
                symbol_distribution[pos.symbol] = symbol_distribution.get(pos.symbol, 0) + pos.volume
                total_volume += pos.volume
            
            # 检查是否需要重新平衡
            if await self.needs_rebalancing(symbol_distribution, total_volume):
                await self.rebalance_portfolio(positions)
            
            # 更新投资组合状态
            await self.update_portfolio_status()
            
        except Exception as e:
            self.logger.error(f"优化投资组合错误: {e}")
    
    async def needs_rebalancing(self, symbol_distribution: Dict, total_volume: float) -> bool:
        """检查是否需要重新平衡"""
        try:
            # 检查单一品种占比是否过高
            for symbol, volume in symbol_distribution.items():
                if volume / total_volume > 0.4:  # 单一品种超过40%
                    self.logger.info(f"品种{symbol}占比过高: {volume/total_volume*100:.1f}%")
                    return True
            
            # 检查相关品种总占比
            correlation_groups = self.get_all_correlation_groups()
            for group_name, symbols in correlation_groups.items():
                group_volume = sum(symbol_distribution.get(symbol, 0) for symbol in symbols)
                if group_volume / total_volume > 0.6:  # 相关品种超过60%
                    self.logger.info(f"相关品种组{group_name}占比过高: {group_volume/total_volume*100:.1f}%")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查重新平衡需求错误: {e}")
            return False
    
    def get_all_correlation_groups(self) -> Dict[str, List[str]]:
        """获取所有相关品种组"""
        return {
            'EUR': ['EURUSD', 'EURGBP', 'EURJPY', 'EURCHF'],
            'GBP': ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPCHF'],
            'JPY': ['USDJPY', 'EURJPY', 'GBPJPY', 'CHFJPY'],
            'USD_MAJOR': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'],
            'USD_MINOR': ['AUDUSD', 'USDCAD', 'NZDUSD'],
            'PRECIOUS': ['XAUUSD', 'XAGUSD']
        }
    
    async def rebalance_portfolio(self, positions):
        """重新平衡投资组合"""
        try:
            self.logger.info("开始重新平衡投资组合...")
            
            # 这里可以实现具体的重新平衡逻辑
            # 例如：减少过度集中的持仓，增加分散化
            
            # 暂时只记录日志
            self.logger.info("投资组合重新平衡完成")
            
        except Exception as e:
            self.logger.error(f"重新平衡投资组合错误: {e}")
    
    async def update_portfolio_status(self):
        """更新投资组合状态"""
        try:
            # 计算当前投资组合指标
            positions = mt5.positions_get()
            if not positions:
                return
            
            # 计算总风险敞口
            total_exposure = sum(pos.volume for pos in positions)
            
            # 计算品种分布
            symbol_count = len(set(pos.symbol for pos in positions))
            
            # 计算总盈亏
            total_profit = sum(pos.profit for pos in positions)
            
            # 记录状态
            status = {
                'timestamp': datetime.now(),
                'total_positions': len(positions),
                'total_exposure': total_exposure,
                'symbol_diversity': symbol_count,
                'total_profit': total_profit
            }
            
            await self.db_manager.save_system_status(status)
            
        except Exception as e:
            self.logger.error(f"更新投资组合状态错误: {e}")
    
    async def get_portfolio_metrics(self) -> Dict:
        """获取投资组合指标"""
        try:
            positions = mt5.positions_get()
            if not positions:
                return {}
            
            # 基础指标
            total_positions = len(positions)
            total_volume = sum(pos.volume for pos in positions)
            total_profit = sum(pos.profit for pos in positions)
            
            # 品种分布
            symbols = list(set(pos.symbol for pos in positions))
            symbol_diversity = len(symbols)
            
            # 方向分布
            buy_positions = len([pos for pos in positions if pos.type == 0])
            sell_positions = len([pos for pos in positions if pos.type == 1])
            
            return {
                'total_positions': total_positions,
                'total_volume': total_volume,
                'total_profit': total_profit,
                'symbol_diversity': symbol_diversity,
                'buy_positions': buy_positions,
                'sell_positions': sell_positions,
                'symbols': symbols
            }
            
        except Exception as e:
            self.logger.error(f"获取投资组合指标错误: {e}")
            return {}
