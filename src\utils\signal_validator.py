"""
信号验证器 - 验证交易信号的有效性
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List


class SignalValidator:
    """信号验证器"""
    
    def __init__(self, filtering_config: Dict):
        self.config = filtering_config
        self.logger = logging.getLogger(__name__)
        
        # 验证规则
        self.min_confidence = filtering_config.get('min_signal_confidence', 0.6)
        self.require_stop_loss = filtering_config.get('require_stop_loss', True)
        self.require_take_profit = filtering_config.get('require_take_profit', False)
        self.max_signal_age = filtering_config.get('max_signal_age_minutes', 5)
    
    def validate(self, signal) -> bool:
        """验证信号"""
        try:
            # 基础验证
            if not self.validate_basic_fields(signal):
                return False
            
            # 置信度验证
            if not self.validate_confidence(signal):
                return False
            
            # 价格逻辑验证
            if not self.validate_price_logic(signal):
                return False
            
            # 时效性验证
            if not self.validate_freshness(signal):
                return False
            
            # 技术过滤验证
            if not self.validate_technical_filters(signal):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证信号错误: {e}")
            return False
    
    def validate_basic_fields(self, signal) -> bool:
        """验证基础字段"""
        # 检查必要字段
        if not signal.symbol or not signal.action:
            self.logger.debug("信号缺少基础字段")
            return False
        
        # 检查交易方向
        if signal.action not in ['BUY', 'SELL']:
            self.logger.debug(f"无效的交易方向: {signal.action}")
            return False
        
        # 检查止损要求
        if self.require_stop_loss and not signal.stop_loss:
            self.logger.debug("信号缺少必需的止损价格")
            return False
        
        # 检查止盈要求
        if self.require_take_profit and not signal.take_profit:
            self.logger.debug("信号缺少必需的止盈价格")
            return False
        
        return True
    
    def validate_confidence(self, signal) -> bool:
        """验证置信度"""
        if signal.confidence < self.min_confidence:
            self.logger.debug(f"信号置信度过低: {signal.confidence}")
            return False
        
        return True
    
    def validate_price_logic(self, signal) -> bool:
        """验证价格逻辑"""
        try:
            entry = signal.entry_price
            sl = signal.stop_loss
            tp = signal.take_profit
            
            # 如果没有价格信息，跳过验证
            if not entry or not sl:
                return True
            
            if signal.action == 'BUY':
                # 买入：止损应该低于入场价
                if sl >= entry:
                    self.logger.debug("买入信号止损价格逻辑错误")
                    return False
                
                # 如果有止盈，应该高于入场价
                if tp and tp <= entry:
                    self.logger.debug("买入信号止盈价格逻辑错误")
                    return False
            
            else:  # SELL
                # 卖出：止损应该高于入场价
                if sl <= entry:
                    self.logger.debug("卖出信号止损价格逻辑错误")
                    return False
                
                # 如果有止盈，应该低于入场价
                if tp and tp >= entry:
                    self.logger.debug("卖出信号止盈价格逻辑错误")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证价格逻辑错误: {e}")
            return False
    
    def validate_freshness(self, signal) -> bool:
        """验证信号时效性"""
        try:
            now = datetime.now()
            signal_age = now - signal.timestamp
            max_age = timedelta(minutes=self.max_signal_age)
            
            if signal_age > max_age:
                self.logger.debug(f"信号过期: {signal_age}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证信号时效性错误: {e}")
            return False
    
    def validate_technical_filters(self, signal) -> bool:
        """验证技术过滤条件"""
        try:
            technical_config = self.config.get('technical_filters', {})
            
            # 趋势确认
            if technical_config.get('trend_confirmation', False):
                if not self.check_trend_confirmation(signal):
                    self.logger.debug("趋势确认失败")
                    return False
            
            # 支撑阻力位确认
            if technical_config.get('support_resistance', False):
                if not self.check_support_resistance(signal):
                    self.logger.debug("支撑阻力位确认失败")
                    return False
            
            # 波动性检查
            if technical_config.get('volatility_check', False):
                if not self.check_volatility(signal):
                    self.logger.debug("波动性检查失败")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"技术过滤验证错误: {e}")
            return True  # 技术过滤失败时不阻止信号
    
    def check_trend_confirmation(self, signal) -> bool:
        """检查趋势确认"""
        # 这里可以添加技术指标确认逻辑
        # 例如：移动平均线、MACD、RSI等
        # 暂时返回True
        return True
    
    def check_support_resistance(self, signal) -> bool:
        """检查支撑阻力位"""
        # 这里可以添加支撑阻力位确认逻辑
        # 例如：关键价位、斐波那契回调等
        # 暂时返回True
        return True
    
    def check_volatility(self, signal) -> bool:
        """检查波动性"""
        # 这里可以添加波动性检查逻辑
        # 例如：ATR、布林带宽度等
        # 暂时返回True
        return True
    
    def calculate_risk_reward_ratio(self, signal) -> float:
        """计算风险回报比"""
        try:
            if not signal.entry_price or not signal.stop_loss or not signal.take_profit:
                return 0.0
            
            if signal.action == 'BUY':
                risk = abs(signal.entry_price - signal.stop_loss)
                reward = abs(signal.take_profit - signal.entry_price)
            else:
                risk = abs(signal.stop_loss - signal.entry_price)
                reward = abs(signal.entry_price - signal.take_profit)
            
            return reward / risk if risk > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"计算风险回报比错误: {e}")
            return 0.0
    
    def validate_risk_reward_ratio(self, signal, min_ratio: float = 1.0) -> bool:
        """验证风险回报比"""
        ratio = self.calculate_risk_reward_ratio(signal)
        
        if ratio < min_ratio:
            self.logger.debug(f"风险回报比过低: {ratio}")
            return False
        
        return True
