#!/usr/bin/env python3
"""
外汇监控系统 - 专注外汇交易对，集成Web面板和模拟交易
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime, timedelta
from pathlib import Path
import re
import threading
from flask import Flask, render_template, jsonify
from flask_socketio import SocketIO, emit

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from bs4 import BeautifulSoup
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False


class ForexMonitorSystem:
    """外汇监控系统"""

    def __init__(self):
        self.logger = self.setup_logger()
        self.base_url = "https://www.forexfactory.com"
        self.trades_url = "https://www.forexfactory.com/trades"

        # 外汇交易对 - 只关注主要外汇对
        self.forex_pairs = {
            'EUR/USD': 'EURUSD',
            'GBP/USD': 'GBPUSD',
            'USD/CAD': 'USDCAD',
            'USD/CHF': 'USDCHF',
            'USD/JPY': 'USDJPY',
            'AUD/USD': 'AUDUSD',
            'NZD/USD': 'NZDUSD'
        }

        # 持久化浏览器会话
        self.driver = None
        self.session_active = False

        # 动态加载交易员配置
        self.target_traders, self.trader_scores = self.load_trader_config()

        # 如果没有配置文件，使用默认配置
        if not self.target_traders:
            self.target_traders = [
                'fusiongoldfx',    # 评分61.0, 100%胜率
                'theTrip75',       # 评分56.0, 100%胜率
                'pipclubtrade',    # 评分20, 黄金专家
                'Dominicus',       # BTC交易员
                'geo1683'          # EUR/USD交易员
            ]

            self.trader_scores = {
                'fusiongoldfx': 61.0,
                'theTrip75': 56.0,
                'pipclubtrade': 20.0,
                'Dominicus': 15.0,
                'geo1683': 15.0
            }

        # 历史交易记录
        self.last_trades_snapshot = {}
        self.live_signals = []

        # 模拟交易系统
        self.virtual_accounts = {}
        self.initialize_virtual_accounts()

        # 监控统计
        self.monitor_stats = {
            'session_start': None,
            'monitoring_cycles': 0,
            'new_trades_detected': 0,
            'signals_generated': 0,
            'successful_captures': 0,
            'forex_signals_only': 0
        }

        # Web应用
        self.app = Flask(__name__, template_folder='templates', static_folder='static')
        self.app.config['SECRET_KEY'] = 'forex-monitor-secret-key'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        self.setup_web_routes()

        # 监控线程
        self.monitor_thread = None
        self.monitoring = False

    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('ForexMonitorSystem')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)

            file_handler = logging.FileHandler('logs/forex_monitor_system.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)

        return logger

    def load_trader_config(self):
        """加载动态交易员配置"""
        try:
            config_file = Path("data/monitor_config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                target_traders = config.get('target_traders', [])
                trader_scores = config.get('trader_scores', {})

                if target_traders:
                    self.logger.info(f"✅ 加载动态交易员配置: {len(target_traders)} 个交易员")
                    return target_traders, trader_scores

        except Exception as e:
            self.logger.error(f"加载交易员配置失败: {e}")

        return [], {}

    def refresh_trader_config(self):
        """刷新交易员配置"""
        new_traders, new_scores = self.load_trader_config()

        if new_traders:
            # 更新交易员列表
            old_traders = set(self.target_traders)
            new_traders_set = set(new_traders)

            added = new_traders_set - old_traders
            removed = old_traders - new_traders_set

            if added or removed:
                self.target_traders = new_traders
                self.trader_scores = new_scores

                # 为新交易员初始化虚拟账户
                for trader in added:
                    if trader not in self.virtual_accounts:
                        self.virtual_accounts[trader] = {
                            'name': trader,
                            'balance': 10000.0,
                            'equity': 10000.0,
                            'margin': 0.0,
                            'free_margin': 10000.0,
                            'trades': [],
                            'total_trades': 0,
                            'winning_trades': 0,
                            'losing_trades': 0,
                            'total_profit': 0.0,
                            'win_rate': 0.0,
                            'max_drawdown': 0.0,
                            'created_at': datetime.now().isoformat()
                        }

                self.logger.info(f"🔄 交易员配置已更新: +{len(added)} -{len(removed)}")

                if added:
                    print(f"➕ 新增交易员: {', '.join(added)}")
                if removed:
                    print(f"➖ 移除交易员: {', '.join(removed)}")

                return True

        return False

    def initialize_virtual_accounts(self):
        """初始化虚拟账户"""
        for trader in self.target_traders:
            self.virtual_accounts[trader] = {
                'name': trader,
                'balance': 10000.0,  # 初始余额10000美元
                'equity': 10000.0,
                'margin': 0.0,
                'free_margin': 10000.0,
                'trades': [],
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'total_profit': 0.0,
                'win_rate': 0.0,
                'max_drawdown': 0.0,
                'created_at': datetime.now().isoformat()
            }

    def create_stealth_driver(self):
        """创建隐身驱动"""
        try:
            self.logger.info("🥷 创建外汇监控隐身驱动...")

            options = uc.ChromeOptions()

            # 最小化检测特征
            options.add_argument('--no-first-run')
            options.add_argument('--no-service-autorun')
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions-except')
            options.add_argument('--disable-plugins-discovery')

            # 语言和地区设置
            options.add_argument('--lang=zh-CN,zh,en-US,en')
            options.add_argument('--accept-lang=zh-CN,zh;q=0.9,en;q=0.8')

            # 窗口设置
            options.add_argument('--window-size=1366,768')

            # 创建驱动
            driver = uc.Chrome(options=options, version_main=None)

            # 反检测脚本
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                    Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                    Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en-US', 'en']});
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {
                            return {
                                commitLoadTime: Date.now() - Math.random() * 1000,
                                finishDocumentLoadTime: Date.now() - Math.random() * 500,
                                finishLoadTime: Date.now() - Math.random() * 200,
                                navigationType: "Other",
                                requestTime: Date.now() - Math.random() * 2000
                            };
                        }
                    };
                '''
            })

            self.logger.info("✅ 外汇监控隐身驱动创建成功")
            return driver

        except Exception as e:
            self.logger.error(f"创建隐身驱动失败: {e}")
            return None

    async def human_like_navigation(self, driver, url):
        """模拟真实人类导航行为 - 慢速版本"""
        self.logger.info("🚶 模拟真实人类导航行为...")

        try:
            # 1. 先访问主页，像真人一样慢慢浏览
            print("🏠 访问ForexFactory主页...")
            driver.get("https://www.forexfactory.com")

            # 真人会花时间阅读页面内容
            reading_time = random.uniform(8, 15)
            print(f"📖 模拟阅读页面内容... ({reading_time:.1f}秒)")
            await asyncio.sleep(reading_time)

            # 2. 模拟真人的鼠标活动 - 慢速移动
            print("🖱️ 模拟真人鼠标活动...")
            driver.execute_script("""
                function simulateHumanMouseMove() {
                    // 模拟真人的鼠标移动轨迹
                    const moves = 5 + Math.floor(Math.random() * 5); // 5-10次移动
                    let currentX = Math.random() * window.innerWidth;
                    let currentY = Math.random() * window.innerHeight;

                    for(let i = 0; i < moves; i++) {
                        setTimeout(() => {
                            // 模拟真人的渐进式移动
                            const targetX = Math.random() * window.innerWidth;
                            const targetY = Math.random() * window.innerHeight;

                            // 创建鼠标移动事件
                            const event = new MouseEvent('mousemove', {
                                view: window,
                                bubbles: true,
                                cancelable: true,
                                clientX: targetX,
                                clientY: targetY
                            });
                            document.dispatchEvent(event);

                            currentX = targetX;
                            currentY = targetY;
                        }, i * (800 + Math.random() * 1200)); // 0.8-2秒间隔
                    }
                }

                simulateHumanMouseMove();
            """)

            # 等待鼠标活动完成
            mouse_activity_time = random.uniform(3, 6)
            await asyncio.sleep(mouse_activity_time)

            # 3. 模拟真人思考时间
            thinking_time = random.uniform(2, 5)
            print(f"🤔 模拟思考时间... ({thinking_time:.1f}秒)")
            await asyncio.sleep(thinking_time)

            # 4. 现在访问目标页面
            print(f"🎯 导航到目标页面...")
            driver.get(url)

            # 5. 真人会等待页面完全加载并观察
            loading_time = random.uniform(5, 10)
            print(f"⏳ 等待页面加载并观察... ({loading_time:.1f}秒)")
            await asyncio.sleep(loading_time)

            # 6. 模拟页面滚动 - 真人会滚动查看内容
            print("📜 模拟页面滚动...")
            driver.execute_script("""
                // 模拟真人的滚动行为
                const scrolls = 2 + Math.floor(Math.random() * 3); // 2-5次滚动
                let currentScroll = 0;

                for(let i = 0; i < scrolls; i++) {
                    setTimeout(() => {
                        const scrollAmount = 100 + Math.random() * 300; // 100-400px
                        currentScroll += scrollAmount;

                        window.scrollTo({
                            top: currentScroll,
                            behavior: 'smooth'
                        });

                        // 滚动后停留观察
                        setTimeout(() => {
                            // 可能滚回一点
                            if(Math.random() > 0.7) {
                                window.scrollTo({
                                    top: currentScroll - 50,
                                    behavior: 'smooth'
                                });
                            }
                        }, 1000 + Math.random() * 2000);

                    }, i * (2000 + Math.random() * 3000)); // 2-5秒间隔
                }
            """)

            # 等待滚动活动完成
            scroll_time = random.uniform(4, 8)
            await asyncio.sleep(scroll_time)

            print("✅ 真人导航行为模拟完成")
            return True

        except Exception as e:
            self.logger.error(f"人类导航模拟失败: {e}")
            return False

    async def initialize_session(self):
        """初始化会话"""
        print("🚀 初始化外汇监控会话...")

        self.driver = self.create_stealth_driver()
        if not self.driver:
            return False

        try:
            # 人类化导航
            nav_success = await self.human_like_navigation(self.driver, self.trades_url)
            if not nav_success:
                print("❌ 导航失败")
                return False

            # 等待页面完全加载
            await asyncio.sleep(random.uniform(3, 5))

            # 验证页面是否正常加载
            page_source = self.driver.page_source.lower()
            if 'trade' in page_source and 'forexfactory' in self.driver.current_url:
                self.session_active = True
                self.monitor_stats['session_start'] = datetime.now().isoformat()
                print("🎉 外汇监控会话初始化成功！")
                return True
            else:
                print("❌ 页面加载异常")
                return False

        except Exception as e:
            self.logger.error(f"会话初始化失败: {e}")
            return False

    def is_forex_pair(self, symbol):
        """检查是否为外汇交易对"""
        if not symbol:
            return False

        # 检查是否在我们的外汇对列表中
        for forex_symbol in self.forex_pairs.keys():
            if symbol.upper() == forex_symbol or symbol.upper() == self.forex_pairs[forex_symbol]:
                return True

        return False

    def normalize_forex_symbol(self, symbol):
        """标准化外汇符号"""
        if not symbol:
            return None

        symbol = symbol.upper()

        # 如果已经是标准格式，直接返回
        if symbol in self.forex_pairs.values():
            return symbol

        # 如果是带斜杠的格式，转换为标准格式
        for forex_symbol, standard_symbol in self.forex_pairs.items():
            if symbol == forex_symbol:
                return standard_symbol

        return None

    async def capture_current_trades(self):
        """捕获当前交易数据 - 只关注外汇，真人速度"""
        if not self.session_active or not self.driver:
            return []

        try:
            # 真人刷新页面的行为
            print("🔄 刷新页面获取最新数据...")
            self.driver.refresh()

            # 真人会等待页面完全加载
            loading_wait = random.uniform(6, 12)
            print(f"⏳ 等待页面加载... ({loading_wait:.1f}秒)")
            await asyncio.sleep(loading_wait)

            # 检查页面状态
            page_source = self.driver.page_source.lower()
            if 'cloudflare' in page_source or '验证您是真人' in page_source:
                print("⚠️  遇到Cloudflare验证，跳过本轮")
                return []

            # 真人会先观察页面内容
            observation_time = random.uniform(3, 7)
            print(f"👀 观察页面内容... ({observation_time:.1f}秒)")
            await asyncio.sleep(observation_time)

            # 解析页面数据
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            current_trades = []

            # 查找表格数据
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 4:
                        content = [cell.get_text().strip() for cell in cells]

                        # 跳过表头
                        if content[0] == 'Trade' or 'Saving' in content[0]:
                            continue

                        # 解析交易信息
                        trade_info = self.parse_trade_info(content)
                        if (trade_info and
                            trade_info['trader'] in self.target_traders and
                            self.is_forex_pair(trade_info.get('symbol'))):  # 只要外汇对
                            current_trades.append(trade_info)

            self.monitor_stats['successful_captures'] += 1
            return current_trades

        except Exception as e:
            self.logger.error(f"捕获交易数据失败: {e}")
            return []

    def parse_trade_info(self, content):
        """解析交易信息 - 专注外汇对"""
        try:
            trade_text = content[0]
            trader_name = content[1]
            return_text = content[2]
            pips_text = content[3]

            # 解析外汇交易对
            forex_patterns = [
                r'(EUR/USD)', r'(GBP/USD)', r'(USD/CAD)',
                r'(USD/CHF)', r'(USD/JPY)', r'(AUD/USD)', r'(NZD/USD)'
            ]

            symbol = None
            for pattern in forex_patterns:
                match = re.search(pattern, trade_text, re.IGNORECASE)
                if match:
                    symbol = self.normalize_forex_symbol(match.group(1))
                    break

            # 如果不是外汇对，跳过
            if not symbol:
                return None

            # 解析交易方向
            action = None
            if re.search(r'\b(BUY|Long)\b', trade_text, re.IGNORECASE):
                action = 'BUY'
            elif re.search(r'\b(SELL|Short)\b', trade_text, re.IGNORECASE):
                action = 'SELL'

            # 解析状态
            status = 'unknown'
            if 'Opened' in trade_text:
                status = 'open'
            elif 'Closed' in trade_text:
                status = 'closed'

            # 解析时间
            time_match = re.search(r'(\d+)\s*min\s*ago', trade_text)
            minutes_ago = int(time_match.group(1)) if time_match else 0

            # 解析价格
            price_matches = re.findall(r'(\d+\.?\d*)', trade_text)
            entry_price = None
            if price_matches:
                try:
                    entry_price = float(price_matches[-1])
                except:
                    entry_price = None

            if symbol and action and trader_name:
                return {
                    'symbol': symbol,
                    'action': action,
                    'trader': trader_name,
                    'status': status,
                    'minutes_ago': minutes_ago,
                    'timestamp': datetime.now() - timedelta(minutes=minutes_ago),
                    'entry_price': entry_price,
                    'raw_text': trade_text,
                    'trade_id': f"{trader_name}_{symbol}_{action}_{minutes_ago}",
                    'is_forex': True
                }

        except Exception as e:
            pass

        return None

    def detect_new_trades(self, current_trades):
        """检测新交易"""
        new_trades = []

        # 创建当前交易的快照
        current_snapshot = {}
        for trade in current_trades:
            trader = trade['trader']
            if trader not in current_snapshot:
                current_snapshot[trader] = []
            current_snapshot[trader].append(trade)

        # 与上次快照比较
        for trader, trades in current_snapshot.items():
            last_trades = self.last_trades_snapshot.get(trader, [])

            for trade in trades:
                # 检查是否是新交易（10分钟内且之前没有）
                if trade['minutes_ago'] <= 10:
                    is_new = True
                    for last_trade in last_trades:
                        if (last_trade.get('symbol') == trade['symbol'] and
                            last_trade.get('action') == trade['action'] and
                            abs(last_trade.get('minutes_ago', 999) - trade['minutes_ago']) <= 2):
                            is_new = False
                            break

                    if is_new:
                        new_trades.append(trade)

        # 更新快照
        self.last_trades_snapshot = current_snapshot

        return new_trades

    def generate_forex_signals(self, new_trades):
        """生成外汇信号"""
        signals = []

        for trade in new_trades:
            if trade['status'] == 'open' and trade.get('is_forex'):  # 只为外汇开仓交易生成信号
                trader_name = trade['trader']
                score = self.trader_scores.get(trader_name, 10.0)
                confidence = min(0.9, max(0.3, score / 100))

                signal = {
                    'id': f"forex_{trade['trade_id']}_{int(datetime.now().timestamp())}",
                    'timestamp': datetime.now().isoformat(),
                    'source': f'FF_Forex_{trader_name}',
                    'symbol': trade['symbol'],
                    'action': trade['action'],
                    'entry_price': trade.get('entry_price'),
                    'confidence': confidence,
                    'signal_type': 'forex_follow',
                    'trader_score': score,
                    'trader_name': trader_name,
                    'minutes_since_entry': trade['minutes_ago'],
                    'is_real': True,
                    'platform': 'ForexFactory_Forex',
                    'urgency': 'high' if trade['minutes_ago'] < 5 else 'medium',
                    'lot_size': 0.01,  # 默认手数
                    'stop_loss': 0,
                    'take_profit': 0
                }

                signals.append(signal)
                self.monitor_stats['forex_signals_only'] += 1

        return signals

    def execute_virtual_trade(self, signal):
        """执行虚拟交易"""
        trader_name = signal['trader_name']

        if trader_name not in self.virtual_accounts:
            return False

        account = self.virtual_accounts[trader_name]

        # 创建虚拟交易
        virtual_trade = {
            'id': signal['id'],
            'symbol': signal['symbol'],
            'action': signal['action'],
            'entry_price': signal.get('entry_price', 1.0),
            'lot_size': signal.get('lot_size', 0.01),
            'entry_time': datetime.now().isoformat(),
            'status': 'open',
            'profit': 0.0,
            'pips': 0.0,
            'confidence': signal['confidence']
        }

        # 添加到账户交易列表
        account['trades'].append(virtual_trade)
        account['total_trades'] += 1

        # 计算保证金（简化计算）
        margin_required = virtual_trade['entry_price'] * virtual_trade['lot_size'] * 100000 * 0.01  # 1%保证金
        account['margin'] += margin_required
        account['free_margin'] = account['balance'] - account['margin']

        self.logger.info(f"✅ 为 {trader_name} 执行虚拟交易: {signal['symbol']} {signal['action']}")

        return True

    def update_virtual_accounts(self):
        """更新虚拟账户（模拟价格变动）"""
        for trader_name, account in self.virtual_accounts.items():
            total_profit = 0.0

            for trade in account['trades']:
                if trade['status'] == 'open':
                    # 模拟价格变动（随机）
                    price_change = random.uniform(-0.01, 0.01)  # 随机价格变动

                    if trade['action'] == 'BUY':
                        trade['profit'] = price_change * trade['lot_size'] * 100000
                    else:  # SELL
                        trade['profit'] = -price_change * trade['lot_size'] * 100000

                    # 计算点数
                    trade['pips'] = price_change * 10000

                    total_profit += trade['profit']

                    # 简单的平仓逻辑（盈利>50或亏损>-30）
                    if trade['profit'] > 50 or trade['profit'] < -30:
                        trade['status'] = 'closed'
                        trade['close_time'] = datetime.now().isoformat()

            # 更新账户统计
            account['equity'] = account['balance'] + total_profit
            account['total_profit'] = sum([t['profit'] for t in account['trades'] if t['status'] == 'closed'])
            account['winning_trades'] = len([t for t in account['trades'] if t['status'] == 'closed' and t['profit'] > 0])
            account['losing_trades'] = len([t for t in account['trades'] if t['status'] == 'closed' and t['profit'] < 0])

            if account['total_trades'] > 0:
                account['win_rate'] = (account['winning_trades'] / account['total_trades']) * 100

            # 计算最大回撤
            if account['equity'] < account['balance']:
                drawdown = ((account['balance'] - account['equity']) / account['balance']) * 100
                account['max_drawdown'] = max(account['max_drawdown'], drawdown)

    def save_signals_and_trades(self, signals):
        """保存信号和交易数据"""
        if not signals:
            return

        try:
            Path("logs").mkdir(exist_ok=True)
            Path("MQL4/Files").mkdir(parents=True, exist_ok=True)

            # 保存外汇信号
            with open('logs/forex_signals.json', 'a', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False, default=str) + '\n')

            # 保存最新信号
            with open('logs/latest_forex_signals.json', 'w', encoding='utf-8') as f:
                json.dump(signals, f, indent=2, ensure_ascii=False, default=str)

            # 保存虚拟账户数据
            with open('logs/virtual_accounts.json', 'w', encoding='utf-8') as f:
                json.dump(self.virtual_accounts, f, indent=2, ensure_ascii=False, default=str)

            # 保存到CSV格式
            import csv
            with open('MQL4/Files/forex_signals.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['timestamp', 'symbol', 'action', 'entry_price', 'stop_loss', 'take_profit', 'lot_size', 'source'])
                for signal in signals:
                    writer.writerow([
                        signal['timestamp'],
                        signal['symbol'],
                        signal['action'],
                        signal.get('entry_price', 0),
                        signal.get('stop_loss', 0),
                        signal.get('take_profit', 0),
                        signal.get('lot_size', 0.01),
                        signal['source']
                    ])

            self.logger.info(f"✅ 已保存 {len(signals)} 个外汇信号")

        except Exception as e:
            self.logger.error(f"保存信号失败: {e}")

    def setup_web_routes(self):
        """设置Web路由"""

        @self.app.route('/')
        def index():
            """主页"""
            return render_template('forex_dashboard.html')

        @self.app.route('/api/account_info')
        def get_account_info():
            """获取账户信息"""
            return jsonify({
                'accounts': self.virtual_accounts,
                'total_accounts': len(self.virtual_accounts),
                'total_balance': sum([acc['balance'] for acc in self.virtual_accounts.values()]),
                'total_equity': sum([acc['equity'] for acc in self.virtual_accounts.values()]),
                'total_profit': sum([acc['total_profit'] for acc in self.virtual_accounts.values()]),
                'last_update': datetime.now().isoformat()
            })

        @self.app.route('/api/signals')
        def get_signals():
            """获取最新信号"""
            return jsonify({
                'signals': self.live_signals[-20:],  # 最新20个信号
                'total_signals': len(self.live_signals),
                'last_update': datetime.now().isoformat()
            })

        @self.app.route('/api/monitor_stats')
        def get_monitor_stats():
            """获取监控统计"""
            return jsonify(self.monitor_stats)

        @self.app.route('/api/trader_performance')
        def get_trader_performance():
            """获取交易员表现"""
            performance_data = []

            for trader_name, account in self.virtual_accounts.items():
                performance_data.append({
                    'name': trader_name,
                    'score': self.trader_scores.get(trader_name, 0),
                    'balance': account['balance'],
                    'equity': account['equity'],
                    'profit': account['total_profit'],
                    'win_rate': account['win_rate'],
                    'total_trades': account['total_trades'],
                    'winning_trades': account['winning_trades'],
                    'losing_trades': account['losing_trades'],
                    'max_drawdown': account['max_drawdown']
                })

            # 按收益排序
            performance_data.sort(key=lambda x: x['profit'], reverse=True)

            return jsonify({
                'traders': performance_data,
                'last_update': datetime.now().isoformat()
            })

    async def monitor_cycle(self):
        """单次监控周期"""
        self.monitor_stats['monitoring_cycles'] += 1
        cycle_num = self.monitor_stats['monitoring_cycles']

        print(f"💱 第 {cycle_num} 轮外汇监控...")

        try:
            # 捕获当前交易
            current_trades = await self.capture_current_trades()

            if current_trades:
                print(f"📊 捕获到 {len(current_trades)} 个外汇交易")

                # 检测新交易
                new_trades = self.detect_new_trades(current_trades)

                if new_trades:
                    self.monitor_stats['new_trades_detected'] += len(new_trades)

                    print(f"🆕 发现 {len(new_trades)} 个新外汇交易:")
                    for trade in new_trades:
                        print(f"   {trade['trader']}: {trade['symbol']} {trade['action']} ({trade['minutes_ago']}分钟前)")

                    # 生成外汇信号
                    signals = self.generate_forex_signals(new_trades)

                    if signals:
                        self.monitor_stats['signals_generated'] += len(signals)
                        self.live_signals.extend(signals)

                        # 执行虚拟交易
                        for signal in signals:
                            self.execute_virtual_trade(signal)

                        # 保存数据
                        self.save_signals_and_trades(signals)

                        print(f"💱 生成 {len(signals)} 个外汇跟单信号:")
                        for signal in signals:
                            urgency = "🔥" if signal['urgency'] == 'high' else "⚡"
                            print(f"   {urgency} {signal['symbol']} {signal['action']} - 置信度: {signal['confidence']*100:.0f}%")

                        # 通过WebSocket推送到前端
                        self.socketio.emit('new_signals', {
                            'signals': signals,
                            'timestamp': datetime.now().isoformat()
                        })

                else:
                    print("⚪ 无新外汇交易")

            else:
                print("⚠️  数据捕获失败")

            # 更新虚拟账户
            self.update_virtual_accounts()

            # 推送账户更新到前端
            self.socketio.emit('account_update', {
                'accounts': self.virtual_accounts,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"监控周期 {cycle_num} 出错: {e}")
            print(f"❌ 第 {cycle_num} 轮监控出错: {e}")

    def start_monitoring_thread(self, interval_minutes=5):
        """启动监控线程"""
        async def monitor_loop():
            # 初始化会话
            if not await self.initialize_session():
                print("❌ 外汇监控会话初始化失败")
                return

            print(f"💱 外汇监控系统已启动")
            print(f"🎯 监控外汇对: {', '.join(self.forex_pairs.values())}")
            print(f"👥 监控交易员: {', '.join(self.target_traders)}")
            print(f"⏰ 监控间隔: {interval_minutes} 分钟")

            self.monitoring = True

            while self.monitoring:
                try:
                    await self.monitor_cycle()

                    # 显示统计
                    stats = self.monitor_stats
                    print(f"📊 统计: 周期{stats['monitoring_cycles']} | 新交易{stats['new_trades_detected']} | 外汇信号{stats['forex_signals_only']}")

                    await asyncio.sleep(interval_minutes * 60)

                except Exception as e:
                    self.logger.error(f"监控循环出错: {e}")
                    await asyncio.sleep(30)  # 出错后等待30秒再继续

        def run_monitor():
            asyncio.run(monitor_loop())

        self.monitor_thread = threading.Thread(target=run_monitor, daemon=True)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass

    def run_web_server(self, host='127.0.0.1', port=5000, interval_minutes=5):
        """运行Web服务器"""
        print("💱 外汇监控系统启动")
        print("="*50)
        print("🎯 专注外汇交易对")
        print("📊 实时模拟交易")
        print("🌐 Web监控面板")
        print("="*50)

        # 启动监控线程
        self.start_monitoring_thread(interval_minutes)

        print(f"\n🌐 Web面板启动: http://{host}:{port}")
        print("🔄 按 Ctrl+C 停止系统")

        try:
            self.socketio.run(self.app, host=host, port=port, debug=False)
        except KeyboardInterrupt:
            print("\n⏹️  停止外汇监控系统")
            self.stop_monitoring()


async def main():
    """主函数"""
    if not SELENIUM_AVAILABLE:
        print("❌ 需要安装依赖:")
        print("pip install undetected-chromedriver selenium beautifulsoup4 flask flask-socketio")
        return

    monitor = ForexMonitorSystem()

    print("💱 外汇监控系统配置")
    print("="*50)
    print("🎯 监控外汇对:")
    for i, (pair, symbol) in enumerate(monitor.forex_pairs.items(), 1):
        print(f"   {i}. {pair} ({symbol})")

    print(f"\n👥 监控交易员:")
    for i, trader in enumerate(monitor.target_traders, 1):
        score = monitor.trader_scores.get(trader, 0)
        print(f"   {i}. {trader} (评分: {score})")

    print("\n🚀 选择监控间隔:")
    print("1. 极速监控 (30秒) - 最快安全速度")
    print("2. 快速监控 (1分钟) - 推荐")
    print("3. 标准监控 (2分钟)")
    print("4. 慢速监控 (5分钟)")

    while True:
        choice = input("\n请选择 (1-4): ").strip()
        if choice in ['1', '2', '3', '4']:
            break
        print("❌ 请输入有效选项 (1-4)")

    intervals = {'1': 0.5, '2': 1, '3': 2, '4': 5}  # 0.5分钟 = 30秒
    interval = intervals[choice]

    print(f"✅ 选择了 {interval} 分钟间隔监控")

    # 运行Web服务器
    monitor.run_web_server(interval_minutes=interval)


if __name__ == "__main__":
    asyncio.run(main())