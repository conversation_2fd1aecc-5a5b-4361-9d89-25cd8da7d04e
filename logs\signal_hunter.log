2025-07-28 12:53:01,144 - INFO - 🔥 启动持续信号猎取模式...
2025-07-28 12:53:01,144 - INFO - 🚀 开始全面信号猎取...
2025-07-28 12:53:01,144 - INFO - 🎯 猎取Reddit专业外汇信号...
2025-07-28 12:53:04,140 - ERROR - 创建隐身驱动失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x1161af3+62339]
	GetHandleVerifier [0x0x1161b34+62404]
	(No symbol) [0x0xfa2123]
	(No symbol) [0x0xfc9693]
	(No symbol) [0x0xfcae20]
	(No symbol) [0x0xfc5f5a]
	(No symbol) [0x0x1019782]
	(No symbol) [0x0x101926c]
	(No symbol) [0x0x101a960]
	(No symbol) [0x0x101a76a]
	(No symbol) [0x0x100f1b6]
	(No symbol) [0x0xfde7a2]
	(No symbol) [0x0xfdf644]
	GetHandleVerifier [0x0x13d6683+2637587]
	GetHandleVerifier [0x0x13d1a8a+2618138]
	GetHandleVerifier [0x0x118856a+220666]
	GetHandleVerifier [0x0x1178998+156200]
	GetHandleVerifier [0x0x117f12d+182717]
	GetHandleVerifier [0x0x1169a38+94920]
	GetHandleVerifier [0x0x1169bc2+95314]
	GetHandleVerifier [0x0x1154d0a+9626]
	BaseThreadInitThunk [0x0x765f5d49+25]
	RtlInitializeExceptionChain [0x0x7736d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7736d281+561]

2025-07-28 12:53:04,147 - INFO - 🎯 猎取Twitter外汇专家信号...
2025-07-28 12:53:10,058 - ERROR - 创建隐身驱动失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x661af3+62339]
	GetHandleVerifier [0x0x661b34+62404]
	(No symbol) [0x0x4a2123]
	(No symbol) [0x0x4c9693]
	(No symbol) [0x0x4cae20]
	(No symbol) [0x0x4c5f5a]
	(No symbol) [0x0x519782]
	(No symbol) [0x0x51926c]
	(No symbol) [0x0x51a960]
	(No symbol) [0x0x51a76a]
	(No symbol) [0x0x50f1b6]
	(No symbol) [0x0x4de7a2]
	(No symbol) [0x0x4df644]
	GetHandleVerifier [0x0x8d6683+2637587]
	GetHandleVerifier [0x0x8d1a8a+2618138]
	GetHandleVerifier [0x0x68856a+220666]
	GetHandleVerifier [0x0x678998+156200]
	GetHandleVerifier [0x0x67f12d+182717]
	GetHandleVerifier [0x0x669a38+94920]
	GetHandleVerifier [0x0x669bc2+95314]
	GetHandleVerifier [0x0x654d0a+9626]
	BaseThreadInitThunk [0x0x765f5d49+25]
	RtlInitializeExceptionChain [0x0x7736d2fb+107]
	RtlGetAppContainerNamedObjectPath [0x0x7736d281+561]

2025-07-28 12:53:10,058 - INFO - 🎯 演示Telegram付费频道猎取方法...
2025-07-28 12:53:10,059 - INFO - 💡 Telegram付费频道配置示例已保存到 config/telegram_premium_demo.json
2025-07-28 12:53:10,059 - INFO - 📋 要捕获真实付费信号，需要:
2025-07-28 12:53:10,059 - INFO -    1. 加入付费Telegram频道
2025-07-28 12:53:10,059 - INFO -    2. 配置登录凭据
2025-07-28 12:53:10,059 - INFO -    3. 使用Selenium自动化登录
2025-07-28 12:53:10,059 - INFO - 🎯 猎取完成: 0 个信号
