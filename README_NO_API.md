# 🚀 MT4智能跟单系统 (无API版本)

## 🎉 完全免费！无需任何付费API！

这是一个**完全免费**的MT4自动跟单系统，不需要任何付费API，直接从公开渠道获取交易信号。

## ✨ 核心优势

### 💰 完全免费
- ✅ 无需MetaAPI付费订阅
- ✅ 无需Telegram Bot API
- ✅ 无需Myfxbook付费账户
- ✅ 所有功能完全免费使用

### 🔄 直接通信
- ✅ 直接与MT4通信，无中间商
- ✅ 通过文件系统交换数据
- ✅ 实时信号处理
- ✅ 稳定可靠的连接

### 🕷️ 智能抓取
- ✅ 从公开Telegram频道获取信号
- ✅ 自动解析多种信号格式
- ✅ 支持中英文信号识别
- ✅ 过滤垃圾和无效信号

### 🛡️ 风险控制
- ✅ 内置风险管理系统
- ✅ 可配置的仓位大小
- ✅ 止损保护机制
- ✅ 实时监控面板

## 🚀 快速开始

### 第一步：运行设置
```bash
python start_no_api.py
```

### 第二步：安装MT4 EA
1. 系统会自动生成 `AutoCopyTrader.mq4` 文件
2. 将此文件复制到 MT4 的 `MQL4/Experts` 目录
3. 在MT4中编译并启动EA
4. 启用自动交易功能

### 第三步：启动服务
```bash
# 选择菜单选项
1. 启动信号抓取器  # 获取交易信号
2. 启动监控面板    # 查看系统状态
3. 同时启动两者    # 推荐选择
```

### 第四步：开始跟单
- 系统自动从免费渠道获取信号
- MT4 EA自动执行交易
- 通过Web面板监控状态

## 📊 系统架构

```
免费信号源 → 信号抓取器 → CSV文件 → MT4 EA → 自动交易
     ↓
  监控面板 ← 状态文件 ← MT4 EA
```

### 🔄 工作流程
1. **信号抓取器** 从公开Telegram频道抓取信号
2. **信号解析** 自动识别交易品种、方向、价格
3. **文件通信** 通过CSV文件传递信号给MT4
4. **EA执行** MT4专家顾问自动执行交易
5. **状态监控** Web面板实时显示系统状态

## 📁 文件说明

### 核心文件
- `start_no_api.py` - 主启动脚本
- `no_api_setup.py` - 系统设置脚本
- `free_signal_scraper.py` - 免费信号抓取器
- `simple_dashboard.py` - 简化版监控面板
- `AutoCopyTrader.mq4` - MT4专家顾问

### 配置文件
- `config/no_api_config.json` - 系统配置
- `MQL4/Files/trading_signals.csv` - 交易信号文件
- `MQL4/Files/trading_status.txt` - 系统状态文件
- `MQL4/Files/account_info.txt` - 账户信息文件

## 🔧 详细设置

### MT4设置步骤
1. **复制EA文件**
   ```
   将 AutoCopyTrader.mq4 复制到:
   MT4安装目录/MQL4/Experts/
   ```

2. **编译EA**
   - 在MT4中按 `F4` 打开MetaEditor
   - 打开 `AutoCopyTrader.mq4` 文件
   - 按 `F7` 编译EA

3. **启动EA**
   - 将EA拖拽到任意图表上
   - 在参数设置中调整手数等参数
   - 勾选"允许自动交易"
   - 点击"确定"启动

4. **启用自动交易**
   - 在MT4工具栏点击"自动交易"按钮
   - 确保按钮变为绿色激活状态

### 信号源配置
系统默认监控以下免费信号源：
- 免费外汇信号频道
- 黄金交易信号频道
- 其他公开交易频道

可以在 `config/no_api_config.json` 中添加更多信号源。

## 📈 监控面板

访问 `http://localhost:8080` 查看：

### 实时数据
- 账户余额和净值
- 当前持仓情况
- 今日盈亏统计
- 系统运行状态

### 信号监控
- 最近接收的信号
- 信号处理状态
- 交易执行结果
- 错误日志信息

## ⚙️ 参数调整

### EA参数
在MT4中可以调整以下参数：
- `LotSize` - 基础手数 (默认: 0.01)
- `MaxSpread` - 最大点差 (默认: 5)
- `EnableTrading` - 启用交易 (默认: true)

### 风险参数
在配置文件中调整：
- `max_risk_per_trade` - 单笔最大风险
- `max_daily_risk` - 每日最大风险
- `base_lot_size` - 基础手数

## 🔍 故障排除

### 常见问题

1. **EA无法启动**
   ```
   解决方案:
   - 检查EA是否正确编译
   - 确保启用了自动交易
   - 查看MT4专家日志
   ```

2. **无法获取信号**
   ```
   解决方案:
   - 检查网络连接
   - 确认信号抓取器正在运行
   - 查看信号文件是否生成
   ```

3. **交易无法执行**
   ```
   解决方案:
   - 检查账户余额
   - 确认交易时间
   - 查看点差是否过大
   ```

### 日志查看
- MT4专家日志: MT4菜单 → 工具 → 选项 → 专家顾问
- 系统日志: `logs/` 目录下的日志文件
- 状态文件: `MQL4/Files/trading_status.txt`

## ⚠️ 重要提醒

### 风险提示
1. **模拟测试**: 建议先在模拟账户充分测试
2. **小额开始**: 真实交易请从小额资金开始
3. **定期监控**: 定期检查系统运行状态
4. **风险控制**: 设置合理的风险参数

### 法律声明
1. **投资风险**: 外汇交易存在高风险
2. **软件免责**: 软件仅供学习和研究使用
3. **盈亏自负**: 使用本软件产生的盈亏由用户承担
4. **合规使用**: 请确保在您的地区使用是合法的

## 🆚 版本对比

| 功能 | 无API版本 | 完整版本 |
|------|-----------|----------|
| 成本 | 完全免费 | 需要API费用 |
| 信号源 | 公开渠道 | 付费优质源 |
| 信号质量 | 中等 | 高质量 |
| 设置难度 | 简单 | 中等 |
| 功能完整性 | 基础功能 | 全部功能 |
| 适用人群 | 新手/预算有限 | 专业交易者 |

## 🎯 适用人群

### 👍 适合使用
- 外汇交易新手
- 预算有限的交易者
- 想要学习自动交易的用户
- 不想支付API费用的用户

### 👎 不太适合
- 需要高质量信号的专业交易者
- 大资金量的交易者
- 对信号延迟敏感的用户

## 📞 获取帮助

### 自助解决
1. 查看本文档的故障排除部分
2. 运行系统测试: `python start_no_api.py` → 选择7
3. 查看日志文件了解错误信息

### 社区支持
- 加入交流群组讨论问题
- 分享使用经验和技巧
- 获取最新版本更新

---

## 🎉 开始你的免费跟单之旅！

```bash
# 一键启动，完全免费！
python start_no_api.py
```

**祝您交易顺利，收益满满！** 💰🚀

---

*免责声明: 本软件仅供学习和研究使用，交易有风险，投资需谨慎。*
