#!/usr/bin/env python3
"""
ForexFactory研究系统 - 完整的FF交易员研究和信号生成系统
"""

import asyncio
import subprocess
import sys
import time
from pathlib import Path
import json


class FFResearchSystem:
    """ForexFactory研究系统"""
    
    def __init__(self):
        self.system_status = {
            'hunter_completed': False,
            'analysis_completed': False,
            'signals_generated': False,
            'total_traders': 0,
            'total_signals': 0
        }
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("🎯 ForexFactory交易员研究系统")
        print("="*60)
        print("📊 ForexFactory是全球最大的外汇交易社区")
        print("🏆 拥有数万名真实交易员和他们的交易记录")
        print("💎 我们将深入研究顶级交易员的策略和信号")
        print("="*60)
        print("\n🔬 研究流程:")
        print("1. 🕷️  猎取交易员 - 扫描和收集交易员信息")
        print("2. 📊 分析交易员 - 深度分析交易模式和表现")
        print("3. 🎯 生成信号 - 基于顶级交易员生成高质量信号")
        print("4. 📋 生成报告 - 详细的研究报告和统计")
        print("="*60)
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔧 检查系统依赖...")
        
        required_packages = [
            'selenium', 'beautifulsoup4', 'requests', 'pandas', 'matplotlib'
        ]
        
        missing = []
        for package in required_packages:
            try:
                if package == 'beautifulsoup4':
                    import bs4
                else:
                    __import__(package.replace('-', '_'))
            except ImportError:
                missing.append(package)
        
        if missing:
            print(f"📦 需要安装依赖: {', '.join(missing)}")
            choice = input("是否自动安装? (y/n): ").strip().lower()
            
            if choice == 'y':
                try:
                    subprocess.run([sys.executable, "-m", "pip", "install"] + missing, 
                                  check=True)
                    print("✅ 依赖安装完成")
                    return True
                except subprocess.CalledProcessError:
                    print("❌ 依赖安装失败，请手动安装")
                    return False
            else:
                print("⚠️  请手动安装依赖后再运行")
                return False
        else:
            print("✅ 所有依赖已安装")
            return True
    
    def check_chrome_driver(self):
        """检查Chrome驱动"""
        print("🌐 检查Chrome浏览器...")
        
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(options=options)
            driver.quit()
            
            print("✅ Chrome驱动正常")
            return True
            
        except Exception as e:
            print(f"❌ Chrome驱动问题: {e}")
            print("💡 请确保已安装Chrome浏览器和ChromeDriver")
            print("📋 安装指南:")
            print("   1. 下载Chrome浏览器")
            print("   2. 下载ChromeDriver: https://chromedriver.chromium.org/")
            print("   3. 将ChromeDriver添加到系统PATH")
            return False
    
    async def run_trader_hunter(self):
        """运行交易员猎手"""
        print("\n🕷️  第1步: 启动ForexFactory交易员猎手...")
        print("⏰ 预计耗时: 5-10分钟")
        print("💡 系统将自动处理反爬虫机制")
        
        try:
            # 导入并运行猎手
            from forexfactory_trader_hunter import ForexFactoryTraderHunter
            
            hunter = ForexFactoryTraderHunter()
            await hunter.hunt_ff_traders()
            
            # 检查结果
            database_file = Path("data/ff_traders_database.json")
            if database_file.exists():
                with open(database_file, 'r', encoding='utf-8') as f:
                    traders_data = json.load(f)
                
                self.system_status['hunter_completed'] = True
                self.system_status['total_traders'] = len(traders_data)
                
                print(f"✅ 交易员猎取完成: 发现 {len(traders_data)} 个交易员")
                return True
            else:
                print("❌ 交易员猎取失败: 未生成数据库文件")
                return False
                
        except Exception as e:
            print(f"❌ 交易员猎取异常: {e}")
            return False
    
    def run_trader_analyzer(self):
        """运行交易员分析器"""
        print("\n📊 第2步: 启动交易员分析器...")
        
        try:
            from ff_trader_analyzer import FFTraderAnalyzer
            
            analyzer = FFTraderAnalyzer()
            analyzer.run_full_analysis()
            
            # 检查分析结果
            signals_file = Path("logs/ff_analysis_signals.json")
            if signals_file.exists():
                with open(signals_file, 'r', encoding='utf-8') as f:
                    signals = f.readlines()
                
                self.system_status['analysis_completed'] = True
                self.system_status['signals_generated'] = True
                self.system_status['total_signals'] = len(signals)
                
                print(f"✅ 分析完成: 生成 {len(signals)} 个高质量信号")
                return True
            else:
                print("⚠️  分析完成但未生成信号文件")
                self.system_status['analysis_completed'] = True
                return True
                
        except Exception as e:
            print(f"❌ 分析过程异常: {e}")
            return False
    
    def generate_final_report(self):
        """生成最终报告"""
        print("\n📋 第3步: 生成最终研究报告...")
        
        try:
            # 收集所有数据
            report_data = {
                'research_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'system_status': self.system_status,
                'files_generated': []
            }
            
            # 检查生成的文件
            files_to_check = [
                ('data/ff_traders_database.json', '交易员数据库'),
                ('logs/ff_analysis_signals.json', '分析信号'),
                ('reports/ff_trader_analysis.json', '详细分析报告'),
                ('reports/ff_analysis_summary.json', '摘要报告'),
                ('logs/ff_trader_hunter.log', '猎取日志')
            ]
            
            for file_path, description in files_to_check:
                path = Path(file_path)
                if path.exists():
                    size = path.stat().st_size
                    report_data['files_generated'].append({
                        'file': file_path,
                        'description': description,
                        'size': size,
                        'exists': True
                    })
                else:
                    report_data['files_generated'].append({
                        'file': file_path,
                        'description': description,
                        'exists': False
                    })
            
            # 保存最终报告
            Path("reports").mkdir(exist_ok=True)
            with open('reports/ff_research_final_report.json', 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print("✅ 最终报告生成完成")
            return report_data
            
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
            return None
    
    def display_final_results(self, report_data):
        """显示最终结果"""
        print(f"\n{'='*60}")
        print(f"🎉 ForexFactory研究系统完成！")
        print(f"{'='*60}")
        
        print(f"📊 研究统计:")
        print(f"   发现交易员: {self.system_status['total_traders']} 个")
        print(f"   生成信号: {self.system_status['total_signals']} 个")
        print(f"   研究时间: {report_data['research_time']}")
        
        print(f"\n📁 生成的文件:")
        for file_info in report_data['files_generated']:
            status = "✅" if file_info['exists'] else "❌"
            size_info = f"({file_info['size']} bytes)" if file_info['exists'] else ""
            print(f"   {status} {file_info['description']}: {file_info['file']} {size_info}")
        
        print(f"\n🎯 如何使用研究结果:")
        print(f"   1. 查看交易员数据库: data/ff_traders_database.json")
        print(f"   2. 获取分析信号: logs/ff_analysis_signals.json")
        print(f"   3. 阅读详细报告: reports/ff_trader_analysis.json")
        print(f"   4. 集成到监控面板: 复制信号文件到logs/目录")
        
        print(f"\n💡 下一步建议:")
        print(f"   - 定期重新运行系统获取最新数据")
        print(f"   - 将信号集成到自动交易系统")
        print(f"   - 跟踪顶级交易员的最新动态")
        print(f"   - 基于分析结果优化交易策略")
    
    async def run_complete_research(self):
        """运行完整研究流程"""
        print("🚀 开始ForexFactory完整研究流程...")
        
        # 第1步: 猎取交易员
        if not await self.run_trader_hunter():
            print("❌ 交易员猎取失败，无法继续")
            return False
        
        # 等待一下
        print("⏰ 等待3秒后开始分析...")
        await asyncio.sleep(3)
        
        # 第2步: 分析交易员
        if not self.run_trader_analyzer():
            print("❌ 交易员分析失败")
            return False
        
        # 第3步: 生成最终报告
        report_data = self.generate_final_report()
        if not report_data:
            print("❌ 报告生成失败")
            return False
        
        # 显示结果
        self.display_final_results(report_data)
        
        return True
    
    async def run_system(self):
        """运行系统"""
        self.show_welcome()
        
        # 检查依赖
        if not self.check_dependencies():
            return
        
        if not self.check_chrome_driver():
            return
        
        print("\n🎯 选择运行模式:")
        print("1. 🚀 完整研究流程 (推荐)")
        print("2. 🕷️  只运行交易员猎手")
        print("3. 📊 只运行分析器 (需要已有数据)")
        print("4. 📋 只生成报告")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            print("\n🚀 启动完整研究流程...")
            print("⚠️  注意: 整个过程可能需要10-15分钟")
            print("💡 请保持网络连接稳定")
            
            confirm = input("确认开始? (y/n): ").strip().lower()
            if confirm == 'y':
                await self.run_complete_research()
            else:
                print("❌ 用户取消")
        
        elif choice == '2':
            await self.run_trader_hunter()
        
        elif choice == '3':
            self.run_trader_analyzer()
        
        elif choice == '4':
            report_data = self.generate_final_report()
            if report_data:
                self.display_final_results(report_data)
        
        else:
            print("❌ 无效选择")


async def main():
    """主函数"""
    system = FFResearchSystem()
    await system.run_system()


if __name__ == "__main__":
    asyncio.run(main())
