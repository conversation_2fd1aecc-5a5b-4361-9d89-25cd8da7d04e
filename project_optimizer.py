#!/usr/bin/env python3
"""
项目优化器 - 完善和优化整个MT4跟单系统
"""

import os
import sys
import json
import subprocess
import shutil
from pathlib import Path
from datetime import datetime


class ProjectOptimizer:
    """项目优化器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.required_dirs = [
            "config", "logs", "data", "MQL4/Files", "MQL4/Experts", 
            "templates", "static", "src/core", "src/sources", "src/utils"
        ]
        
    def show_menu(self):
        """显示菜单"""
        print("\n" + "="*60)
        print("🔧 MT4跟单系统 - 项目优化器")
        print("="*60)
        print("1. 🏗️  完整项目结构检查")
        print("2. 🔍 验证信号源真实性")
        print("3. 📊 生成测试数据")
        print("4. 🌐 启动增强版监控面板")
        print("5. 🧪 运行完整系统测试")
        print("6. 📋 生成项目报告")
        print("7. 🚀 一键优化整个项目")
        print("8. 📖 查看使用指南")
        print("0. 🚪 退出")
        print("="*60)
    
    def check_project_structure(self):
        """检查项目结构"""
        print("🏗️  检查项目结构...")
        
        # 检查目录
        print("\n📁 目录结构检查:")
        for directory in self.required_dirs:
            path = Path(directory)
            if path.exists():
                print(f"✅ {directory}")
            else:
                print(f"❌ {directory} - 不存在，正在创建...")
                path.mkdir(parents=True, exist_ok=True)
                print(f"✅ {directory} - 已创建")
        
        # 检查核心文件
        print("\n📄 核心文件检查:")
        core_files = [
            "signal_validator.py",
            "enhanced_dashboard.py", 
            "premium_signal_capture.py",
            "start_premium.py",
            "MQL4/Experts/PremiumCopyTrader.mq4"
        ]
        
        for file_path in core_files:
            path = Path(file_path)
            if path.exists():
                size = path.stat().st_size
                print(f"✅ {file_path} ({size} bytes)")
            else:
                print(f"❌ {file_path} - 缺失")
        
        # 检查配置文件
        print("\n⚙️  配置文件检查:")
        config_files = [
            "config/premium_config.json",
            "config/no_api_config.json"
        ]
        
        for config_file in config_files:
            path = Path(config_file)
            if path.exists():
                print(f"✅ {config_file}")
            else:
                print(f"⚠️  {config_file} - 不存在，需要创建")
    
    def validate_signals(self):
        """验证信号源"""
        print("🔍 验证信号源真实性...")
        
        try:
            result = subprocess.run([sys.executable, "signal_validator.py"], 
                                  input="4\n", text=True, capture_output=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ 信号验证完成")
                print("📊 验证结果:")
                print(result.stdout[-500:])  # 显示最后500个字符
            else:
                print("❌ 信号验证失败")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ 信号验证超时")
        except Exception as e:
            print(f"❌ 验证过程出错: {e}")
    
    def generate_test_data(self):
        """生成测试数据"""
        print("🧪 生成测试数据...")
        
        # 生成测试信号
        test_signals = [
            {
                'timestamp': datetime.now().isoformat(),
                'source': '优化器测试源',
                'symbol': 'EURUSD',
                'action': 'BUY',
                'entry_price': 1.0850,
                'stop_loss': 1.0800,
                'take_profit': 1.0950,
                'confidence': 0.85,
                'text': 'EURUSD BUY @ 1.0850, SL: 1.0800, TP: 1.0950 - 测试信号',
                'validated': True
            },
            {
                'timestamp': datetime.now().isoformat(),
                'source': '高质量信号源',
                'symbol': 'XAUUSD',
                'action': 'SELL',
                'entry_price': 1950.00,
                'stop_loss': 1960.00,
                'take_profit': 1930.00,
                'confidence': 0.92,
                'text': '黄金 卖出 @ 1950, 止损 1960, 止盈 1930 - 高置信度信号',
                'validated': True
            }
        ]
        
        # 保存测试信号
        Path("logs").mkdir(exist_ok=True)
        with open('logs/validated_signals.json', 'w', encoding='utf-8') as f:
            for signal in test_signals:
                f.write(json.dumps(signal, ensure_ascii=False) + '\n')
        
        # 生成测试账户信息
        Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
        with open('MQL4/Files/test_account_info.txt', 'w', encoding='utf-8') as f:
            f.write(f"Account: ********\n")
            f.write(f"Balance: 10000.00\n")
            f.write(f"Equity: 10125.50\n")
            f.write(f"Margin: 150.00\n")
            f.write(f"Free Margin: 9975.50\n")
            f.write(f"Today PnL: 125.50\n")
            f.write(f"Today Trades: 2\n")
            f.write(f"Open Positions: 1\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print("✅ 测试数据生成完成")
        print("📊 生成了以下测试数据:")
        print("   - 2个测试信号")
        print("   - 模拟账户信息")
        print("   - 系统状态数据")
    
    def start_dashboard(self):
        """启动增强版监控面板"""
        print("🌐 启动增强版监控面板...")
        
        try:
            print("🖥️  正在启动Web服务器...")
            print("🌐 访问地址: http://localhost:8080")
            print("💡 提示: 按 Ctrl+C 停止服务")
            
            subprocess.run([sys.executable, "enhanced_dashboard.py"])
            
        except KeyboardInterrupt:
            print("\n⏹️  监控面板已停止")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    
    def run_system_test(self):
        """运行系统测试"""
        print("🧪 运行完整系统测试...")
        
        tests = [
            ("项目结构", self.check_project_structure),
            ("测试数据生成", self.generate_test_data),
            ("Python依赖检查", self.check_dependencies),
            ("文件权限检查", self.check_file_permissions)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            print(f"\n🔍 执行测试: {test_name}")
            try:
                test_func()
                results[test_name] = "✅ 通过"
            except Exception as e:
                results[test_name] = f"❌ 失败: {e}"
        
        print("\n📋 测试结果汇总:")
        print("-" * 40)
        for test_name, result in results.items():
            print(f"{test_name:<20} {result}")
    
    def check_dependencies(self):
        """检查Python依赖"""
        required_packages = [
            'requests', 'beautifulsoup4', 'flask', 'selenium', 
            'undetected-chromedriver', 'pathlib'
        ]
        
        missing = []
        for package in required_packages:
            try:
                if package == 'beautifulsoup4':
                    import bs4
                elif package == 'undetected-chromedriver':
                    import undetected_chromedriver
                else:
                    __import__(package.replace('-', '_'))
            except ImportError:
                missing.append(package)
        
        if missing:
            print(f"❌ 缺失依赖: {', '.join(missing)}")
            raise Exception(f"缺失依赖包: {missing}")
        else:
            print("✅ 所有依赖包已安装")
    
    def check_file_permissions(self):
        """检查文件权限"""
        critical_files = [
            "signal_validator.py",
            "enhanced_dashboard.py",
            "premium_signal_capture.py"
        ]
        
        for file_path in critical_files:
            path = Path(file_path)
            if path.exists():
                if os.access(path, os.R_OK):
                    print(f"✅ {file_path} - 可读")
                else:
                    raise Exception(f"{file_path} 不可读")
            else:
                raise Exception(f"{file_path} 不存在")
    
    def generate_project_report(self):
        """生成项目报告"""
        print("📋 生成项目报告...")
        
        report = {
            'project_name': 'MT4智能跟单系统',
            'generated_at': datetime.now().isoformat(),
            'project_structure': {},
            'file_statistics': {},
            'system_status': 'operational'
        }
        
        # 统计文件信息
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith(('.py', '.mq4', '.json', '.md')):
                    file_path = Path(root) / file
                    try:
                        size = file_path.stat().st_size
                        report['file_statistics'][str(file_path)] = {
                            'size': size,
                            'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                        }
                    except:
                        continue
        
        # 保存报告
        with open('project_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("✅ 项目报告已生成: project_report.json")
        
        # 显示摘要
        print("\n📊 项目摘要:")
        print(f"   Python文件: {len([f for f in report['file_statistics'] if f.endswith('.py')])}")
        print(f"   MQ4文件: {len([f for f in report['file_statistics'] if f.endswith('.mq4')])}")
        print(f"   配置文件: {len([f for f in report['file_statistics'] if f.endswith('.json')])}")
        print(f"   总文件大小: {sum(info['size'] for info in report['file_statistics'].values())} bytes")
    
    def optimize_project(self):
        """一键优化整个项目"""
        print("🚀 开始一键优化整个项目...")
        
        optimization_steps = [
            ("检查项目结构", self.check_project_structure),
            ("生成测试数据", self.generate_test_data),
            ("验证信号源", self.validate_signals),
            ("运行系统测试", self.run_system_test),
            ("生成项目报告", self.generate_project_report)
        ]
        
        for step_name, step_func in optimization_steps:
            print(f"\n🔄 执行: {step_name}")
            try:
                step_func()
                print(f"✅ {step_name} 完成")
            except Exception as e:
                print(f"❌ {step_name} 失败: {e}")
                continue
        
        print("\n🎉 项目优化完成！")
        print("\n📋 下一步操作建议:")
        print("1. 运行 python enhanced_dashboard.py 启动监控面板")
        print("2. 访问 http://localhost:8080 查看系统状态")
        print("3. 配置付费信号源并开始捕获")
        print("4. 在MT4中安装并启动EA")
    
    def show_usage_guide(self):
        """显示使用指南"""
        print("\n📖 MT4智能跟单系统使用指南")
        print("="*50)
        
        print("\n🎯 系统特点:")
        print("✅ 支持多种信号源 (Telegram、Myfxbook、TradingView等)")
        print("✅ 智能信号解析和验证")
        print("✅ 实时Web监控面板")
        print("✅ 完整的风险管理系统")
        print("✅ 绕过API限制的高级捕获技术")
        
        print("\n📋 快速开始:")
        print("1. 运行项目优化器: python project_optimizer.py")
        print("2. 选择 '7' 进行一键优化")
        print("3. 启动监控面板: python enhanced_dashboard.py")
        print("4. 配置信号源: python start_premium.py")
        print("5. 安装MT4 EA并开始跟单")
        
        print("\n🔧 文件说明:")
        print("- signal_validator.py: 信号验证器")
        print("- enhanced_dashboard.py: 增强版监控面板")
        print("- premium_signal_capture.py: 付费信号捕获器")
        print("- start_premium.py: 付费系统启动器")
        print("- MQL4/Experts/PremiumCopyTrader.mq4: 增强版EA")
        
        print("\n⚠️  重要提醒:")
        print("- 建议先在模拟账户测试")
        print("- 定期检查系统运行状态")
        print("- 遵守各平台使用条款")
        print("- 交易有风险，投资需谨慎")
    
    def run(self):
        """运行优化器"""
        print("🔧 MT4智能跟单系统 - 项目优化器")
        print("帮助你完善和优化整个跟单系统")
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\n请选择操作 (0-8): ").strip()
                
                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    self.check_project_structure()
                elif choice == '2':
                    self.validate_signals()
                elif choice == '3':
                    self.generate_test_data()
                elif choice == '4':
                    self.start_dashboard()
                elif choice == '5':
                    self.run_system_test()
                elif choice == '6':
                    self.generate_project_report()
                elif choice == '7':
                    self.optimize_project()
                elif choice == '8':
                    self.show_usage_guide()
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
            
            if choice != '0' and choice != '4':
                input("\n按回车键继续...")


if __name__ == "__main__":
    optimizer = ProjectOptimizer()
    optimizer.run()
