"""
信号管理器 - 负责从多个来源获取和处理交易信号
"""

import asyncio
import re
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from dataclasses import dataclass

from ..sources.telegram_source import TelegramSignalSource
from ..sources.myfxbook_source import MyfxbookSignalSource
from ..utils.signal_parser import SignalParser
from ..utils.signal_validator import SignalValidator


@dataclass
class TradingSignal:
    """交易信号数据类"""
    source: str
    symbol: str
    action: str  # BUY, SELL
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    confidence: float
    timestamp: datetime
    raw_text: str
    trader_id: Optional[str] = None
    signal_id: Optional[str] = None


class SignalManager:
    """信号管理器"""
    
    def __init__(self, signal_sources_config: Dict, filtering_config: Dict, db_manager):
        self.config = signal_sources_config
        self.filtering_config = filtering_config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # 信号源
        self.telegram_source = None
        self.myfxbook_source = None
        
        # 工具类
        self.signal_parser = SignalParser()
        self.signal_validator = SignalValidator(filtering_config)
        
        # 信号缓存
        self.processed_signals = set()
        self.signal_queue = asyncio.Queue()
        
    async def initialize(self):
        """初始化信号源"""
        try:
            # 初始化Telegram信号源
            if self.config.get('telegram_channels'):
                self.telegram_source = TelegramSignalSource(
                    self.config['telegram_channels']
                )
                await self.telegram_source.initialize()
            
            # 初始化Myfxbook信号源
            if self.config.get('myfxbook_traders'):
                self.myfxbook_source = MyfxbookSignalSource(
                    self.config['myfxbook_traders']
                )
                await self.myfxbook_source.initialize()
            
            self.logger.info("✅ 信号源初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 信号源初始化失败: {e}")
            raise
    
    async def get_new_signals(self) -> List[TradingSignal]:
        """获取新的交易信号"""
        signals = []
        
        try:
            # 从Telegram获取信号
            if self.telegram_source:
                telegram_signals = await self.telegram_source.get_signals()
                signals.extend(telegram_signals)
            
            # 从Myfxbook获取信号
            if self.myfxbook_source:
                myfxbook_signals = await self.myfxbook_source.get_signals()
                signals.extend(myfxbook_signals)
            
            # 处理和过滤信号
            processed_signals = []
            for signal_data in signals:
                signal = await self.process_signal(signal_data)
                if signal and await self.validate_signal(signal):
                    processed_signals.append(signal)
            
            return processed_signals
            
        except Exception as e:
            self.logger.error(f"获取信号错误: {e}")
            return []
    
    async def process_signal(self, signal_data: Dict) -> Optional[TradingSignal]:
        """处理原始信号数据"""
        try:
            # 解析信号文本
            parsed_data = self.signal_parser.parse(signal_data['text'])
            
            if not parsed_data:
                return None
            
            # 创建信号对象
            signal = TradingSignal(
                source=signal_data['source'],
                symbol=parsed_data['symbol'],
                action=parsed_data['action'],
                entry_price=parsed_data.get('entry_price'),
                stop_loss=parsed_data.get('stop_loss'),
                take_profit=parsed_data.get('take_profit'),
                confidence=self.calculate_confidence(signal_data, parsed_data),
                timestamp=signal_data['timestamp'],
                raw_text=signal_data['text'],
                trader_id=signal_data.get('trader_id'),
                signal_id=signal_data.get('signal_id')
            )
            
            return signal
            
        except Exception as e:
            self.logger.error(f"处理信号错误: {e}")
            return None
    
    def calculate_confidence(self, signal_data: Dict, parsed_data: Dict) -> float:
        """计算信号置信度"""
        confidence = 0.5  # 基础置信度
        
        try:
            # 基于信号源权重
            source_weight = signal_data.get('weight', 0.5)
            confidence += source_weight * 0.3
            
            # 基于信号完整性
            if parsed_data.get('stop_loss'):
                confidence += 0.1
            if parsed_data.get('take_profit'):
                confidence += 0.1
            if parsed_data.get('entry_price'):
                confidence += 0.1
            
            # 基于交易师历史表现
            trader_performance = signal_data.get('trader_performance', {})
            if trader_performance:
                win_rate = trader_performance.get('win_rate', 0.5)
                confidence += (win_rate - 0.5) * 0.2
            
            # 基于技术分析确认
            if self.has_technical_confirmation(parsed_data):
                confidence += 0.1
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            self.logger.error(f"计算置信度错误: {e}")
            return 0.5
    
    def has_technical_confirmation(self, parsed_data: Dict) -> bool:
        """检查是否有技术分析确认"""
        # 这里可以添加技术指标确认逻辑
        # 例如：趋势确认、支撑阻力位确认等
        return False
    
    async def validate_signal(self, signal: TradingSignal) -> bool:
        """验证信号是否有效"""
        try:
            # 检查是否已处理过
            signal_hash = self.get_signal_hash(signal)
            if signal_hash in self.processed_signals:
                return False
            
            # 使用信号验证器
            if not self.signal_validator.validate(signal):
                return False
            
            # 检查信号时效性
            if not self.is_signal_fresh(signal):
                return False
            
            # 检查交易时间
            if not self.is_trading_time():
                return False
            
            # 添加到已处理集合
            self.processed_signals.add(signal_hash)
            
            # 清理过期的已处理信号
            await self.cleanup_processed_signals()
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证信号错误: {e}")
            return False
    
    def get_signal_hash(self, signal: TradingSignal) -> str:
        """生成信号哈希值"""
        return f"{signal.source}_{signal.symbol}_{signal.action}_{signal.timestamp.isoformat()}"
    
    def is_signal_fresh(self, signal: TradingSignal) -> bool:
        """检查信号是否新鲜"""
        max_age = timedelta(minutes=self.filtering_config.get('max_signal_age_minutes', 5))
        return datetime.now() - signal.timestamp <= max_age
    
    def is_trading_time(self) -> bool:
        """检查是否在交易时间内"""
        # 这里可以添加交易时间检查逻辑
        # 例如：避开周末、节假日、重要新闻时间等
        now = datetime.now()
        
        # 避开周末
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        return True
    
    async def cleanup_processed_signals(self):
        """清理过期的已处理信号"""
        # 保留最近1小时的信号记录
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        # 这里简化处理，实际应该基于时间戳清理
        if len(self.processed_signals) > 1000:
            # 清理一半
            signals_to_remove = list(self.processed_signals)[:500]
            for signal_hash in signals_to_remove:
                self.processed_signals.discard(signal_hash)
    
    async def get_signals_count_today(self) -> int:
        """获取今日处理的信号数量"""
        try:
            today = datetime.now().date()
            return await self.db_manager.get_signals_count_by_date(today)
        except Exception as e:
            self.logger.error(f"获取信号数量错误: {e}")
            return 0
    
    async def get_signal_statistics(self) -> Dict:
        """获取信号统计信息"""
        try:
            stats = {
                'total_signals_today': await self.get_signals_count_today(),
                'sources_active': 0,
                'average_confidence': 0.0,
                'top_symbols': []
            }
            
            # 检查活跃信号源
            if self.telegram_source and self.telegram_source.is_active():
                stats['sources_active'] += 1
            
            if self.myfxbook_source and self.myfxbook_source.is_active():
                stats['sources_active'] += 1
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取信号统计错误: {e}")
            return {}
    
    async def shutdown(self):
        """关闭信号管理器"""
        try:
            if self.telegram_source:
                await self.telegram_source.shutdown()
            
            if self.myfxbook_source:
                await self.myfxbook_source.shutdown()
            
            self.logger.info("✅ 信号管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭信号管理器错误: {e}")


class SignalAggregator:
    """信号聚合器 - 合并来自多个源的相似信号"""
    
    def __init__(self):
        self.pending_signals = {}
        self.aggregation_window = timedelta(minutes=2)
    
    async def aggregate_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """聚合相似信号"""
        aggregated = []
        
        for signal in signals:
            key = f"{signal.symbol}_{signal.action}"
            
            if key in self.pending_signals:
                # 合并信号
                existing_signal = self.pending_signals[key]
                merged_signal = self.merge_signals(existing_signal, signal)
                self.pending_signals[key] = merged_signal
            else:
                self.pending_signals[key] = signal
        
        # 检查是否有信号可以输出
        current_time = datetime.now()
        for key, signal in list(self.pending_signals.items()):
            if current_time - signal.timestamp >= self.aggregation_window:
                aggregated.append(signal)
                del self.pending_signals[key]
        
        return aggregated
    
    def merge_signals(self, signal1: TradingSignal, signal2: TradingSignal) -> TradingSignal:
        """合并两个相似信号"""
        # 使用置信度更高的信号作为基础
        base_signal = signal1 if signal1.confidence >= signal2.confidence else signal2
        
        # 平均置信度
        merged_confidence = (signal1.confidence + signal2.confidence) / 2
        
        # 创建合并后的信号
        merged_signal = TradingSignal(
            source=f"{signal1.source}+{signal2.source}",
            symbol=base_signal.symbol,
            action=base_signal.action,
            entry_price=base_signal.entry_price,
            stop_loss=base_signal.stop_loss,
            take_profit=base_signal.take_profit,
            confidence=merged_confidence,
            timestamp=min(signal1.timestamp, signal2.timestamp),
            raw_text=f"{signal1.raw_text} | {signal2.raw_text}"
        )
        
        return merged_signal
