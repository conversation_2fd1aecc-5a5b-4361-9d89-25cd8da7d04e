"""
日志工具 - 设置系统日志
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Dict


def setup_logger(log_config: Dict) -> logging.Logger:
    """设置日志系统"""
    
    # 创建日志目录
    log_file = Path(log_config['file_path'])
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_config['level']))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=log_config['max_file_size_mb'] * 1024 * 1024,
        backupCount=log_config['backup_count'],
        encoding='utf-8'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    
    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
