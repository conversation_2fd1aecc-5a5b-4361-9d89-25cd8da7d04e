#!/usr/bin/env python3
"""
无API版本主程序 - 完全免费的MT4跟单系统
"""

import asyncio
import json
import logging
import csv
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

# 免费信号源
from .sources.telegram_userbot import TelegramUserBot
from .sources.web_scraper import WebSignalScraper
from .sources.manual_signals import ManualSignalReader

# 核心组件
from .utils.signal_parser import SignalParser
from .utils.signal_validator import SignalValidator
from .utils.logger import setup_logger


class NoAPITradingSystem:
    """无API版本交易系统"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = None
        self.logger = None
        self.running = False
        
        # 信号源
        self.telegram_userbot = None
        self.web_scraper = None
        self.manual_reader = None
        
        # 工具
        self.signal_parser = SignalParser()
        self.signal_validator = None
        
        # MT4通信
        self.mt4_data_dir = Path("MT4_Data")
        self.signals_file = self.mt4_data_dir / "signals.csv"
        self.status_file = self.mt4_data_dir / "status.csv"
        self.trades_file = self.mt4_data_dir / "trades.csv"
        
        # 状态
        self.processed_signals = set()
        self.daily_trades = 0
        self.daily_risk_used = 0.0
    
    async def initialize(self):
        """初始化系统"""
        try:
            # 加载配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            # 设置日志
            log_config = self.config['monitoring']['logging']
            self.logger = setup_logger(log_config)
            self.logger.info("🚀 无API版本MT4跟单系统启动中...")
            
            # 初始化信号验证器
            self.signal_validator = SignalValidator({
                'min_signal_confidence': 0.5,
                'require_stop_loss': False,  # 免费信号可能没有止损
                'max_signal_age_minutes': 10
            })
            
            # 创建MT4数据目录
            self.mt4_data_dir.mkdir(exist_ok=True)
            self.init_mt4_files()
            
            # 初始化信号源
            await self.init_signal_sources()
            
            self.logger.info("✅ 无API版本系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 初始化失败: {e}")
            raise
    
    def init_mt4_files(self):
        """初始化MT4通信文件"""
        # 初始化信号文件
        if not self.signals_file.exists():
            with open(self.signals_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'symbol', 'action', 'entry_price', 
                    'stop_loss', 'take_profit', 'volume', 'confidence', 'source'
                ])
        
        # 初始化状态文件
        if not self.status_file.exists():
            with open(self.status_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['timestamp', 'status', 'message'])
                writer.writerow([datetime.now().isoformat(), 'INITIALIZED', 'System started'])
        
        # 初始化交易文件
        if not self.trades_file.exists():
            with open(self.trades_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'ticket', 'symbol', 'action', 'volume', 
                    'open_price', 'close_price', 'profit', 'status'
                ])
    
    async def init_signal_sources(self):
        """初始化信号源"""
        try:
            # 初始化Telegram用户机器人
            telegram_config = self.config.get('telegram_setup', {})
            if telegram_config.get('api_id') and telegram_config.get('api_hash'):
                self.telegram_userbot = TelegramUserBot(
                    telegram_config,
                    self.config['signal_sources']['telegram_channels']
                )
                await self.telegram_userbot.initialize()
            
            # 初始化网页抓取器
            web_config = self.config['signal_sources'].get('web_scraping', {})
            if web_config.get('enabled'):
                self.web_scraper = WebSignalScraper(web_config['sources'])
                await self.web_scraper.initialize()
            
            # 初始化手动信号读取器
            manual_config = self.config['signal_sources'].get('manual_signals', {})
            if manual_config.get('enabled'):
                self.manual_reader = ManualSignalReader(manual_config['file_path'])
            
            self.logger.info("✅ 信号源初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 信号源初始化失败: {e}")
    
    async def start(self):
        """启动系统"""
        try:
            self.running = True
            self.logger.info("🎯 开始监控免费交易信号...")
            
            # 更新状态
            self.update_status("RUNNING", "System is running")
            
            # 启动主循环
            await self.main_loop()
            
        except Exception as e:
            self.logger.error(f"❌ 系统运行错误: {e}")
            self.update_status("ERROR", str(e))
            raise
    
    async def main_loop(self):
        """主循环"""
        while self.running:
            try:
                # 获取新信号
                signals = await self.collect_signals()
                
                # 处理信号
                for signal in signals:
                    await self.process_signal(signal)
                
                # 检查MT4状态
                await self.check_mt4_status()
                
                # 更新系统状态
                self.update_system_status()
                
                # 等待下一次检查
                await asyncio.sleep(self.config['mt4_integration']['check_interval'])
                
            except Exception as e:
                self.logger.error(f"主循环错误: {e}")
                await asyncio.sleep(5)
    
    async def collect_signals(self) -> List[Dict]:
        """收集所有信号源的信号"""
        all_signals = []
        
        try:
            # 从Telegram收集信号
            if self.telegram_userbot:
                telegram_signals = await self.telegram_userbot.get_signals()
                all_signals.extend(telegram_signals)
            
            # 从网页抓取信号
            if self.web_scraper:
                web_signals = await self.web_scraper.get_signals()
                all_signals.extend(web_signals)
            
            # 从手动文件读取信号
            if self.manual_reader:
                manual_signals = self.manual_reader.get_signals()
                all_signals.extend(manual_signals)
            
            return all_signals
            
        except Exception as e:
            self.logger.error(f"收集信号错误: {e}")
            return []
    
    async def process_signal(self, signal_data: Dict):
        """处理单个信号"""
        try:
            # 解析信号
            parsed_signal = self.signal_parser.parse(signal_data.get('text', ''))
            if not parsed_signal:
                return
            
            # 创建完整信号对象
            signal = {
                'timestamp': signal_data.get('timestamp', datetime.now()),
                'source': signal_data.get('source', 'unknown'),
                'symbol': parsed_signal['symbol'],
                'action': parsed_signal['action'],
                'entry_price': parsed_signal.get('entry_price'),
                'stop_loss': parsed_signal.get('stop_loss'),
                'take_profit': parsed_signal.get('take_profit'),
                'confidence': signal_data.get('confidence', 0.5),
                'raw_text': signal_data.get('text', '')
            }
            
            # 生成信号ID
            signal_id = self.generate_signal_id(signal)
            
            # 检查是否已处理
            if signal_id in self.processed_signals:
                return
            
            # 验证信号
            if not self.validate_signal(signal):
                return
            
            # 风险检查
            if not self.check_risk_limits(signal):
                return
            
            # 计算仓位大小
            volume = self.calculate_position_size(signal)
            signal['volume'] = volume
            
            # 写入MT4信号文件
            self.write_signal_to_mt4(signal)
            
            # 记录已处理
            self.processed_signals.add(signal_id)
            
            self.logger.info(f"✅ 信号已发送到MT4: {signal['symbol']} {signal['action']}")
            
        except Exception as e:
            self.logger.error(f"处理信号错误: {e}")
    
    def generate_signal_id(self, signal: Dict) -> str:
        """生成信号ID"""
        return f"{signal['source']}_{signal['symbol']}_{signal['action']}_{signal['timestamp'].strftime('%Y%m%d_%H%M')}"
    
    def validate_signal(self, signal: Dict) -> bool:
        """验证信号"""
        try:
            # 检查必要字段
            if not signal.get('symbol') or not signal.get('action'):
                return False
            
            # 检查交易品种是否允许
            allowed_symbols = self.config['trading_settings']['allowed_symbols']
            if signal['symbol'] not in allowed_symbols:
                self.logger.debug(f"品种不在允许列表: {signal['symbol']}")
                return False
            
            # 检查交易时间
            if not self.is_trading_time():
                self.logger.debug("当前不在交易时间")
                return False
            
            # 检查信号时效性
            signal_age = datetime.now() - signal['timestamp']
            max_age = timedelta(minutes=10)
            if signal_age > max_age:
                self.logger.debug(f"信号过期: {signal_age}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证信号错误: {e}")
            return False
    
    def is_trading_time(self) -> bool:
        """检查是否在交易时间"""
        try:
            trading_hours = self.config['trading_settings']['trading_hours']
            if not trading_hours.get('enabled'):
                return True
            
            now = datetime.now()
            
            # 检查周末
            if now.weekday() >= 5:  # 周六、周日
                return False
            
            # 检查时间范围
            start_time = trading_hours['start_time']
            end_time = trading_hours['end_time']
            current_time = now.strftime('%H:%M')
            
            return start_time <= current_time <= end_time
            
        except Exception as e:
            self.logger.error(f"检查交易时间错误: {e}")
            return True
    
    def check_risk_limits(self, signal: Dict) -> bool:
        """检查风险限制"""
        try:
            risk_config = self.config['risk_management']['account_settings']
            
            # 检查每日交易次数
            if self.daily_trades >= 10:  # 每日最多10笔交易
                self.logger.warning("已达到每日最大交易次数")
                return False
            
            # 检查每日风险使用
            if self.daily_risk_used >= risk_config['max_daily_risk']:
                self.logger.warning("已达到每日最大风险限制")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查风险限制错误: {e}")
            return False
    
    def calculate_position_size(self, signal: Dict) -> float:
        """计算仓位大小"""
        try:
            position_config = self.config['risk_management']['position_sizing']
            
            # 基础仓位
            base_volume = position_config['base_lot_size']
            
            # 根据置信度调整
            confidence_factor = signal.get('confidence', 0.5)
            adjusted_volume = base_volume * (0.5 + confidence_factor)
            
            # 应用限制
            max_volume = position_config['max_lot_size']
            final_volume = min(adjusted_volume, max_volume)
            
            return round(final_volume, 2)
            
        except Exception as e:
            self.logger.error(f"计算仓位大小错误: {e}")
            return self.config['risk_management']['position_sizing']['base_lot_size']
    
    def write_signal_to_mt4(self, signal: Dict):
        """写入信号到MT4文件"""
        try:
            with open(self.signals_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    signal['timestamp'].isoformat(),
                    signal['symbol'],
                    signal['action'],
                    signal.get('entry_price', ''),
                    signal.get('stop_loss', ''),
                    signal.get('take_profit', ''),
                    signal['volume'],
                    signal['confidence'],
                    signal['source']
                ])
            
            self.daily_trades += 1
            
        except Exception as e:
            self.logger.error(f"写入MT4信号文件错误: {e}")
    
    async def check_mt4_status(self):
        """检查MT4状态"""
        try:
            # 读取交易文件，检查是否有新的交易
            if self.trades_file.exists():
                with open(self.trades_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    trades = list(reader)
                    
                    # 检查今日交易
                    today = datetime.now().date()
                    today_trades = [
                        trade for trade in trades 
                        if datetime.fromisoformat(trade['timestamp']).date() == today
                    ]
                    
                    if len(today_trades) != self.daily_trades:
                        self.logger.info(f"MT4交易状态更新: {len(today_trades)}笔交易")
            
        except Exception as e:
            self.logger.error(f"检查MT4状态错误: {e}")
    
    def update_status(self, status: str, message: str):
        """更新系统状态"""
        try:
            with open(self.status_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([datetime.now().isoformat(), status, message])
                
        except Exception as e:
            self.logger.error(f"更新状态错误: {e}")
    
    def update_system_status(self):
        """更新系统状态"""
        try:
            # 清理过期的已处理信号
            cutoff_time = datetime.now() - timedelta(hours=1)
            self.processed_signals = {
                signal_id for signal_id in self.processed_signals
                if not signal_id.endswith(cutoff_time.strftime('%Y%m%d_%H%M'))
            }
            
            # 重置每日计数器
            now = datetime.now()
            if now.hour == 0 and now.minute == 0:
                self.daily_trades = 0
                self.daily_risk_used = 0.0
                self.logger.info("每日计数器已重置")
            
        except Exception as e:
            self.logger.error(f"更新系统状态错误: {e}")
    
    async def shutdown(self):
        """关闭系统"""
        try:
            self.logger.info("🔄 系统关闭中...")
            self.running = False
            
            # 关闭信号源
            if self.telegram_userbot:
                await self.telegram_userbot.shutdown()
            
            if self.web_scraper:
                await self.web_scraper.shutdown()
            
            # 更新状态
            self.update_status("STOPPED", "System stopped")
            
            self.logger.info("✅ 无API版本系统已安全关闭")
            
        except Exception as e:
            self.logger.error(f"关闭系统错误: {e}")
