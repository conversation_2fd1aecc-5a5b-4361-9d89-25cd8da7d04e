"""
信号解析器 - 解析各种格式的交易信号
"""

import re
import logging
from typing import Dict, Optional, List
from datetime import datetime


class SignalParser:
    """交易信号解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 交易品种模式
        self.symbol_patterns = {
            r'\b(EUR/USD|EURUSD|欧美)\b': 'EURUSD',
            r'\b(GBP/USD|GBPUSD|镑美)\b': 'GBPUSD',
            r'\b(USD/JPY|USDJPY|美日)\b': 'USDJPY',
            r'\b(USD/CHF|USDCHF|美瑞)\b': 'USDCHF',
            r'\b(AUD/USD|AUDUSD|澳美)\b': 'AUDUSD',
            r'\b(USD/CAD|USDCAD|美加)\b': 'USDCAD',
            r'\b(NZD/USD|NZDUSD|纽美)\b': 'NZDUSD',
            r'\b(EUR/JPY|EURJPY|欧日)\b': 'EURJPY',
            r'\b(GBP/JPY|GBPJPY|镑日)\b': 'GBPJPY',
            r'\b(EUR/GBP|EURGBP|欧镑)\b': 'EURGBP',
            r'\b(XAU/USD|XAUUSD|黄金|GOLD)\b': 'XAUUSD',
            r'\b(XAG/USD|XAGUSD|白银|SILVER)\b': 'XAGUSD'
        }
        
        # 交易方向模式
        self.action_patterns = {
            'BUY': [
                r'(?i)\b(buy|long|bull|上涨|买入|做多|看涨)\b',
                r'(?i)\b(call|bullish)\b'
            ],
            'SELL': [
                r'(?i)\b(sell|short|bear|下跌|卖出|做空|看跌)\b',
                r'(?i)\b(put|bearish)\b'
            ]
        }
        
        # 价格模式
        self.price_patterns = {
            'entry': [
                r'(?i)(?:entry|入场|进场|开仓).*?(\d+\.?\d*)',
                r'(?i)(?:price|价格|@).*?(\d+\.?\d*)',
                r'(?i)@\s*(\d+\.?\d*)'
            ],
            'stop_loss': [
                r'(?i)(?:sl|stop loss|止损).*?(\d+\.?\d*)',
                r'(?i)(?:stop|停止).*?(\d+\.?\d*)'
            ],
            'take_profit': [
                r'(?i)(?:tp|take profit|止盈|目标).*?(\d+\.?\d*)',
                r'(?i)(?:target|目标价|profit).*?(\d+\.?\d*)'
            ]
        }
    
    def parse(self, text: str) -> Optional[Dict]:
        """解析信号文本"""
        try:
            if not text or not isinstance(text, str):
                return None
            
            # 清理文本
            text = self.clean_text(text)
            
            # 提取交易品种
            symbol = self.extract_symbol(text)
            if not symbol:
                return None
            
            # 提取交易方向
            action = self.extract_action(text)
            if not action:
                return None
            
            # 提取价格信息
            prices = self.extract_prices(text)
            
            # 构建结果
            result = {
                'symbol': symbol,
                'action': action,
                'entry_price': prices.get('entry'),
                'stop_loss': prices.get('stop_loss'),
                'take_profit': prices.get('take_profit'),
                'raw_text': text
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"解析信号错误: {e}")
            return None
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留必要的符号）
        text = re.sub(r'[^\w\s\.\/@:：-]', ' ', text)
        
        return text
    
    def extract_symbol(self, text: str) -> Optional[str]:
        """提取交易品种"""
        for pattern, symbol in self.symbol_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                return symbol
        return None
    
    def extract_action(self, text: str) -> Optional[str]:
        """提取交易方向"""
        for action, patterns in self.action_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text):
                    return action
        return None
    
    def extract_prices(self, text: str) -> Dict[str, Optional[float]]:
        """提取价格信息"""
        prices = {}
        
        for price_type, patterns in self.price_patterns.items():
            price = None
            
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    try:
                        price = float(match.group(1))
                        break
                    except (ValueError, IndexError):
                        continue
            
            prices[price_type] = price
        
        return prices
    
    def parse_multiple_signals(self, text: str) -> List[Dict]:
        """解析包含多个信号的文本"""
        signals = []
        
        # 按行分割
        lines = text.split('\n')
        
        for line in lines:
            signal = self.parse(line)
            if signal:
                signals.append(signal)
        
        return signals
    
    def validate_signal(self, signal: Dict) -> bool:
        """验证信号的有效性"""
        try:
            # 检查必要字段
            if not signal.get('symbol') or not signal.get('action'):
                return False
            
            # 检查价格逻辑
            entry = signal.get('entry_price')
            sl = signal.get('stop_loss')
            tp = signal.get('take_profit')
            
            if entry and sl and tp:
                if signal['action'] == 'BUY':
                    # 买入：止损应该低于入场价，止盈应该高于入场价
                    if sl >= entry or tp <= entry:
                        return False
                else:
                    # 卖出：止损应该高于入场价，止盈应该低于入场价
                    if sl <= entry or tp >= entry:
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证信号错误: {e}")
            return False
    
    def extract_lot_size(self, text: str) -> Optional[float]:
        """提取手数信息"""
        patterns = [
            r'(?i)(?:lot|手数|volume).*?(\d+\.?\d*)',
            r'(?i)(\d+\.?\d*)\s*(?:lot|手)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    def extract_risk_reward(self, text: str) -> Optional[float]:
        """提取风险回报比"""
        patterns = [
            r'(?i)(?:rr|risk reward|风险回报).*?(\d+\.?\d*)',
            r'(?i)(\d+\.?\d*)\s*:\s*(\d+\.?\d*)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    if len(match.groups()) == 2:
                        risk = float(match.group(1))
                        reward = float(match.group(2))
                        return reward / risk if risk > 0 else None
                    else:
                        return float(match.group(1))
                except ValueError:
                    continue
        
        return None
