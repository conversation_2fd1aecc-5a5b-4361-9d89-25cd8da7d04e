#!/usr/bin/env python3
"""
ForexFactory交易员猎手 - 专门研究和捕获FF交易员的真实交易信号
ForexFactory是外汇界最权威的社区，这里的交易员都是真实的
"""

import asyncio
import json
import logging
import random
import time
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup


class ForexFactoryTraderHunter:
    """ForexFactory交易员猎手"""

    def __init__(self):
        self.logger = self.setup_logger()
        self.base_url = "https://www.forexfactory.com"
        self.trades_url = "https://www.forexfactory.com/trades"

        # 交易员数据存储
        self.traders_database = {}
        self.captured_trades = []

        # 统计信息
        self.stats = {
            'total_traders_found': 0,
            'active_traders': 0,
            'total_trades_captured': 0,
            'profitable_traders': 0,
            'last_scan_time': None
        }

        # Chrome选项配置
        self.chrome_options = Options()
        self.setup_chrome_options()

    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('ForexFactoryTraderHunter')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)

            # 文件日志
            file_handler = logging.FileHandler('logs/ff_trader_hunter.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

            # 控制台日志
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)

        return logger

    def setup_chrome_options(self):
        """设置Chrome选项"""
        # 基础设置
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)

        # 用户代理
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
        ]
        self.chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')

        # 其他反检测设置
        self.chrome_options.add_argument('--disable-web-security')
        self.chrome_options.add_argument('--allow-running-insecure-content')
        self.chrome_options.add_argument('--disable-extensions')

        # 可选：无头模式（后台运行）
        # self.chrome_options.add_argument('--headless')

    def create_driver(self):
        """创建Chrome驱动"""
        try:
            driver = webdriver.Chrome(options=self.chrome_options)

            # 执行反检测脚本
            driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
                window.chrome = {runtime: {}};
            """)

            return driver

        except Exception as e:
            self.logger.error(f"创建Chrome驱动失败: {e}")
            return None

    async def handle_robot_verification(self, driver):
        """处理机器人验证"""
        try:
            # 检测常见的验证元素
            verification_selectors = [
                'iframe[src*="captcha"]',
                'div[class*="captcha"]',
                'div[class*="challenge"]',
                'div[class*="verification"]',
                'div[id*="captcha"]',
                '.cf-challenge-running',
                '.cf-browser-verification'
            ]

            verification_detected = False
            for selector in verification_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        verification_detected = True
                        self.logger.info(f"🤖 检测到验证元素: {selector}")
                        break
                except:
                    continue

            # 检查页面标题和内容
            page_title = driver.title.lower()
            page_source = driver.page_source.lower()

            verification_keywords = ['verification', 'captcha', 'challenge', 'robot', 'human', 'cloudflare']
            if any(keyword in page_title or keyword in page_source for keyword in verification_keywords):
                verification_detected = True
                self.logger.info("🤖 检测到验证关键词")

            if verification_detected:
                self.logger.info("🤖 检测到机器人验证，等待用户完成...")
                print("\n" + "="*60)
                print("🤖 检测到ForexFactory机器人验证")
                print("📋 请在浏览器中完成验证:")
                print("   1. 点击验证框")
                print("   2. 完成拼图或其他验证")
                print("   3. 等待页面跳转")
                print("⏰ 系统将等待最多3分钟...")
                print("="*60)

                # 等待验证完成，最多3分钟
                max_wait_time = 180  # 3分钟
                wait_interval = 5    # 每5秒检查一次
                waited_time = 0

                while waited_time < max_wait_time:
                    await asyncio.sleep(wait_interval)
                    waited_time += wait_interval

                    # 检查是否还在验证页面
                    current_title = driver.title.lower()
                    current_source = driver.page_source.lower()

                    still_verifying = any(keyword in current_title or keyword in current_source
                                        for keyword in verification_keywords)

                    if not still_verifying:
                        self.logger.info("✅ 验证完成，继续执行...")
                        print("✅ 验证完成！继续执行...")
                        break

                    # 显示等待进度
                    remaining = max_wait_time - waited_time
                    print(f"\r⏰ 等待验证完成... 剩余时间: {remaining}秒", end="", flush=True)

                if waited_time >= max_wait_time:
                    self.logger.warning("⏰ 验证等待超时")
                    print("\n⏰ 验证等待超时，尝试继续...")

                # 验证完成后额外等待页面稳定
                self.logger.info("⏰ 验证后等待页面稳定...")
                await asyncio.sleep(random.uniform(5, 8))

            else:
                self.logger.info("✅ 未检测到验证，直接继续")

        except Exception as e:
            self.logger.error(f"处理验证时出错: {e}")
            # 出错时也等待一下
            await asyncio.sleep(5)

    async def wait_for_page_elements(self, driver):
        """等待页面元素加载"""
        self.logger.info("⏰ 等待页面元素加载...")

        # 多种等待策略
        wait_strategies = [
            # 策略1: 等待常见的交易员相关元素
            [".trader", ".trade-row", "[data-trader]", ".user-trade"],
            # 策略2: 等待表格元素
            ["table", "tbody", "tr"],
            # 策略3: 等待用户链接
            ["a[href*='member']", "a[href*='showthread']"],
            # 策略4: 等待任何内容加载
            ["body", "main", "#content", ".content"]
        ]

        for i, selectors in enumerate(wait_strategies, 1):
            self.logger.info(f"⏰ 尝试等待策略 {i}: {selectors}")

            for selector in selectors:
                try:
                    # 等待元素出现
                    WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    self.logger.info(f"✅ 找到元素: {selector}")

                    # 额外等待确保元素完全加载
                    await asyncio.sleep(random.uniform(2, 4))
                    return True

                except TimeoutException:
                    self.logger.debug(f"⏰ 元素 {selector} 等待超时")
                    continue
                except Exception as e:
                    self.logger.debug(f"⏰ 等待元素 {selector} 时出错: {e}")
                    continue

        # 如果所有策略都失败，等待页面至少加载完成
        self.logger.warning("⚠️  未找到预期元素，等待页面基本加载...")
        try:
            WebDriverWait(driver, 20).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            self.logger.info("✅ 页面基本加载完成")
        except:
            self.logger.warning("⚠️  页面加载状态检查失败")

        # 最后的等待时间
        await asyncio.sleep(random.uniform(3, 6))
        return False

    async def scan_traders_page(self):
        """扫描交易员页面"""
        self.logger.info("🔍 开始扫描ForexFactory交易员页面...")

        driver = self.create_driver()
        if not driver:
            return []

        traders_found = []

        try:
            # 访问交易员页面
            self.logger.info(f"访问: {self.trades_url}")
            driver.get(self.trades_url)

            # 检测机器人验证
            self.logger.info("⏰ 检测页面状态...")
            await self.handle_robot_verification(driver)

            # 等待页面完全加载
            self.logger.info("⏰ 等待页面完全加载...")
            await asyncio.sleep(random.uniform(8, 12))

            # 多次尝试等待页面元素加载
            await self.wait_for_page_elements(driver)

            # 查找交易员元素 - 扩展选择器列表
            trader_selectors = [
                ".trader",
                ".trade-row",
                "[data-trader]",
                ".user-trade",
                "tr[class*='trade']",
                "table tr",
                ".forumline tr",
                "tbody tr",
                "div[class*='user']",
                "a[href*='member']",
                "a[href*='showthread']"
            ]

            traders_elements = []
            for selector in trader_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        traders_elements = elements
                        self.logger.info(f"使用选择器 {selector} 找到 {len(elements)} 个元素")
                        break
                except Exception as e:
                    continue

            if not traders_elements:
                # 尝试获取页面源码进行分析
                page_source = driver.page_source
                self.logger.info("未找到交易员元素，分析页面结构...")

                # 保存页面源码用于分析
                with open('logs/ff_page_source.html', 'w', encoding='utf-8') as f:
                    f.write(page_source)

                # 使用BeautifulSoup分析
                soup = BeautifulSoup(page_source, 'html.parser')

                # 查找可能的交易员信息
                potential_traders = self.analyze_page_structure(soup)
                traders_found.extend(potential_traders)

            else:
                # 解析找到的交易员元素
                for element in traders_elements[:20]:  # 限制前20个
                    try:
                        trader_info = await self.extract_trader_info(element, driver)
                        if trader_info:
                            traders_found.append(trader_info)

                        # 随机延迟
                        await asyncio.sleep(random.uniform(0.5, 1.5))

                    except Exception as e:
                        self.logger.error(f"提取交易员信息失败: {e}")
                        continue

            self.logger.info(f"✅ 扫描完成，发现 {len(traders_found)} 个交易员")

        except Exception as e:
            self.logger.error(f"扫描交易员页面失败: {e}")

        finally:
            driver.quit()

        return traders_found

    def analyze_page_structure(self, soup):
        """分析页面结构寻找交易员信息"""
        traders = []

        try:
            # 查找包含用户名的元素
            username_patterns = [
                'a[href*="/showthread.php?u="]',
                'a[href*="/member.php"]',
                '.username',
                '.user-name',
                '[data-user]'
            ]

            for pattern in username_patterns:
                user_elements = soup.select(pattern)
                if user_elements:
                    self.logger.info(f"找到 {len(user_elements)} 个用户元素")

                    for user_elem in user_elements[:10]:  # 限制数量
                        try:
                            username = user_elem.get_text().strip()
                            user_url = user_elem.get('href', '')

                            if username and len(username) > 2:
                                trader_info = {
                                    'username': username,
                                    'profile_url': user_url,
                                    'found_method': 'page_analysis',
                                    'discovery_time': datetime.now().isoformat()
                                }
                                traders.append(trader_info)
                        except Exception as e:
                            continue
                    break

            # 查找交易相关信息
            trade_keywords = ['profit', 'loss', 'pips', 'trade', 'position', 'entry', 'exit']
            text_content = soup.get_text().lower()

            if any(keyword in text_content for keyword in trade_keywords):
                self.logger.info("页面包含交易相关内容")

        except Exception as e:
            self.logger.error(f"分析页面结构失败: {e}")

        return traders

    async def extract_trader_info(self, element, driver):
        """提取交易员信息"""
        try:
            trader_info = {
                'discovery_time': datetime.now().isoformat(),
                'extraction_method': 'selenium'
            }

            # 尝试提取用户名
            username_selectors = ['.username', '.user-name', 'a[href*="member"]', '.trader-name']
            for selector in username_selectors:
                try:
                    username_elem = element.find_element(By.CSS_SELECTOR, selector)
                    trader_info['username'] = username_elem.text.strip()
                    trader_info['profile_url'] = username_elem.get_attribute('href')
                    break
                except NoSuchElementException:
                    continue

            # 尝试提取交易信息
            trade_selectors = ['.trade-info', '.position', '.profit-loss', '.pips']
            for selector in trade_selectors:
                try:
                    trade_elem = element.find_element(By.CSS_SELECTOR, selector)
                    trader_info['trade_info'] = trade_elem.text.strip()
                    break
                except NoSuchElementException:
                    continue

            # 提取其他可能的信息
            try:
                trader_info['element_text'] = element.text.strip()
                trader_info['element_html'] = element.get_attribute('outerHTML')[:500]  # 限制长度
            except:
                pass

            return trader_info if trader_info.get('username') else None

        except Exception as e:
            self.logger.error(f"提取交易员信息异常: {e}")
            return None

    async def research_trader_profile(self, trader_info):
        """深入研究单个交易员档案"""
        if not trader_info.get('profile_url'):
            return None

        self.logger.info(f"🔍 研究交易员: {trader_info.get('username', 'Unknown')}")

        driver = self.create_driver()
        if not driver:
            return None

        try:
            profile_url = trader_info['profile_url']
            if not profile_url.startswith('http'):
                profile_url = self.base_url + profile_url

            driver.get(profile_url)
            await asyncio.sleep(random.uniform(2, 4))

            # 提取详细信息
            detailed_info = trader_info.copy()

            # 查找交易历史
            trade_history = await self.extract_trade_history(driver)
            if trade_history:
                detailed_info['trade_history'] = trade_history

            # 查找统计信息
            stats = await self.extract_trader_stats(driver)
            if stats:
                detailed_info['stats'] = stats

            return detailed_info

        except Exception as e:
            self.logger.error(f"研究交易员档案失败: {e}")
            return None

        finally:
            driver.quit()

    async def extract_trade_history(self, driver):
        """提取交易历史"""
        trades = []

        try:
            # 查找交易记录表格或列表
            trade_selectors = [
                'table.trades tr',
                '.trade-row',
                '.position-history tr',
                '[data-trade]'
            ]

            for selector in trade_selectors:
                try:
                    trade_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if trade_elements:
                        for trade_elem in trade_elements[:10]:  # 限制数量
                            trade_text = trade_elem.text.strip()
                            if trade_text and len(trade_text) > 10:
                                # 解析交易信息
                                trade_info = self.parse_trade_text(trade_text)
                                if trade_info:
                                    trades.append(trade_info)
                        break
                except Exception as e:
                    continue

        except Exception as e:
            self.logger.error(f"提取交易历史失败: {e}")

        return trades

    def parse_trade_text(self, trade_text):
        """解析交易文本"""
        try:
            # 查找货币对
            symbol_match = re.search(r'\b(EUR/USD|GBP/USD|USD/JPY|XAU/USD|EURUSD|GBPUSD|USDJPY|XAUUSD)\b', trade_text, re.IGNORECASE)

            # 查找方向
            action_match = re.search(r'\b(BUY|SELL|LONG|SHORT)\b', trade_text, re.IGNORECASE)

            # 查找价格
            price_matches = re.findall(r'\d+\.\d{2,5}', trade_text)

            # 查找盈亏
            profit_match = re.search(r'[+-]?\$?\d+\.?\d*\s*(pips?|points?|\$)', trade_text, re.IGNORECASE)

            if symbol_match and action_match:
                return {
                    'symbol': symbol_match.group().replace('/', '').upper(),
                    'action': action_match.group().upper(),
                    'prices': price_matches,
                    'profit_info': profit_match.group() if profit_match else None,
                    'raw_text': trade_text,
                    'parsed_time': datetime.now().isoformat()
                }

        except Exception as e:
            pass

        return None

    async def extract_trader_stats(self, driver):
        """提取交易员统计信息"""
        stats = {}

        try:
            # 查找统计信息
            stat_selectors = [
                '.stats',
                '.trader-stats',
                '.performance',
                '.summary'
            ]

            for selector in stat_selectors:
                try:
                    stat_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for stat_elem in stat_elements:
                        stat_text = stat_elem.text.strip()
                        if stat_text:
                            # 解析统计信息
                            parsed_stats = self.parse_stats_text(stat_text)
                            stats.update(parsed_stats)
                except Exception as e:
                    continue

        except Exception as e:
            self.logger.error(f"提取统计信息失败: {e}")

        return stats

    def parse_stats_text(self, stats_text):
        """解析统计文本"""
        stats = {}

        try:
            # 查找胜率
            win_rate_match = re.search(r'(\d+\.?\d*)%.*win', stats_text, re.IGNORECASE)
            if win_rate_match:
                stats['win_rate'] = float(win_rate_match.group(1))

            # 查找总盈亏
            profit_match = re.search(r'profit.*?([+-]?\$?\d+\.?\d*)', stats_text, re.IGNORECASE)
            if profit_match:
                stats['total_profit'] = profit_match.group(1)

            # 查找交易次数
            trades_match = re.search(r'(\d+).*trades?', stats_text, re.IGNORECASE)
            if trades_match:
                stats['total_trades'] = int(trades_match.group(1))

        except Exception as e:
            pass

        return stats

    def save_traders_database(self):
        """保存交易员数据库"""
        try:
            Path("data").mkdir(exist_ok=True)

            # 保存详细数据库
            with open('data/ff_traders_database.json', 'w', encoding='utf-8') as f:
                json.dump(self.traders_database, f, indent=2, ensure_ascii=False)

            # 保存统计信息
            with open('data/ff_hunter_stats.json', 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)

            # 保存交易信号
            if self.captured_trades:
                with open('logs/ff_trader_signals.json', 'w', encoding='utf-8') as f:
                    for trade in self.captured_trades:
                        f.write(json.dumps(trade, ensure_ascii=False) + '\n')

            self.logger.info(f"✅ 已保存 {len(self.traders_database)} 个交易员数据")

        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")

    async def hunt_ff_traders(self):
        """猎取ForexFactory交易员"""
        self.logger.info("🚀 开始ForexFactory交易员猎取...")

        # 第一步：扫描交易员页面
        traders_found = await self.scan_traders_page()

        if not traders_found:
            self.logger.warning("未发现交易员，可能需要调整策略")
            return

        self.stats['total_traders_found'] = len(traders_found)

        # 第二步：深入研究每个交易员
        for i, trader in enumerate(traders_found[:5], 1):  # 限制前5个进行深入研究
            self.logger.info(f"🔍 深入研究交易员 {i}/{min(5, len(traders_found))}: {trader.get('username', 'Unknown')}")

            detailed_info = await self.research_trader_profile(trader)
            if detailed_info:
                trader_id = detailed_info.get('username', f'trader_{i}')
                self.traders_database[trader_id] = detailed_info

                # 如果有交易历史，转换为信号
                if detailed_info.get('trade_history'):
                    for trade in detailed_info['trade_history']:
                        signal = self.convert_trade_to_signal(trade, trader_id)
                        if signal:
                            self.captured_trades.append(signal)

            # 延迟避免被封
            await asyncio.sleep(random.uniform(5, 10))

        # 更新统计
        self.stats['active_traders'] = len(self.traders_database)
        self.stats['total_trades_captured'] = len(self.captured_trades)
        self.stats['last_scan_time'] = datetime.now().isoformat()

        # 保存数据
        self.save_traders_database()

        # 显示结果
        self.display_results()

    def convert_trade_to_signal(self, trade, trader_id):
        """将交易记录转换为信号"""
        try:
            if not trade.get('symbol') or not trade.get('action'):
                return None

            return {
                'id': f"ff_{trader_id}_{trade['symbol']}_{trade['action']}_{hash(trade['raw_text'])}",
                'timestamp': datetime.now().isoformat(),
                'source': f'ForexFactory_Trader_{trader_id}',
                'symbol': trade['symbol'],
                'action': trade['action'],
                'confidence': 0.7,  # FF交易员信号质量较高
                'signal_type': 'trader_history',
                'trader_id': trader_id,
                'original_trade': trade,
                'is_real': True,
                'cost_type': 'free',
                'quality': 'high',
                'platform': 'ForexFactory'
            }

        except Exception as e:
            return None

    def display_results(self):
        """显示结果"""
        print(f"\n{'='*60}")
        print(f"🎯 ForexFactory交易员猎取结果")
        print(f"{'='*60}")
        print(f"📊 发现交易员: {self.stats['total_traders_found']} 个")
        print(f"🔍 深入研究: {self.stats['active_traders']} 个")
        print(f"📈 捕获交易: {self.stats['total_trades_captured']} 个")
        print(f"⏰ 扫描时间: {self.stats['last_scan_time']}")

        if self.traders_database:
            print(f"\n🏆 发现的交易员:")
            for i, (trader_id, info) in enumerate(self.traders_database.items(), 1):
                print(f"{i}. {trader_id}")
                if info.get('stats'):
                    stats = info['stats']
                    win_rate = stats.get('win_rate', 'N/A')
                    total_trades = stats.get('total_trades', 'N/A')
                    print(f"   胜率: {win_rate}% | 交易次数: {total_trades}")

                if info.get('trade_history'):
                    print(f"   交易历史: {len(info['trade_history'])} 条记录")

        if self.captured_trades:
            print(f"\n🎯 最新交易信号:")
            for i, signal in enumerate(self.captured_trades[-3:], 1):
                print(f"{i}. {signal['symbol']} {signal['action']} - 来自 {signal['trader_id']}")
                print(f"   置信度: {signal['confidence']*100:.0f}%")

        print(f"\n📁 数据保存位置:")
        print(f"   交易员数据库: data/ff_traders_database.json")
        print(f"   交易信号: logs/ff_trader_signals.json")
        print(f"   统计信息: data/ff_hunter_stats.json")


async def main():
    """主函数"""
    print("🎯 ForexFactory交易员猎手")
    print("="*50)
    print("📊 ForexFactory是外汇界最权威的社区")
    print("🏆 这里的交易员都是真实的，有实际交易记录")
    print("💎 我们将深入研究他们的交易策略和信号")
    print("="*50)

    hunter = ForexFactoryTraderHunter()

    print("\n🚀 开始猎取ForexFactory交易员...")
    print("⚠️  注意: 这个过程可能需要几分钟时间")
    print("💡 系统会自动处理反爬虫机制")

    try:
        await hunter.hunt_ff_traders()
        print("\n✅ ForexFactory交易员猎取完成！")

    except KeyboardInterrupt:
        print("\n⏹️  用户中断猎取过程")
    except Exception as e:
        print(f"\n❌ 猎取过程出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
