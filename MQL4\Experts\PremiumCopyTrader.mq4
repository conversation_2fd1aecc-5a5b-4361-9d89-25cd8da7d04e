//+------------------------------------------------------------------+
//|                                        PremiumCopyTrader.mq4    |
//|                              付费信号智能跟单系统 - 增强版EA      |
//+------------------------------------------------------------------+
#property copyright "PremiumCopyTrader"
#property version   "2.00"
#property strict

// 输入参数
input double BaseLotSize = 0.01;        // 基础手数
input double MaxLotSize = 1.0;          // 最大手数
input int    MaxSpread = 5;             // 最大点差
input bool   EnableTrading = true;      // 启用交易
input bool   UseRiskManagement = true;  // 启用风险管理
input double RiskPerTrade = 2.0;        // 单笔风险百分比
input double MaxDailyRisk = 5.0;        // 每日最大风险百分比
input int    MaxPositions = 5;          // 最大持仓数
input bool   EnableSignalFilter = true; // 启用信号过滤
input double MinConfidence = 0.6;       // 最小置信度
input string PremiumSignalFile = "premium_signals.csv";  // 付费信号文件
input string StatusFile = "premium_status.txt";         // 状态文件
input string LogFile = "premium_log.txt";               // 日志文件

// 全局变量
datetime lastCheckTime = 0;
int checkInterval = 5;  // 5秒检查一次
double dailyPnL = 0.0;
int dailyTrades = 0;
datetime lastResetDate = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("PremiumCopyTrader EA 启动 - 付费信号智能跟单系统");
    
    // 初始化日志
    WriteLog("EA已启动，等待付费信号...");
    WriteStatus("系统已启动，监控付费信号中...");
    
    // 重置每日统计
    ResetDailyStats();
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    WriteStatus("EA已停止");
    WriteLog("PremiumCopyTrader EA 停止");
    Print("PremiumCopyTrader EA 停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查是否需要重置每日统计
    if(TimeDay(TimeCurrent()) != TimeDay(lastResetDate))
    {
        ResetDailyStats();
    }
    
    // 每5秒检查一次信号文件
    if(TimeCurrent() - lastCheckTime >= checkInterval)
    {
        CheckPremiumSignals();
        UpdateAccountInfo();
        lastCheckTime = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| 检查付费信号文件                                                  |
//+------------------------------------------------------------------+
void CheckPremiumSignals()
{
    if(!EnableTrading) return;
    
    int fileHandle = FileOpen(PremiumSignalFile, FILE_READ|FILE_CSV);
    if(fileHandle == INVALID_HANDLE)
    {
        return; // 文件不存在或无法打开
    }
    
    // 读取信号文件
    while(!FileIsEnding(fileHandle))
    {
        string timestamp = FileReadString(fileHandle);
        string symbol = FileReadString(fileHandle);
        string action = FileReadString(fileHandle);
        double entryPrice = FileReadNumber(fileHandle);
        double stopLoss = FileReadNumber(fileHandle);
        double takeProfit = FileReadNumber(fileHandle);
        double lotSize = FileReadNumber(fileHandle);
        string status = FileReadString(fileHandle);
        
        // 检查是否是新信号且符合当前品种
        if(status == "NEW")
        {
            // 处理信号（不限制品种，支持多品种交易）
            if(ProcessPremiumSignal(symbol, action, entryPrice, stopLoss, takeProfit, lotSize, timestamp))
            {
                // 标记信号为已处理
                MarkSignalProcessed(timestamp);
            }
        }
    }
    
    FileClose(fileHandle);
}

//+------------------------------------------------------------------+
//| 处理付费交易信号                                                  |
//+------------------------------------------------------------------+
bool ProcessPremiumSignal(string symbol, string action, double entry, double sl, double tp, double lots, string timestamp)
{
    // 风险管理检查
    if(!PassRiskCheck(symbol, action))
    {
        WriteLog("信号未通过风险检查: " + symbol + " " + action);
        return false;
    }
    
    // 计算最优手数
    double optimalLots = CalculateOptimalLotSize(symbol, entry, sl, lots);
    if(optimalLots <= 0)
    {
        WriteLog("计算手数失败: " + symbol);
        return false;
    }
    
    // 检查品种是否可交易
    if(!IsSymbolTradeable(symbol))
    {
        WriteLog("品种不可交易: " + symbol);
        return false;
    }
    
    // 检查点差
    double spread = MarketInfo(symbol, MODE_SPREAD);
    if(spread > MaxSpread)
    {
        WriteLog("点差过大，跳过信号: " + symbol + " 点差:" + DoubleToString(spread, 1));
        return false;
    }
    
    // 执行交易
    int orderType = -1;
    double price = 0;
    
    if(action == "BUY")
    {
        orderType = OP_BUY;
        price = MarketInfo(symbol, MODE_ASK);
    }
    else if(action == "SELL")
    {
        orderType = OP_SELL;
        price = MarketInfo(symbol, MODE_BID);
    }
    else
    {
        WriteLog("无效的交易方向: " + action);
        return false;
    }
    
    // 调整止损止盈
    double adjustedSL = AdjustStopLoss(symbol, orderType, price, sl);
    double adjustedTP = AdjustTakeProfit(symbol, orderType, price, tp);
    
    // 发送订单
    int ticket = OrderSend(symbol, orderType, optimalLots, price, 3, adjustedSL, adjustedTP, 
                          "Premium Signal: " + timestamp, 54321, 0, clrNONE);
    
    if(ticket > 0)
    {
        dailyTrades++;
        string message = "付费信号执行成功: " + symbol + " " + action + 
                        " 手数:" + DoubleToString(optimalLots, 2) + 
                        " 价格:" + DoubleToString(price, 5) + 
                        " Ticket:" + IntegerToString(ticket);
        
        WriteStatus(message);
        WriteLog(message);
        Print(message);
        
        return true;
    }
    else
    {
        int error = GetLastError();
        string errorMsg = "付费信号执行失败: " + symbol + " " + action + 
                         " Error:" + IntegerToString(error) + " " + ErrorDescription(error);
        
        WriteStatus(errorMsg);
        WriteLog(errorMsg);
        Print(errorMsg);
        
        return false;
    }
}

//+------------------------------------------------------------------+
//| 风险检查                                                          |
//+------------------------------------------------------------------+
bool PassRiskCheck(string symbol, string action)
{
    if(!UseRiskManagement) return true;
    
    // 检查每日交易次数
    if(dailyTrades >= 20)
    {
        WriteLog("已达到每日最大交易次数限制");
        return false;
    }
    
    // 检查当前持仓数
    int currentPositions = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderMagicNumber() == 54321)
                currentPositions++;
        }
    }
    
    if(currentPositions >= MaxPositions)
    {
        WriteLog("已达到最大持仓数限制: " + IntegerToString(currentPositions));
        return false;
    }
    
    // 检查每日风险
    double accountBalance = AccountBalance();
    double maxDailyRiskAmount = accountBalance * MaxDailyRisk / 100.0;
    
    if(MathAbs(dailyPnL) >= maxDailyRiskAmount)
    {
        WriteLog("已达到每日最大风险限制: " + DoubleToString(dailyPnL, 2));
        return false;
    }
    
    // 检查品种相关性（避免过度集中）
    if(CountSymbolPositions(symbol) >= 2)
    {
        WriteLog("同品种持仓过多: " + symbol);
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算最优手数                                                      |
//+------------------------------------------------------------------+
double CalculateOptimalLotSize(string symbol, double entry, double sl, double suggestedLots)
{
    if(!UseRiskManagement)
    {
        return NormalizeDouble(MathMax(BaseLotSize, MathMin(MaxLotSize, suggestedLots)), 2);
    }
    
    double accountBalance = AccountBalance();
    double riskAmount = accountBalance * RiskPerTrade / 100.0;
    
    // 如果没有止损，使用建议手数
    if(sl <= 0 || entry <= 0)
    {
        return NormalizeDouble(MathMax(BaseLotSize, MathMin(MaxLotSize, BaseLotSize)), 2);
    }
    
    // 计算风险点数
    double riskPips = MathAbs(entry - sl);
    if(riskPips <= 0) return BaseLotSize;
    
    // 获取点值
    double tickValue = MarketInfo(symbol, MODE_TICKVALUE);
    if(tickValue <= 0) tickValue = 1.0;
    
    // 计算最优手数
    double optimalLots = riskAmount / (riskPips * tickValue);
    
    // 应用限制
    optimalLots = NormalizeDouble(MathMax(BaseLotSize, MathMin(MaxLotSize, optimalLots)), 2);
    
    return optimalLots;
}

//+------------------------------------------------------------------+
//| 检查品种是否可交易                                                |
//+------------------------------------------------------------------+
bool IsSymbolTradeable(string symbol)
{
    double bid = MarketInfo(symbol, MODE_BID);
    double ask = MarketInfo(symbol, MODE_ASK);
    
    return (bid > 0 && ask > 0);
}

//+------------------------------------------------------------------+
//| 调整止损价格                                                      |
//+------------------------------------------------------------------+
double AdjustStopLoss(string symbol, int orderType, double price, double sl)
{
    if(sl <= 0) return 0;
    
    double minStopLevel = MarketInfo(symbol, MODE_STOPLEVEL) * MarketInfo(symbol, MODE_POINT);
    
    if(orderType == OP_BUY)
    {
        double minSL = price - minStopLevel;
        return NormalizeDouble(MathMin(sl, minSL), MarketInfo(symbol, MODE_DIGITS));
    }
    else if(orderType == OP_SELL)
    {
        double maxSL = price + minStopLevel;
        return NormalizeDouble(MathMax(sl, maxSL), MarketInfo(symbol, MODE_DIGITS));
    }
    
    return NormalizeDouble(sl, MarketInfo(symbol, MODE_DIGITS));
}

//+------------------------------------------------------------------+
//| 调整止盈价格                                                      |
//+------------------------------------------------------------------+
double AdjustTakeProfit(string symbol, int orderType, double price, double tp)
{
    if(tp <= 0) return 0;
    
    double minStopLevel = MarketInfo(symbol, MODE_STOPLEVEL) * MarketInfo(symbol, MODE_POINT);
    
    if(orderType == OP_BUY)
    {
        double minTP = price + minStopLevel;
        return NormalizeDouble(MathMax(tp, minTP), MarketInfo(symbol, MODE_DIGITS));
    }
    else if(orderType == OP_SELL)
    {
        double maxTP = price - minStopLevel;
        return NormalizeDouble(MathMin(tp, maxTP), MarketInfo(symbol, MODE_DIGITS));
    }
    
    return NormalizeDouble(tp, MarketInfo(symbol, MODE_DIGITS));
}

//+------------------------------------------------------------------+
//| 统计同品种持仓数                                                  |
//+------------------------------------------------------------------+
int CountSymbolPositions(string symbol)
{
    int count = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == symbol && OrderMagicNumber() == 54321)
                count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| 重置每日统计                                                      |
//+------------------------------------------------------------------+
void ResetDailyStats()
{
    dailyPnL = 0.0;
    dailyTrades = 0;
    lastResetDate = TimeCurrent();
    
    WriteLog("每日统计已重置");
}

//+------------------------------------------------------------------+
//| 更新账户信息                                                      |
//+------------------------------------------------------------------+
void UpdateAccountInfo()
{
    // 计算当日盈亏
    double todayPnL = 0.0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderMagicNumber() == 54321)
                todayPnL += OrderProfit() + OrderSwap() + OrderCommission();
        }
    }
    
    dailyPnL = todayPnL;
    
    // 写入账户信息文件
    string accountFile = "premium_account_info.txt";
    int fileHandle = FileOpen(accountFile, FILE_WRITE|FILE_TXT);
    
    if(fileHandle != INVALID_HANDLE)
    {
        FileWrite(fileHandle, "Account: " + IntegerToString(AccountNumber()));
        FileWrite(fileHandle, "Balance: " + DoubleToString(AccountBalance(), 2));
        FileWrite(fileHandle, "Equity: " + DoubleToString(AccountEquity(), 2));
        FileWrite(fileHandle, "Margin: " + DoubleToString(AccountMargin(), 2));
        FileWrite(fileHandle, "Free Margin: " + DoubleToString(AccountFreeMargin(), 2));
        FileWrite(fileHandle, "Today PnL: " + DoubleToString(dailyPnL, 2));
        FileWrite(fileHandle, "Today Trades: " + IntegerToString(dailyTrades));
        FileWrite(fileHandle, "Open Positions: " + IntegerToString(OrdersTotal()));
        FileWrite(fileHandle, "Timestamp: " + TimeToString(TimeCurrent()));
        
        FileClose(fileHandle);
    }
}

//+------------------------------------------------------------------+
//| 标记信号为已处理                                                  |
//+------------------------------------------------------------------+
void MarkSignalProcessed(string timestamp)
{
    WriteLog("信号已处理: " + timestamp);
}

//+------------------------------------------------------------------+
//| 写入状态文件                                                      |
//+------------------------------------------------------------------+
void WriteStatus(string message)
{
    int fileHandle = FileOpen(StatusFile, FILE_WRITE|FILE_TXT);
    if(fileHandle != INVALID_HANDLE)
    {
        FileWrite(fileHandle, TimeToString(TimeCurrent()) + ": " + message);
        FileClose(fileHandle);
    }
}

//+------------------------------------------------------------------+
//| 写入日志文件                                                      |
//+------------------------------------------------------------------+
void WriteLog(string message)
{
    int fileHandle = FileOpen(LogFile, FILE_WRITE|FILE_TXT);
    if(fileHandle != INVALID_HANDLE)
    {
        FileWrite(fileHandle, TimeToString(TimeCurrent()) + ": " + message);
        FileClose(fileHandle);
    }
}

//+------------------------------------------------------------------+
//| 获取错误描述                                                      |
//+------------------------------------------------------------------+
string ErrorDescription(int errorCode)
{
    switch(errorCode)
    {
        case 0: return "无错误";
        case 1: return "无错误，但结果未知";
        case 2: return "一般错误";
        case 3: return "无效参数";
        case 4: return "交易服务器忙";
        case 5: return "旧版本的客户端";
        case 6: return "无连接";
        case 7: return "权限不够";
        case 8: return "请求过于频繁";
        case 9: return "无效操作";
        case 130: return "无效止损";
        case 131: return "无效交易量";
        case 132: return "市场关闭";
        case 133: return "交易被禁止";
        case 134: return "资金不足";
        case 135: return "价格改变";
        case 136: return "无价格";
        case 137: return "经纪商忙";
        case 138: return "重新报价";
        case 139: return "订单被锁定";
        case 140: return "只允许买入";
        case 141: return "请求过多";
        default: return "未知错误 " + IntegerToString(errorCode);
    }
}
