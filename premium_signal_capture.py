#!/usr/bin/env python3
"""
付费信号捕获系统 - 绕过API限制，直接捕获付费渠道信号
支持多种付费信号源的智能捕获
"""

import asyncio
import json
import logging
import re
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import undetected_chromedriver as uc


class PremiumSignalCapture:
    """付费信号捕获器"""
    
    def __init__(self, config_file="config/premium_config.json"):
        self.config = self.load_config(config_file)
        self.logger = self.setup_logger()
        self.drivers = {}
        self.session = requests.Session()
        
        # 设置请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 信号缓存
        self.processed_signals = set()
        self.signal_queue = asyncio.Queue()
    
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.create_default_config(config_file)
    
    def create_default_config(self, config_file):
        """创建默认配置"""
        config = {
            "premium_sources": {
                "telegram_premium": {
                    "enabled": True,
                    "channels": [
                        {
                            "name": "VIP外汇信号",
                            "url": "https://t.me/s/vip_forex_signals",
                            "login_required": False,
                            "weight": 0.8,
                            "capture_method": "web_scraping"
                        },
                        {
                            "name": "专业交易室",
                            "url": "https://t.me/joinchat/xxxxx",
                            "login_required": True,
                            "phone": "YOUR_PHONE_NUMBER",
                            "weight": 0.9,
                            "capture_method": "selenium"
                        }
                    ]
                },
                
                "myfxbook_premium": {
                    "enabled": True,
                    "traders": [
                        {
                            "name": "顶级交易师1",
                            "url": "https://www.myfxbook.com/members/trader123",
                            "login_required": True,
                            "username": "YOUR_USERNAME",
                            "password": "YOUR_PASSWORD",
                            "weight": 0.85
                        }
                    ]
                },
                
                "discord_premium": {
                    "enabled": True,
                    "servers": [
                        {
                            "name": "专业交易Discord",
                            "invite_link": "https://discord.gg/xxxxx",
                            "channel_id": "signal-channel",
                            "token": "YOUR_DISCORD_TOKEN",
                            "weight": 0.7
                        }
                    ]
                },
                
                "whatsapp_premium": {
                    "enabled": False,
                    "groups": [
                        {
                            "name": "VIP交易群",
                            "phone": "YOUR_PHONE",
                            "group_id": "GROUP_ID",
                            "weight": 0.8
                        }
                    ]
                }
            },
            
            "capture_settings": {
                "check_interval": 30,
                "max_retries": 3,
                "timeout": 30,
                "use_proxy": False,
                "proxy_list": [],
                "anti_detection": True,
                "headless": False
            },
            
            "signal_filtering": {
                "min_confidence": 0.6,
                "required_fields": ["symbol", "action"],
                "allowed_symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"],
                "max_signal_age_minutes": 10
            }
        }
        
        # 保存默认配置
        Path("config").mkdir(exist_ok=True)
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        return config
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('PremiumCapture')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def create_driver(self, headless=True, anti_detection=True):
        """创建浏览器驱动"""
        try:
            if anti_detection:
                # 使用undetected-chromedriver绕过检测
                options = uc.ChromeOptions()
            else:
                options = Options()
            
            if headless:
                options.add_argument('--headless')
            
            # 反检测设置
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 设置用户代理
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            if anti_detection:
                driver = uc.Chrome(options=options)
            else:
                driver = webdriver.Chrome(options=options)
            
            # 执行反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
            
        except Exception as e:
            self.logger.error(f"创建浏览器驱动失败: {e}")
            return None
    
    async def capture_telegram_premium(self):
        """捕获Telegram付费频道信号"""
        signals = []
        
        for channel in self.config['premium_sources']['telegram_premium']['channels']:
            if not channel.get('enabled', True):
                continue
            
            try:
                self.logger.info(f"正在捕获Telegram频道: {channel['name']}")
                
                if channel.get('login_required', False):
                    # 需要登录的私有频道
                    channel_signals = await self.capture_private_telegram(channel)
                else:
                    # 公开频道，使用网页抓取
                    channel_signals = await self.capture_public_telegram(channel)
                
                signals.extend(channel_signals)
                
            except Exception as e:
                self.logger.error(f"捕获Telegram频道 {channel['name']} 失败: {e}")
        
        return signals
    
    async def capture_private_telegram(self, channel):
        """捕获私有Telegram频道"""
        signals = []
        
        try:
            # 使用Selenium模拟登录
            driver = self.create_driver(headless=False, anti_detection=True)
            if not driver:
                return signals
            
            # 访问Telegram Web
            driver.get("https://web.telegram.org/k/")
            
            # 等待页面加载
            await asyncio.sleep(5)
            
            # 这里需要手动登录或使用已保存的session
            # 实际实现中可以保存cookies来避免重复登录
            
            # 访问目标频道
            driver.get(channel['url'])
            await asyncio.sleep(3)
            
            # 获取最新消息
            messages = driver.find_elements(By.CLASS_NAME, "message")
            
            for message in messages[-10:]:  # 最近10条消息
                try:
                    text = message.text
                    signal = self.parse_signal_text(text, channel['name'])
                    if signal:
                        signal['weight'] = channel.get('weight', 0.5)
                        signals.append(signal)
                except Exception as e:
                    continue
            
            driver.quit()
            
        except Exception as e:
            self.logger.error(f"捕获私有Telegram频道失败: {e}")
        
        return signals
    
    async def capture_public_telegram(self, channel):
        """捕获公开Telegram频道"""
        signals = []
        
        try:
            # 使用requests获取页面内容
            response = self.session.get(channel['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找消息内容
            messages = soup.find_all('div', class_='tgme_widget_message_text')
            
            for msg in messages[-15:]:  # 最近15条消息
                text = msg.get_text()
                signal = self.parse_signal_text(text, channel['name'])
                if signal:
                    signal['weight'] = channel.get('weight', 0.5)
                    signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"捕获公开Telegram频道失败: {e}")
        
        return signals
    
    async def capture_myfxbook_premium(self):
        """捕获Myfxbook付费交易师信号"""
        signals = []
        
        for trader in self.config['premium_sources']['myfxbook_premium']['traders']:
            if not trader.get('enabled', True):
                continue
            
            try:
                self.logger.info(f"正在捕获Myfxbook交易师: {trader['name']}")
                
                # 使用Selenium登录Myfxbook
                driver = self.create_driver(headless=True, anti_detection=True)
                if not driver:
                    continue
                
                # 登录Myfxbook
                driver.get("https://www.myfxbook.com/login")
                await asyncio.sleep(3)
                
                # 输入用户名和密码
                username_field = driver.find_element(By.NAME, "email")
                password_field = driver.find_element(By.NAME, "password")
                
                username_field.send_keys(trader['username'])
                password_field.send_keys(trader['password'])
                
                # 点击登录
                login_button = driver.find_element(By.XPATH, "//input[@type='submit']")
                login_button.click()
                
                await asyncio.sleep(5)
                
                # 访问交易师页面
                driver.get(trader['url'])
                await asyncio.sleep(3)
                
                # 获取最新交易记录
                trades = driver.find_elements(By.CSS_SELECTOR, ".tradingHistoryTable tr")
                
                for trade in trades[1:11]:  # 跳过表头，取前10条
                    try:
                        cells = trade.find_elements(By.TAG_NAME, "td")
                        if len(cells) >= 6:
                            signal = self.parse_myfxbook_trade(cells, trader['name'])
                            if signal:
                                signal['weight'] = trader.get('weight', 0.5)
                                signals.append(signal)
                    except Exception as e:
                        continue
                
                driver.quit()
                
            except Exception as e:
                self.logger.error(f"捕获Myfxbook交易师 {trader['name']} 失败: {e}")
        
        return signals
    
    async def capture_discord_premium(self):
        """捕获Discord付费服务器信号"""
        signals = []
        
        for server in self.config['premium_sources']['discord_premium']['servers']:
            if not server.get('enabled', True):
                continue
            
            try:
                self.logger.info(f"正在捕获Discord服务器: {server['name']}")
                
                # 使用Discord API或网页版
                # 这里实现Discord信号捕获逻辑
                # 由于Discord的反爬虫机制，可能需要使用官方API
                
                pass  # 具体实现根据需要添加
                
            except Exception as e:
                self.logger.error(f"捕获Discord服务器 {server['name']} 失败: {e}")
        
        return signals
    
    def parse_signal_text(self, text, source):
        """解析信号文本"""
        try:
            # 增强的信号解析，支持更多格式
            signal_patterns = {
                'symbol': [
                    r'\b(EUR/USD|EURUSD|欧美)\b',
                    r'\b(GBP/USD|GBPUSD|镑美)\b',
                    r'\b(USD/JPY|USDJPY|美日)\b',
                    r'\b(XAU/USD|XAUUSD|黄金|GOLD)\b',
                    r'\b(XAG/USD|XAGUSD|白银|SILVER)\b'
                ],
                'action': [
                    r'(?i)\b(BUY|LONG|买入|做多|看涨)\b',
                    r'(?i)\b(SELL|SHORT|卖出|做空|看跌)\b'
                ],
                'entry': [
                    r'(?i)(?:entry|入场|进场|@).*?(\d+\.?\d*)',
                    r'(?i)(?:price|价格).*?(\d+\.?\d*)'
                ],
                'sl': [
                    r'(?i)(?:sl|stop loss|止损).*?(\d+\.?\d*)'
                ],
                'tp': [
                    r'(?i)(?:tp|take profit|止盈|目标).*?(\d+\.?\d*)'
                ]
            }
            
            # 提取交易品种
            symbol = None
            for pattern in signal_patterns['symbol']:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    symbol = match.group().replace('/', '').upper()
                    # 标准化符号
                    symbol_map = {
                        '欧美': 'EURUSD', '镑美': 'GBPUSD', '美日': 'USDJPY',
                        '黄金': 'XAUUSD', '白银': 'XAGUSD', 'GOLD': 'XAUUSD', 'SILVER': 'XAGUSD'
                    }
                    symbol = symbol_map.get(symbol, symbol)
                    break
            
            if not symbol:
                return None
            
            # 提取交易方向
            action = None
            for pattern in signal_patterns['action']:
                match = re.search(pattern, text)
                if match:
                    action_text = match.group().upper()
                    if action_text in ['BUY', 'LONG', '买入', '做多', '看涨']:
                        action = 'BUY'
                    elif action_text in ['SELL', 'SHORT', '卖出', '做空', '看跌']:
                        action = 'SELL'
                    break
            
            if not action:
                return None
            
            # 提取价格信息
            entry_price = None
            for pattern in signal_patterns['entry']:
                match = re.search(pattern, text)
                if match:
                    try:
                        entry_price = float(match.group(1))
                        break
                    except (ValueError, IndexError):
                        continue
            
            stop_loss = None
            for pattern in signal_patterns['sl']:
                match = re.search(pattern, text)
                if match:
                    try:
                        stop_loss = float(match.group(1))
                        break
                    except (ValueError, IndexError):
                        continue
            
            take_profit = None
            for pattern in signal_patterns['tp']:
                match = re.search(pattern, text)
                if match:
                    try:
                        take_profit = float(match.group(1))
                        break
                    except (ValueError, IndexError):
                        continue
            
            # 计算置信度
            confidence = 0.5
            if entry_price:
                confidence += 0.1
            if stop_loss:
                confidence += 0.1
            if take_profit:
                confidence += 0.1
            if len(text) > 50:  # 详细的信号描述
                confidence += 0.1
            
            return {
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'confidence': confidence,
                'raw_text': text[:200]  # 保留前200个字符
            }
            
        except Exception as e:
            self.logger.error(f"解析信号文本失败: {e}")
            return None
    
    def parse_myfxbook_trade(self, cells, source):
        """解析Myfxbook交易记录"""
        try:
            # 假设表格结构：时间、品种、方向、手数、开仓价、平仓价、盈亏
            if len(cells) < 6:
                return None
            
            symbol = cells[1].text.strip()
            action_text = cells[2].text.strip().upper()
            open_price = float(cells[4].text.strip())
            
            action = 'BUY' if 'BUY' in action_text else 'SELL'
            
            return {
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'symbol': symbol,
                'action': action,
                'entry_price': open_price,
                'stop_loss': None,
                'take_profit': None,
                'confidence': 0.8,  # Myfxbook数据质量较高
                'raw_text': f"Myfxbook交易: {symbol} {action} @ {open_price}"
            }
            
        except Exception as e:
            return None
    
    async def run_capture_cycle(self):
        """运行一次捕获周期"""
        all_signals = []
        
        try:
            # 捕获Telegram信号
            if self.config['premium_sources']['telegram_premium']['enabled']:
                telegram_signals = await self.capture_telegram_premium()
                all_signals.extend(telegram_signals)
            
            # 捕获Myfxbook信号
            if self.config['premium_sources']['myfxbook_premium']['enabled']:
                myfxbook_signals = await self.capture_myfxbook_premium()
                all_signals.extend(myfxbook_signals)
            
            # 捕获Discord信号
            if self.config['premium_sources']['discord_premium']['enabled']:
                discord_signals = await self.capture_discord_premium()
                all_signals.extend(discord_signals)
            
            # 过滤和去重
            filtered_signals = self.filter_signals(all_signals)
            
            # 保存信号
            if filtered_signals:
                self.save_signals(filtered_signals)
                self.logger.info(f"捕获到 {len(filtered_signals)} 个高质量信号")
            
            return filtered_signals
            
        except Exception as e:
            self.logger.error(f"捕获周期执行失败: {e}")
            return []
    
    def filter_signals(self, signals):
        """过滤信号"""
        filtered = []
        
        for signal in signals:
            try:
                # 检查置信度
                if signal.get('confidence', 0) < self.config['signal_filtering']['min_confidence']:
                    continue
                
                # 检查必要字段
                required_fields = self.config['signal_filtering']['required_fields']
                if not all(signal.get(field) for field in required_fields):
                    continue
                
                # 检查允许的交易品种
                allowed_symbols = self.config['signal_filtering']['allowed_symbols']
                if signal.get('symbol') not in allowed_symbols:
                    continue
                
                # 检查是否已处理过
                signal_hash = f"{signal['source']}_{signal['symbol']}_{signal['action']}_{signal['timestamp']}"
                if signal_hash in self.processed_signals:
                    continue
                
                self.processed_signals.add(signal_hash)
                filtered.append(signal)
                
            except Exception as e:
                continue
        
        return filtered
    
    def save_signals(self, signals):
        """保存信号到文件"""
        try:
            # 保存为CSV格式供MT4读取
            import csv
            
            Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
            
            with open('MQL4/Files/premium_signals.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                for signal in signals:
                    writer.writerow([
                        signal['timestamp'],
                        signal['symbol'],
                        signal['action'],
                        signal.get('entry_price', 0),
                        signal.get('stop_loss', 0),
                        signal.get('take_profit', 0),
                        0.01,  # 默认手数
                        'NEW'  # 状态
                    ])
            
            # 同时保存详细信息
            with open('logs/premium_signals.json', 'a', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False) + '\n')
            
        except Exception as e:
            self.logger.error(f"保存信号失败: {e}")
    
    async def run_continuous(self):
        """持续运行捕获"""
        self.logger.info("🚀 开始持续捕获付费信号...")
        
        while True:
            try:
                signals = await self.run_capture_cycle()
                
                if signals:
                    self.logger.info(f"✅ 本轮捕获 {len(signals)} 个信号")
                else:
                    self.logger.info("ℹ️  本轮未发现新信号")
                
                # 等待下一次捕获
                interval = self.config['capture_settings']['check_interval']
                await asyncio.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("用户中断，停止捕获")
                break
            except Exception as e:
                self.logger.error(f"捕获过程出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟
    
    def cleanup(self):
        """清理资源"""
        for driver in self.drivers.values():
            try:
                driver.quit()
            except:
                pass


async def main():
    """主函数"""
    capture = PremiumSignalCapture()
    
    try:
        await capture.run_continuous()
    finally:
        capture.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
