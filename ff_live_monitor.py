#!/usr/bin/env python3
"""
ForexFactory实时监控系统 - 持续监控顶级交易员并生成实时跟单信号
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
import time

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from bs4 import BeautifulSoup
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False


class FFLiveMonitor:
    """ForexFactory实时监控系统"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.trades_url = "https://www.forexfactory.com/trades"
        
        # 监控的顶级交易员
        self.target_traders = [
            'fusiongoldfx',    # 评分61.0, 100%胜率
            'theTrip75',       # 评分56.0, 100%胜率
            'pipclubtrade',    # 评分20, 黄金专家
            'Dominicus',       # BTC交易员
            'geo1683'          # EUR/USD交易员
        ]
        
        # 历史交易记录
        self.historical_trades = {}
        self.new_signals = []
        
        # 监控统计
        self.monitor_stats = {
            'monitoring_cycles': 0,
            'new_trades_detected': 0,
            'signals_generated': 0,
            'last_update': None
        }
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('FFLiveMonitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/ff_live_monitor.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def load_historical_data(self):
        """加载历史数据"""
        try:
            analysis_file = Path("data/ff_analysis_results.json")
            if analysis_file.exists():
                with open(analysis_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 提取历史交易
                for trader_name, profile in data.get('traders_profiles', {}).items():
                    if trader_name in self.target_traders:
                        self.historical_trades[trader_name] = profile.get('trades', [])
                
                self.logger.info(f"✅ 已加载 {len(self.historical_trades)} 个交易员的历史数据")
                return True
            
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")
        
        return False
    
    def create_monitor_driver(self):
        """创建监控驱动"""
        if not SELENIUM_AVAILABLE:
            self.logger.error("需要安装依赖: pip install undetected-chromedriver selenium beautifulsoup4")
            return None
        
        try:
            options = uc.ChromeOptions()
            options.add_argument('--headless')  # 后台运行
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            
            driver = uc.Chrome(options=options)
            return driver
            
        except Exception as e:
            self.logger.error(f"创建监控驱动失败: {e}")
            return None
    
    async def capture_current_trades(self):
        """捕获当前交易数据"""
        self.logger.info("📊 捕获当前交易数据...")
        
        driver = self.create_monitor_driver()
        if not driver:
            return []
        
        current_trades = []
        
        try:
            # 访问交易页面
            driver.get(self.trades_url)
            await asyncio.sleep(5)  # 等待页面加载
            
            # 检查是否需要处理Cloudflare
            page_source = driver.page_source.lower()
            if 'cloudflare' in page_source or '验证您是真人' in page_source:
                self.logger.warning("⚠️  遇到Cloudflare验证，跳过本轮监控")
                return []
            
            # 解析页面
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # 查找表格数据
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 4:
                        content = [cell.get_text().strip() for cell in cells]
                        
                        # 跳过表头
                        if content[0] == 'Trade' or 'Saving' in content[0]:
                            continue
                        
                        # 解析交易信息
                        trade_info = self.parse_trade_info(content)
                        if trade_info and trade_info['trader'] in self.target_traders:
                            current_trades.append(trade_info)
            
            self.logger.info(f"✅ 捕获到 {len(current_trades)} 个目标交易员的交易")
            
        except Exception as e:
            self.logger.error(f"捕获交易数据失败: {e}")
        
        finally:
            try:
                driver.quit()
            except:
                pass
        
        return current_trades
    
    def parse_trade_info(self, content):
        """解析交易信息"""
        try:
            import re
            
            trade_text = content[0]
            trader_name = content[1]
            return_text = content[2]
            pips_text = content[3]
            
            # 解析交易品种
            symbol_match = re.search(r'(BTC/USD|EUR/USD|Xauusdc|GBP/USD|USD/JPY)', trade_text, re.IGNORECASE)
            symbol = symbol_match.group(1).upper() if symbol_match else None
            if symbol == 'XAUUSDC':
                symbol = 'XAUUSD'
            
            # 解析交易方向
            action = None
            if re.search(r'\b(BUY|Long)\b', trade_text, re.IGNORECASE):
                action = 'BUY'
            elif re.search(r'\b(SELL|Short)\b', trade_text, re.IGNORECASE):
                action = 'SELL'
            
            # 解析状态
            status = 'unknown'
            if 'Opened' in trade_text:
                status = 'open'
            elif 'Closed' in trade_text:
                status = 'closed'
            
            # 解析时间
            time_match = re.search(r'(\d+)\s*min\s*ago', trade_text)
            minutes_ago = int(time_match.group(1)) if time_match else 0
            
            if symbol and action and trader_name:
                return {
                    'symbol': symbol,
                    'action': action,
                    'trader': trader_name,
                    'status': status,
                    'minutes_ago': minutes_ago,
                    'timestamp': datetime.now() - timedelta(minutes=minutes_ago),
                    'raw_text': trade_text,
                    'trade_id': f"{trader_name}_{symbol}_{action}_{minutes_ago}"
                }
        
        except Exception as e:
            pass
        
        return None
    
    def detect_new_trades(self, current_trades):
        """检测新交易"""
        new_trades = []
        
        for trade in current_trades:
            trader_name = trade['trader']
            trade_id = trade['trade_id']
            
            # 检查是否是新交易
            historical = self.historical_trades.get(trader_name, [])
            
            # 简单的新交易检测：如果交易时间很近（<10分钟）且之前没有记录
            is_new = True
            for hist_trade in historical:
                if (hist_trade.get('symbol') == trade['symbol'] and 
                    hist_trade.get('action') == trade['action'] and
                    abs(hist_trade.get('minutes_ago', 999) - trade['minutes_ago']) < 5):
                    is_new = False
                    break
            
            if is_new and trade['minutes_ago'] < 10:  # 10分钟内的新交易
                new_trades.append(trade)
                self.logger.info(f"🆕 检测到新交易: {trader_name} {trade['symbol']} {trade['action']}")
        
        return new_trades
    
    def generate_live_signals(self, new_trades):
        """生成实时信号"""
        signals = []
        
        # 交易员评分（基于之前的分析）
        trader_scores = {
            'fusiongoldfx': 61.0,
            'theTrip75': 56.0,
            'pipclubtrade': 20.0,
            'Dominicus': 15.0,
            'geo1683': 15.0
        }
        
        for trade in new_trades:
            trader_name = trade['trader']
            score = trader_scores.get(trader_name, 10.0)
            
            # 计算置信度
            confidence = min(0.9, max(0.3, score / 100))
            
            # 只为开仓交易生成信号
            if trade['status'] == 'open':
                signal = {
                    'id': f"ff_live_{trade['trade_id']}_{int(datetime.now().timestamp())}",
                    'timestamp': datetime.now().isoformat(),
                    'source': f'FF_Live_{trader_name}',
                    'symbol': trade['symbol'],
                    'action': trade['action'],
                    'confidence': confidence,
                    'signal_type': 'live_follow',
                    'trader_score': score,
                    'minutes_since_entry': trade['minutes_ago'],
                    'is_real': True,
                    'platform': 'ForexFactory_Live',
                    'urgency': 'high' if trade['minutes_ago'] < 5 else 'medium',
                    'follow_reason': f"实时跟随评分{score}的交易员{trader_name}"
                }
                
                signals.append(signal)
        
        return signals
    
    def save_live_signals(self, signals):
        """保存实时信号"""
        if not signals:
            return
        
        try:
            Path("logs").mkdir(exist_ok=True)
            
            # 追加到实时信号文件
            with open('logs/ff_live_signals.json', 'a', encoding='utf-8') as f:
                for signal in signals:
                    f.write(json.dumps(signal, ensure_ascii=False, default=str) + '\n')
            
            # 保存最新信号（覆盖）
            with open('logs/ff_latest_signals.json', 'w', encoding='utf-8') as f:
                json.dump(signals, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"✅ 已保存 {len(signals)} 个实时信号")
            
        except Exception as e:
            self.logger.error(f"保存实时信号失败: {e}")
    
    def update_historical_data(self, new_trades):
        """更新历史数据"""
        for trade in new_trades:
            trader_name = trade['trader']
            if trader_name not in self.historical_trades:
                self.historical_trades[trader_name] = []
            
            self.historical_trades[trader_name].append(trade)
    
    async def monitor_cycle(self):
        """单次监控周期"""
        self.monitor_stats['monitoring_cycles'] += 1
        cycle_num = self.monitor_stats['monitoring_cycles']
        
        self.logger.info(f"🔄 开始第 {cycle_num} 轮监控...")
        
        try:
            # 捕获当前交易
            current_trades = await self.capture_current_trades()
            
            if current_trades:
                # 检测新交易
                new_trades = self.detect_new_trades(current_trades)
                
                if new_trades:
                    self.monitor_stats['new_trades_detected'] += len(new_trades)
                    
                    # 生成实时信号
                    live_signals = self.generate_live_signals(new_trades)
                    
                    if live_signals:
                        self.monitor_stats['signals_generated'] += len(live_signals)
                        self.new_signals.extend(live_signals)
                        
                        # 保存信号
                        self.save_live_signals(live_signals)
                        
                        # 显示新信号
                        print(f"\n🚨 第 {cycle_num} 轮发现 {len(live_signals)} 个新信号:")
                        for i, signal in enumerate(live_signals, 1):
                            urgency = "🔥" if signal['urgency'] == 'high' else "⚡"
                            print(f"{urgency} {i}. {signal['symbol']} {signal['action']} - 置信度: {signal['confidence']*100:.0f}%")
                            print(f"   跟随: {signal['source']} ({signal['minutes_since_entry']}分钟前开仓)")
                    
                    # 更新历史数据
                    self.update_historical_data(new_trades)
                
                else:
                    print(f"⚪ 第 {cycle_num} 轮: 无新交易")
            
            else:
                print(f"⚠️  第 {cycle_num} 轮: 数据捕获失败")
            
            self.monitor_stats['last_update'] = datetime.now().isoformat()
            
        except Exception as e:
            self.logger.error(f"监控周期 {cycle_num} 出错: {e}")
    
    async def start_live_monitoring(self, interval_minutes=5):
        """启动实时监控"""
        self.logger.info("🚀 启动ForexFactory实时监控...")
        
        # 加载历史数据
        self.load_historical_data()
        
        print(f"📊 实时监控系统已启动")
        print(f"🎯 监控目标: {', '.join(self.target_traders)}")
        print(f"⏰ 监控间隔: {interval_minutes} 分钟")
        print(f"🔄 按 Ctrl+C 停止监控")
        print("="*60)
        
        try:
            while True:
                # 执行监控周期
                await self.monitor_cycle()
                
                # 显示统计
                stats = self.monitor_stats
                print(f"📊 统计: 周期{stats['monitoring_cycles']} | 新交易{stats['new_trades_detected']} | 信号{stats['signals_generated']}")
                
                # 等待下一个周期
                print(f"⏰ 等待 {interval_minutes} 分钟后进行下一轮监控...")
                await asyncio.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print(f"\n⏹️  用户停止监控")
            self.logger.info("用户停止实时监控")
            
            # 显示最终统计
            print(f"\n📊 监控统计:")
            print(f"   总监控周期: {stats['monitoring_cycles']}")
            print(f"   检测新交易: {stats['new_trades_detected']}")
            print(f"   生成信号: {stats['signals_generated']}")
            
            if self.new_signals:
                print(f"\n🎯 本次监控生成的信号:")
                for i, signal in enumerate(self.new_signals[-5:], 1):  # 显示最后5个
                    print(f"{i}. {signal['symbol']} {signal['action']} - {signal['source']}")


async def main():
    """主函数"""
    print("📊 ForexFactory实时监控系统")
    print("="*50)
    print("🎯 监控顶级交易员的实时交易")
    print("🚨 自动生成跟单信号")
    print("⚡ 实时推送到MT4系统")
    print("="*50)
    
    if not SELENIUM_AVAILABLE:
        print("❌ 需要安装依赖:")
        print("pip install undetected-chromedriver selenium beautifulsoup4")
        return
    
    monitor = FFLiveMonitor()
    
    print("\n🚀 选择监控模式:")
    print("1. 快速监控 (2分钟间隔)")
    print("2. 标准监控 (5分钟间隔)")
    print("3. 慢速监控 (10分钟间隔)")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    intervals = {'1': 2, '2': 5, '3': 10}
    interval = intervals.get(choice, 5)
    
    print(f"\n🎯 启动 {interval} 分钟间隔监控...")
    await monitor.start_live_monitoring(interval)


if __name__ == "__main__":
    asyncio.run(main())
