#!/usr/bin/env python3
"""
交易员管理工具 - 独立的交易员发现、评估和配置管理
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
import sys


class TraderManager:
    """交易员管理器"""
    
    def __init__(self):
        self.logger = self.setup_logger()
        self.config_file = Path("data/monitor_config.json")
        self.analysis_file = Path("data/trader_analysis.json")
        
        # 当前配置
        self.current_config = self.load_current_config()
    
    def setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('TraderManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            Path("logs").mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler('logs/trader_manager.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def load_current_config(self):
        """加载当前配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
        
        return {
            'target_traders': [],
            'trader_scores': {},
            'update_time': None,
            'selection_criteria': {}
        }
    
    def save_config(self, config):
        """保存配置"""
        try:
            Path("data").mkdir(exist_ok=True)
            
            config['update_time'] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.current_config = config
            self.logger.info("✅ 配置已保存")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def display_current_config(self):
        """显示当前配置"""
        print(f"\n{'='*60}")
        print(f"📊 当前监控配置")
        print(f"{'='*60}")
        
        if not self.current_config['target_traders']:
            print("❌ 暂无配置的交易员")
            return
        
        print(f"👥 监控交易员 ({len(self.current_config['target_traders'])} 个):")
        print("-" * 60)
        print(f"{'序号':<4} {'交易员':<20} {'评分':<8} {'状态'}")
        print("-" * 60)
        
        for i, trader in enumerate(self.current_config['target_traders'], 1):
            score = self.current_config['trader_scores'].get(trader, 0)
            status = "✅ 活跃" if score > 0 else "⚠️  未知"
            print(f"{i:<4} {trader:<20} {score:<8} {status}")
        
        update_time = self.current_config.get('update_time')
        if update_time:
            update_dt = datetime.fromisoformat(update_time)
            print(f"\n🕒 最后更新: {update_dt.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"{'='*60}")
    
    def add_trader_manual(self):
        """手动添加交易员"""
        print("\n➕ 手动添加交易员")
        print("-" * 30)
        
        while True:
            trader_name = input("请输入交易员名称 (或输入 'q' 退出): ").strip()
            
            if trader_name.lower() == 'q':
                break
            
            if not trader_name:
                print("❌ 交易员名称不能为空")
                continue
            
            if trader_name in self.current_config['target_traders']:
                print(f"⚠️  交易员 {trader_name} 已存在")
                continue
            
            # 输入评分
            while True:
                try:
                    score_input = input(f"请输入 {trader_name} 的评分 (0-100): ").strip()
                    score = float(score_input)
                    if 0 <= score <= 100:
                        break
                    else:
                        print("❌ 评分必须在 0-100 之间")
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            # 添加到配置
            self.current_config['target_traders'].append(trader_name)
            self.current_config['trader_scores'][trader_name] = score
            
            print(f"✅ 已添加交易员: {trader_name} (评分: {score})")
            
            # 询问是否继续
            continue_add = input("是否继续添加? (y/n): ").strip().lower()
            if continue_add != 'y':
                break
        
        # 保存配置
        if self.save_config(self.current_config):
            print("✅ 配置已保存")
    
    def remove_trader(self):
        """移除交易员"""
        if not self.current_config['target_traders']:
            print("❌ 暂无交易员可移除")
            return
        
        print("\n➖ 移除交易员")
        print("-" * 30)
        
        # 显示当前交易员列表
        for i, trader in enumerate(self.current_config['target_traders'], 1):
            score = self.current_config['trader_scores'].get(trader, 0)
            print(f"{i}. {trader} (评分: {score})")
        
        while True:
            try:
                choice = input("\n请选择要移除的交易员序号 (或输入 'q' 退出): ").strip()
                
                if choice.lower() == 'q':
                    break
                
                index = int(choice) - 1
                if 0 <= index < len(self.current_config['target_traders']):
                    trader_name = self.current_config['target_traders'][index]
                    
                    # 确认移除
                    confirm = input(f"确认移除交易员 {trader_name}? (y/n): ").strip().lower()
                    if confirm == 'y':
                        self.current_config['target_traders'].remove(trader_name)
                        if trader_name in self.current_config['trader_scores']:
                            del self.current_config['trader_scores'][trader_name]
                        
                        print(f"✅ 已移除交易员: {trader_name}")
                        
                        # 保存配置
                        if self.save_config(self.current_config):
                            print("✅ 配置已保存")
                        break
                    else:
                        print("❌ 取消移除")
                else:
                    print("❌ 无效的序号")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def update_trader_score(self):
        """更新交易员评分"""
        if not self.current_config['target_traders']:
            print("❌ 暂无交易员可更新")
            return
        
        print("\n📊 更新交易员评分")
        print("-" * 30)
        
        # 显示当前交易员列表
        for i, trader in enumerate(self.current_config['target_traders'], 1):
            score = self.current_config['trader_scores'].get(trader, 0)
            print(f"{i}. {trader} (当前评分: {score})")
        
        while True:
            try:
                choice = input("\n请选择要更新的交易员序号 (或输入 'q' 退出): ").strip()
                
                if choice.lower() == 'q':
                    break
                
                index = int(choice) - 1
                if 0 <= index < len(self.current_config['target_traders']):
                    trader_name = self.current_config['target_traders'][index]
                    current_score = self.current_config['trader_scores'].get(trader_name, 0)
                    
                    print(f"\n更新交易员: {trader_name}")
                    print(f"当前评分: {current_score}")
                    
                    # 输入新评分
                    while True:
                        try:
                            new_score_input = input("请输入新评分 (0-100): ").strip()
                            new_score = float(new_score_input)
                            if 0 <= new_score <= 100:
                                break
                            else:
                                print("❌ 评分必须在 0-100 之间")
                        except ValueError:
                            print("❌ 请输入有效的数字")
                    
                    # 更新评分
                    self.current_config['trader_scores'][trader_name] = new_score
                    
                    print(f"✅ 已更新 {trader_name} 的评分: {current_score} → {new_score}")
                    
                    # 保存配置
                    if self.save_config(self.current_config):
                        print("✅ 配置已保存")
                    break
                else:
                    print("❌ 无效的序号")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
    
    async def run_trader_discovery(self):
        """运行交易员发现"""
        print("\n🔍 启动交易员发现...")
        
        try:
            # 导入动态筛选器
            from dynamic_trader_selector import DynamicTraderSelector
            
            selector = DynamicTraderSelector()
            
            print("🚀 选择扫描模式:")
            print("1. 快速发现 (5轮)")
            print("2. 标准发现 (10轮)")
            print("3. 深度发现 (20轮)")
            
            while True:
                choice = input("\n请选择 (1-3): ").strip()
                if choice in ['1', '2', '3']:
                    break
                print("❌ 请输入有效选项 (1-3)")
            
            scan_rounds = {'1': 5, '2': 10, '3': 20}[choice]
            
            print(f"✅ 开始 {scan_rounds} 轮交易员发现...")
            
            # 运行发现
            await selector.run_trader_selection(scan_rounds)
            
            # 检查是否生成了新配置
            if self.config_file.exists():
                print("✅ 发现完成！新配置已生成")
                self.current_config = self.load_current_config()
                self.display_current_config()
            else:
                print("⚠️  未生成新配置，可能没有发现符合条件的交易员")
            
        except ImportError:
            print("❌ 无法导入动态筛选器，请检查依赖")
        except Exception as e:
            print(f"❌ 交易员发现失败: {e}")
    
    def export_config(self):
        """导出配置"""
        try:
            export_file = f"trader_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 配置已导出到: {export_file}")
            
        except Exception as e:
            print(f"❌ 导出配置失败: {e}")
    
    def import_config(self):
        """导入配置"""
        import_file = input("请输入配置文件路径: ").strip()
        
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置格式
            required_keys = ['target_traders', 'trader_scores']
            if all(key in imported_config for key in required_keys):
                self.current_config = imported_config
                
                if self.save_config(self.current_config):
                    print("✅ 配置导入成功")
                    self.display_current_config()
                else:
                    print("❌ 保存导入的配置失败")
            else:
                print("❌ 配置文件格式无效")
                
        except FileNotFoundError:
            print("❌ 配置文件不存在")
        except json.JSONDecodeError:
            print("❌ 配置文件格式错误")
        except Exception as e:
            print(f"❌ 导入配置失败: {e}")
    
    def run_interactive_menu(self):
        """运行交互式菜单"""
        while True:
            print(f"\n{'='*60}")
            print(f"🎯 交易员管理工具")
            print(f"{'='*60}")
            print("1. 📊 查看当前配置")
            print("2. ➕ 手动添加交易员")
            print("3. ➖ 移除交易员")
            print("4. 📊 更新交易员评分")
            print("5. 🔍 自动发现交易员")
            print("6. 📤 导出配置")
            print("7. 📥 导入配置")
            print("8. ❌ 退出")
            
            choice = input("\n请选择操作 (1-8): ").strip()
            
            if choice == '1':
                self.display_current_config()
            elif choice == '2':
                self.add_trader_manual()
            elif choice == '3':
                self.remove_trader()
            elif choice == '4':
                self.update_trader_score()
            elif choice == '5':
                asyncio.run(self.run_trader_discovery())
            elif choice == '6':
                self.export_config()
            elif choice == '7':
                self.import_config()
            elif choice == '8':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重试")


def main():
    """主函数"""
    print("🎯 交易员管理工具")
    print("="*50)
    print("📊 管理监控系统的交易员配置")
    print("🔍 发现和评估外汇交易员")
    print("⚙️  独立于主监控系统运行")
    print("="*50)
    
    manager = TraderManager()
    manager.run_interactive_menu()


if __name__ == "__main__":
    main()
