#!/usr/bin/env python3
"""
清理测试数据脚本 - 删除所有模拟/测试数据，只保留真实信号
"""

import os
from pathlib import Path


def clear_test_data():
    """清理所有测试数据"""
    print("🧹 清理测试数据...")
    
    # 要删除的测试文件
    test_files = [
        "logs/validated_signals.json",
        "MQL4/Files/test_account_info.txt",
        "MQL4/Files/validated_signals.csv",
        "MQL4/Files/trading_signals.csv",
        "MQL4/Files/trading_status.txt"
    ]
    
    deleted_count = 0
    
    for file_path in test_files:
        path = Path(file_path)
        if path.exists():
            try:
                path.unlink()
                print(f"✅ 已删除: {file_path}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ 删除失败 {file_path}: {e}")
        else:
            print(f"⚪ 不存在: {file_path}")
    
    print(f"\n🎯 清理完成！删除了 {deleted_count} 个测试文件")
    
    # 检查真实信号文件
    real_files = [
        "logs/real_signals.json",
        "logs/premium_signals.json",
        "MQL4/Files/real_signals.csv"
    ]
    
    print("\n📊 真实信号文件状态:")
    for file_path in real_files:
        path = Path(file_path)
        if path.exists():
            size = path.stat().st_size
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} - 不存在")
    
    print("\n⚠️  重要提醒:")
    print("- 所有测试数据已清理")
    print("- 现在只会显示真实信号")
    print("- 请运行 python real_signal_capture.py 获取真实信号")
    print("- 或运行 python start_premium.py 配置付费信号源")


if __name__ == "__main__":
    clear_test_data()
