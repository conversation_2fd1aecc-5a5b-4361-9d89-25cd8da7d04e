#!/usr/bin/env python3
"""
无API版本启动脚本 - 完全免费的MT4跟单系统
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def check_requirements():
    """检查基础要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['requests', 'beautifulsoup4', 'flask']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n📦 正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + missing_packages, 
                          check=True, capture_output=True)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {e}")
            return False
    
    return True


def setup_system():
    """设置系统"""
    print("\n🔧 设置无API版本系统...")
    
    # 运行设置脚本
    if Path("no_api_setup.py").exists():
        try:
            subprocess.run([sys.executable, "no_api_setup.py"], check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 设置失败: {e}")
            return False
    else:
        print("❌ 设置脚本不存在")
        return False


def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🚀 MT4智能跟单系统 (无API版本)")
    print("="*50)
    print("1. 🕷️  启动信号抓取器")
    print("2. 🌐 启动监控面板")
    print("3. 🔄 同时启动抓取器和面板")
    print("4. 📊 查看MT4文件状态")
    print("5. 🔧 重新设置系统")
    print("6. 📋 查看使用说明")
    print("7. 🧪 测试系统组件")
    print("0. 🚪 退出")
    print("="*50)


def start_signal_scraper():
    """启动信号抓取器"""
    print("🕷️ 启动信号抓取器...")
    
    if not Path("free_signal_scraper.py").exists():
        print("❌ 信号抓取器文件不存在，请先运行设置")
        return
    
    try:
        print("📡 开始抓取免费信号...")
        print("💡 提示: 按 Ctrl+C 停止抓取")
        subprocess.run([sys.executable, "free_signal_scraper.py"])
    except KeyboardInterrupt:
        print("\n⏹️  信号抓取器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def start_dashboard():
    """启动监控面板"""
    print("🌐 启动监控面板...")
    
    if not Path("simple_dashboard.py").exists():
        print("❌ 监控面板文件不存在，请先运行设置")
        return
    
    try:
        print("🖥️  启动Web监控面板...")
        print("🌐 访问地址: http://localhost:8080")
        print("💡 提示: 按 Ctrl+C 停止面板")
        subprocess.run([sys.executable, "simple_dashboard.py"])
    except KeyboardInterrupt:
        print("\n⏹️  监控面板已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def start_both():
    """同时启动抓取器和面板"""
    print("🔄 同时启动信号抓取器和监控面板...")
    
    import threading
    import time
    
    def run_scraper():
        try:
            subprocess.run([sys.executable, "free_signal_scraper.py"])
        except:
            pass
    
    def run_dashboard():
        time.sleep(2)  # 延迟2秒启动
        try:
            subprocess.run([sys.executable, "simple_dashboard.py"])
        except:
            pass
    
    # 启动线程
    scraper_thread = threading.Thread(target=run_scraper)
    dashboard_thread = threading.Thread(target=run_dashboard)
    
    scraper_thread.daemon = True
    dashboard_thread.daemon = True
    
    scraper_thread.start()
    dashboard_thread.start()
    
    print("✅ 两个组件都已启动")
    print("🌐 监控面板: http://localhost:8080")
    print("💡 按 Ctrl+C 停止所有服务")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n⏹️  所有服务已停止")


def check_mt4_files():
    """检查MT4文件状态"""
    print("📊 检查MT4文件状态...")
    
    files_to_check = [
        "MQL4/Experts/AutoCopyTrader.mq4",
        "MQL4/Files/trading_signals.csv",
        "MQL4/Files/trading_status.txt",
        "MQL4/Files/account_info.txt"
    ]
    
    print("\n文件状态:")
    print("-" * 40)
    
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            size = path.stat().st_size
            mtime = path.stat().st_mtime
            import datetime
            mod_time = datetime.datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
            print(f"✅ {file_path}")
            print(f"   大小: {size} 字节")
            print(f"   修改时间: {mod_time}")
        else:
            print(f"❌ {file_path} - 不存在")
        print()
    
    # 检查信号文件内容
    signals_file = Path("MQL4/Files/trading_signals.csv")
    if signals_file.exists():
        try:
            with open(signals_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"📈 信号文件包含 {len(lines)} 条记录")
            if lines:
                print("最近的信号:")
                for line in lines[-3:]:
                    print(f"   {line.strip()}")
        except Exception as e:
            print(f"❌ 读取信号文件失败: {e}")


def show_instructions():
    """显示使用说明"""
    print("\n📋 无API版本使用说明")
    print("="*50)
    
    print("\n🎯 系统特点:")
    print("✅ 完全免费，无需任何付费API")
    print("✅ 从公开Telegram频道获取信号")
    print("✅ 直接与MT4通信，无中间商")
    print("✅ 简单易用，适合新手")
    
    print("\n📋 安装步骤:")
    print("1. 确保MT4已安装并正常运行")
    print("2. 运行本脚本完成系统设置")
    print("3. 将生成的EA文件复制到MT4")
    print("4. 在MT4中启用EA和自动交易")
    print("5. 启动信号抓取器和监控面板")
    
    print("\n🔧 MT4设置:")
    print("1. 打开MT4，按 Ctrl+Shift+N 打开导航器")
    print("2. 右键点击 'Expert Advisors' -> 刷新")
    print("3. 找到 'AutoCopyTrader' EA")
    print("4. 拖拽EA到任意图表上")
    print("5. 在设置中启用 '允许自动交易'")
    print("6. 点击 '确定' 启动EA")
    
    print("\n📊 监控说明:")
    print("- 信号抓取器: 从免费渠道获取交易信号")
    print("- 监控面板: 实时查看账户状态和信号")
    print("- MT4 EA: 自动执行交易信号")
    
    print("\n⚠️  重要提醒:")
    print("- 建议先在模拟账户测试")
    print("- 定期检查系统运行状态")
    print("- 根据实际情况调整参数")
    print("- 交易有风险，投资需谨慎")


def test_components():
    """测试系统组件"""
    print("🧪 测试系统组件...")
    
    tests = [
        ("配置文件", lambda: Path("config/no_api_config.json").exists()),
        ("MT4 EA文件", lambda: Path("MQL4/Experts/AutoCopyTrader.mq4").exists()),
        ("信号抓取器", lambda: Path("free_signal_scraper.py").exists()),
        ("监控面板", lambda: Path("simple_dashboard.py").exists()),
        ("MQL4目录", lambda: Path("MQL4/Files").exists()),
    ]
    
    print("\n测试结果:")
    print("-" * 30)
    
    passed = 0
    for test_name, test_func in tests:
        try:
            result = test_func()
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:<15} {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"{test_name:<15} ❌ 错误: {e}")
    
    print(f"\n总计: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有组件测试通过！")
    else:
        print("⚠️  部分组件测试失败，请检查设置")


def main():
    """主函数"""
    print("🚀 MT4智能跟单系统 (无API版本)")
    
    # 检查要求
    if not check_requirements():
        print("❌ 系统要求检查失败")
        return
    
    # 如果是首次运行，进行设置
    if not Path("config/no_api_config.json").exists():
        print("\n🔧 检测到首次运行，正在设置系统...")
        if not setup_system():
            print("❌ 系统设置失败")
            return
        print("✅ 系统设置完成")
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-7): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                start_signal_scraper()
            elif choice == '2':
                start_dashboard()
            elif choice == '3':
                start_both()
            elif choice == '4':
                check_mt4_files()
            elif choice == '5':
                setup_system()
            elif choice == '6':
                show_instructions()
            elif choice == '7':
                test_components()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        if choice != '0':
            input("\n按回车键继续...")


if __name__ == "__main__":
    main()
