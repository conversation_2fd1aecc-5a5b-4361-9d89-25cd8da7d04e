#!/usr/bin/env python3
"""
MT4智能跟单系统 - 主程序
自动跟随网上厉害交易师的完整解决方案
"""

import asyncio
import json
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path

from src.core.trading_engine import TradingEngine
from src.core.signal_manager import SignalManager
from src.core.risk_manager import RiskManager
from src.core.portfolio_manager import PortfolioManager
from src.utils.logger import setup_logger
from src.utils.config_loader import ConfigLoader
from src.database.db_manager import DatabaseManager


class MT4AutoCopier:
    """MT4自动跟单系统主类"""
    
    def __init__(self, config_path: str = "config/config.json"):
        """初始化系统"""
        self.config_path = config_path
        self.config = None
        self.logger = None
        self.running = False
        
        # 核心组件
        self.db_manager = None
        self.signal_manager = None
        self.risk_manager = None
        self.portfolio_manager = None
        self.trading_engine = None
        
    async def initialize(self):
        """初始化所有组件"""
        try:
            # 加载配置
            self.config = ConfigLoader.load(self.config_path)
            
            # 设置日志
            self.logger = setup_logger(self.config['monitoring']['logging'])
            self.logger.info("🚀 MT4智能跟单系统启动中...")
            
            # 初始化数据库
            self.db_manager = DatabaseManager(self.config['database'])
            await self.db_manager.initialize()
            
            # 初始化风险管理器
            self.risk_manager = RiskManager(self.config['risk_management'])
            
            # 初始化投资组合管理器
            self.portfolio_manager = PortfolioManager(
                self.config, self.db_manager, self.risk_manager
            )
            
            # 初始化信号管理器
            self.signal_manager = SignalManager(
                self.config['signal_sources'],
                self.config['signal_filtering'],
                self.db_manager
            )
            
            # 初始化交易引擎
            self.trading_engine = TradingEngine(
                self.config['api_keys']['metaapi'],
                self.config['trading_settings'],
                self.db_manager,
                self.risk_manager
            )
            
            # 连接MT4
            await self.trading_engine.connect()
            
            self.logger.info("✅ 所有组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 初始化失败: {e}")
            raise
    
    async def start(self):
        """启动系统"""
        try:
            self.running = True
            self.logger.info("🎯 开始监控交易信号...")
            
            # 启动信号监控
            signal_task = asyncio.create_task(self.signal_monitoring_loop())
            
            # 启动风险监控
            risk_task = asyncio.create_task(self.risk_monitoring_loop())
            
            # 启动投资组合优化
            portfolio_task = asyncio.create_task(self.portfolio_optimization_loop())
            
            # 启动状态监控
            status_task = asyncio.create_task(self.status_monitoring_loop())
            
            # 等待所有任务
            await asyncio.gather(
                signal_task,
                risk_task,
                portfolio_task,
                status_task,
                return_exceptions=True
            )
            
        except Exception as e:
            self.logger.error(f"❌ 系统运行错误: {e}")
            raise
    
    async def signal_monitoring_loop(self):
        """信号监控循环"""
        while self.running:
            try:
                # 获取新信号
                signals = await self.signal_manager.get_new_signals()
                
                for signal in signals:
                    await self.process_signal(signal)
                
                await asyncio.sleep(1)  # 1秒检查一次
                
            except Exception as e:
                self.logger.error(f"信号监控错误: {e}")
                await asyncio.sleep(5)
    
    async def process_signal(self, signal):
        """处理交易信号"""
        try:
            self.logger.info(f"📊 收到信号: {signal['symbol']} {signal['action']}")
            
            # 风险检查
            if not await self.risk_manager.check_signal_risk(signal):
                self.logger.warning(f"⚠️ 信号风险检查未通过: {signal}")
                return
            
            # 计算仓位大小
            position_size = await self.portfolio_manager.calculate_position_size(signal)
            
            if position_size <= 0:
                self.logger.warning(f"⚠️ 计算仓位大小为0: {signal}")
                return
            
            # 执行交易
            result = await self.trading_engine.execute_signal(signal, position_size)
            
            if result['success']:
                self.logger.info(f"✅ 交易执行成功: {result}")
                
                # 记录交易
                await self.db_manager.save_trade(result)
                
                # 发送通知
                await self.send_notification(f"交易执行: {signal['symbol']} {signal['action']}")
            else:
                self.logger.error(f"❌ 交易执行失败: {result}")
                
        except Exception as e:
            self.logger.error(f"处理信号错误: {e}")
    
    async def risk_monitoring_loop(self):
        """风险监控循环"""
        while self.running:
            try:
                # 检查账户风险
                risk_status = await self.risk_manager.check_account_risk()
                
                if risk_status['emergency_stop']:
                    self.logger.critical("🚨 触发紧急止损！")
                    await self.emergency_stop()
                    
                elif risk_status['warning']:
                    self.logger.warning(f"⚠️ 风险警告: {risk_status['message']}")
                    await self.send_notification(f"风险警告: {risk_status['message']}")
                
                await asyncio.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                self.logger.error(f"风险监控错误: {e}")
                await asyncio.sleep(60)
    
    async def portfolio_optimization_loop(self):
        """投资组合优化循环"""
        while self.running:
            try:
                # 每小时优化一次投资组合
                await self.portfolio_manager.optimize_portfolio()
                
                await asyncio.sleep(3600)  # 1小时
                
            except Exception as e:
                self.logger.error(f"投资组合优化错误: {e}")
                await asyncio.sleep(3600)
    
    async def status_monitoring_loop(self):
        """状态监控循环"""
        while self.running:
            try:
                # 记录系统状态
                status = await self.get_system_status()
                await self.db_manager.save_system_status(status)
                
                # 每10分钟记录一次
                await asyncio.sleep(600)
                
            except Exception as e:
                self.logger.error(f"状态监控错误: {e}")
                await asyncio.sleep(600)
    
    async def emergency_stop(self):
        """紧急停止所有交易"""
        try:
            self.logger.critical("🚨 执行紧急停止...")
            
            # 关闭所有持仓
            await self.trading_engine.close_all_positions()
            
            # 停止新信号处理
            self.running = False
            
            # 发送紧急通知
            await self.send_notification("🚨 系统紧急停止！所有持仓已关闭。")
            
        except Exception as e:
            self.logger.error(f"紧急停止错误: {e}")
    
    async def send_notification(self, message: str):
        """发送通知"""
        try:
            # 这里可以集成Telegram、邮件等通知方式
            self.logger.info(f"📢 通知: {message}")
            
        except Exception as e:
            self.logger.error(f"发送通知错误: {e}")
    
    async def get_system_status(self):
        """获取系统状态"""
        return {
            'timestamp': datetime.now().isoformat(),
            'running': self.running,
            'account_balance': await self.trading_engine.get_account_balance(),
            'open_positions': await self.trading_engine.get_open_positions_count(),
            'daily_pnl': await self.trading_engine.get_daily_pnl(),
            'signals_processed': await self.signal_manager.get_signals_count_today()
        }
    
    async def shutdown(self):
        """优雅关闭系统"""
        try:
            self.logger.info("🔄 系统关闭中...")
            self.running = False
            
            # 关闭所有连接
            if self.trading_engine:
                await self.trading_engine.disconnect()
            
            if self.db_manager:
                await self.db_manager.close()
            
            self.logger.info("✅ 系统已安全关闭")
            
        except Exception as e:
            self.logger.error(f"关闭系统错误: {e}")


async def main():
    """主函数"""
    copier = MT4AutoCopier()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print("\n收到退出信号，正在安全关闭系统...")
        asyncio.create_task(copier.shutdown())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化并启动系统
        await copier.initialize()
        await copier.start()
        
    except KeyboardInterrupt:
        print("\n用户中断，正在关闭系统...")
    except Exception as e:
        print(f"系统错误: {e}")
    finally:
        await copier.shutdown()


if __name__ == "__main__":
    # 创建必要的目录
    Path("logs").mkdir(exist_ok=True)
    Path("data").mkdir(exist_ok=True)
    Path("config").mkdir(exist_ok=True)
    
    # 运行主程序
    asyncio.run(main())
