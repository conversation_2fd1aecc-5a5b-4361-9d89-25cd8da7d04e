#!/usr/bin/env python3
"""
ForexFactory数据分析器 - 分析真实交易员数据并生成跟单信号
"""

import json
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict
import re


class FFDataAnalyzer:
    """ForexFactory数据分析器"""
    
    def __init__(self):
        self.raw_data = []
        self.traders_profiles = {}
        self.trading_signals = []
    
    def load_data(self):
        """加载数据"""
        data_file = Path("data/ff_stealth_data.json")
        
        if not data_file.exists():
            print("❌ 未找到数据文件，请先运行 ff_stealth_hunter.py")
            return False
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                self.raw_data = json.load(f)
            
            print(f"✅ 已加载 {len(self.raw_data)} 条原始数据")
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def analyze_data(self):
        """分析数据"""
        print("🔍 分析ForexFactory交易数据...")
        
        traders_data = defaultdict(list)
        
        # 从表格内容中提取交易信息
        for item in self.raw_data:
            if item.get('type') == 'table_content':
                content = item.get('content', [])
                if len(content) >= 4:
                    # 跳过表头
                    if content[0] == 'Trade' or 'Saving' in str(content[0]):
                        continue
                    
                    try:
                        # 解析交易信息
                        trade_info = self.parse_trade_row(content)
                        if trade_info:
                            trader_name = trade_info['trader']
                            traders_data[trader_name].append(trade_info)
                    except Exception as e:
                        continue
        
        # 转换为交易员档案
        for trader_name, trades in traders_data.items():
            performance = self.calculate_performance(trades)
            
            self.traders_profiles[trader_name] = {
                'name': trader_name,
                'trades': trades,
                'total_trades': len(trades),
                'symbols_traded': list(set([t.get('symbol') for t in trades if t.get('symbol')])),
                'performance': performance
            }
        
        print(f"✅ 分析完成，发现 {len(self.traders_profiles)} 个交易员")
        
        # 显示交易员概览
        if self.traders_profiles:
            print(f"\n🏆 发现的交易员:")
            for i, (name, profile) in enumerate(self.traders_profiles.items(), 1):
                symbols = ', '.join(profile['symbols_traded'][:3])
                perf = profile['performance']
                print(f"{i}. {name} - {profile['total_trades']}笔交易")
                print(f"   品种: {symbols}")
                print(f"   胜率: {perf.get('win_rate', 0):.1f}% | 平均收益: {perf.get('avg_profit', 0):.2f}%")
                print()
    
    def parse_trade_row(self, content):
        """解析单行交易数据"""
        try:
            trade_text = str(content[0])  # 交易信息
            trader_name = str(content[1])  # 交易员名称
            return_text = str(content[2])  # 收益信息
            pips_text = str(content[3])   # 点数信息
            
            # 解析交易品种
            symbol_patterns = [
                r'(BTC/USD)', r'(EUR/USD)', r'(Xauusdc)', r'(GBP/USD)', r'(USD/JPY)'
            ]
            
            symbol = None
            for pattern in symbol_patterns:
                match = re.search(pattern, trade_text, re.IGNORECASE)
                if match:
                    symbol = match.group(1).upper()
                    if symbol == 'XAUUSDC':
                        symbol = 'XAUUSD'
                    break
            
            # 解析交易方向
            action = None
            if re.search(r'\b(BUY|Long)\b', trade_text, re.IGNORECASE):
                action = 'BUY'
            elif re.search(r'\b(SELL|Short)\b', trade_text, re.IGNORECASE):
                action = 'SELL'
            
            # 解析交易状态
            status = 'unknown'
            if 'Opened' in trade_text:
                status = 'open'
            elif 'Closed' in trade_text:
                status = 'closed'
            elif 'Scaled Out' in trade_text:
                status = 'partial_close'
            
            # 解析时间
            time_match = re.search(r'(\d+)\s*min\s*ago', trade_text)
            minutes_ago = int(time_match.group(1)) if time_match else 0
            
            # 解析收益
            profit_pct = self.parse_return_value(return_text)
            pips_value = self.parse_pips_value(pips_text)
            
            if symbol and action and trader_name:
                return {
                    'symbol': symbol,
                    'action': action,
                    'trader': trader_name,
                    'status': status,
                    'minutes_ago': minutes_ago,
                    'timestamp': datetime.now() - timedelta(minutes=minutes_ago),
                    'profit_pct': profit_pct,
                    'pips': pips_value,
                    'raw_text': trade_text,
                    'is_profitable': profit_pct > 0 if profit_pct is not None else None
                }
            
        except Exception as e:
            pass
        
        return None
    
    def parse_return_value(self, return_text):
        """解析收益百分比"""
        try:
            # 查找百分比数字
            pct_matches = re.findall(r'([+-]?\d+\.?\d*)%', return_text)
            if pct_matches:
                for pct_str in reversed(pct_matches):
                    pct_value = float(pct_str)
                    if pct_value != 0:
                        return pct_value
                return 0.0
        except:
            pass
        return None
    
    def parse_pips_value(self, pips_text):
        """解析点数"""
        try:
            pip_matches = re.findall(r'([+-]?\d+)', pips_text)
            if pip_matches:
                for pip_str in reversed(pip_matches):
                    pip_value = int(pip_str)
                    if pip_value != 0:
                        return pip_value
                return 0
        except:
            pass
        return None
    
    def calculate_performance(self, trades):
        """计算交易员表现"""
        if not trades:
            return {}
        
        total_trades = len(trades)
        closed_trades = [t for t in trades if t['status'] == 'closed']
        profitable_trades = [t for t in closed_trades if t.get('is_profitable') == True]
        
        # 计算胜率
        win_rate = (len(profitable_trades) / len(closed_trades) * 100) if closed_trades else 0
        
        # 计算平均收益
        profits = [t['profit_pct'] for t in closed_trades if t.get('profit_pct') is not None]
        avg_profit = sum(profits) / len(profits) if profits else 0
        
        # 计算平均点数
        pips_list = [t['pips'] for t in closed_trades if t.get('pips') is not None]
        avg_pips = sum(pips_list) / len(pips_list) if pips_list else 0
        
        return {
            'total_trades': total_trades,
            'closed_trades': len(closed_trades),
            'win_rate': round(win_rate, 1),
            'avg_profit': round(avg_profit, 2),
            'avg_pips': round(avg_pips, 1),
            'profitable_trades': len(profitable_trades),
            'last_trade_minutes_ago': min([t['minutes_ago'] for t in trades], default=0)
        }
    
    def identify_top_traders(self):
        """识别顶级交易员"""
        print("🏆 识别顶级交易员...")
        
        performers = []
        
        for name, profile in self.traders_profiles.items():
            perf = profile['performance']
            
            # 计算综合评分
            score = 0
            
            # 胜率权重 (40%)
            if perf['win_rate'] > 0:
                score += (perf['win_rate'] / 100) * 40
            
            # 平均收益权重 (30%)
            if perf['avg_profit'] > 0:
                score += min(perf['avg_profit'] * 10, 30)
            
            # 交易活跃度权重 (20%)
            if perf['total_trades'] > 0:
                score += min(perf['total_trades'] * 5, 20)
            
            # 最近活跃度权重 (10%)
            if perf['last_trade_minutes_ago'] < 10:
                score += 10
            elif perf['last_trade_minutes_ago'] < 30:
                score += 5
            
            performers.append({
                'name': name,
                'score': round(score, 1),
                'profile': profile
            })
        
        # 按评分排序
        performers.sort(key=lambda x: x['score'], reverse=True)
        top_performers = performers[:5]  # 前5名
        
        if top_performers:
            print(f"\n🏆 顶级交易员排行榜:")
            print("-" * 80)
            print(f"{'排名':<4} {'交易员':<15} {'评分':<6} {'胜率':<8} {'平均收益':<10} {'交易数':<8} {'最近活动'}")
            print("-" * 80)
            
            for i, performer in enumerate(top_performers, 1):
                name = performer['name']
                score = performer['score']
                perf = performer['profile']['performance']
                
                print(f"{i:<4} {name[:14]:<15} {score:<6} {perf['win_rate']:<7}% {perf['avg_profit']:<9}% {perf['total_trades']:<8} {perf['last_trade_minutes_ago']}分钟前")
        
        return top_performers
    
    def generate_follow_signals(self, top_performers):
        """生成跟单信号"""
        print("\n🎯 生成跟单信号...")
        
        signals = []
        
        # 基于顶级交易员的开仓交易生成信号
        for performer in top_performers[:3]:  # 前3名
            trader_name = performer['name']
            profile = performer['profile']
            
            # 查找最近的开仓交易
            open_trades = [t for t in profile['trades'] 
                          if t['status'] == 'open' and t['minutes_ago'] < 15]
            
            for trade in open_trades:
                if trade.get('symbol') and trade.get('action'):
                    # 计算信号置信度
                    confidence = min(0.9, max(0.4, performer['score'] / 100))
                    
                    signal = {
                        'id': f"ff_follow_{trader_name}_{trade['symbol']}_{trade['action']}_{int(datetime.now().timestamp())}",
                        'timestamp': datetime.now().isoformat(),
                        'source': f'FF_TopTrader_{trader_name}',
                        'symbol': trade['symbol'],
                        'action': trade['action'],
                        'confidence': confidence,
                        'signal_type': 'follow_trade',
                        'trader_score': performer['score'],
                        'trader_win_rate': profile['performance']['win_rate'],
                        'minutes_since_entry': trade['minutes_ago'],
                        'is_real': True,
                        'platform': 'ForexFactory',
                        'follow_reason': f"跟随评分{performer['score']}的顶级交易员{trader_name}"
                    }
                    
                    signals.append(signal)
        
        if signals:
            print(f"✅ 生成了 {len(signals)} 个跟单信号")
            
            print(f"\n🎯 最新跟单信号:")
            for i, signal in enumerate(signals, 1):
                print(f"{i}. {signal['symbol']} {signal['action']} - 置信度: {signal['confidence']*100:.0f}%")
                print(f"   跟随: {signal['source']} (评分: {signal['trader_score']})")
                print(f"   {signal['minutes_since_entry']}分钟前开仓")
        else:
            print("⚠️  当前没有合适的跟单信号")
        
        return signals
    
    def save_results(self, top_performers, signals):
        """保存结果"""
        try:
            Path("data").mkdir(exist_ok=True)
            Path("logs").mkdir(exist_ok=True)
            
            # 保存分析结果
            analysis_data = {
                'analysis_time': datetime.now().isoformat(),
                'traders_profiles': self.traders_profiles,
                'top_performers': top_performers,
                'trading_signals': signals
            }
            
            with open('data/ff_analysis_results.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)
            
            # 保存跟单信号
            if signals:
                with open('logs/ff_follow_signals.json', 'w', encoding='utf-8') as f:
                    for signal in signals:
                        f.write(json.dumps(signal, ensure_ascii=False, default=str) + '\n')
            
            print(f"\n✅ 结果已保存:")
            print(f"   分析结果: data/ff_analysis_results.json")
            if signals:
                print(f"   跟单信号: logs/ff_follow_signals.json")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🔍 ForexFactory交易员数据分析")
        print("="*50)
        
        # 加载数据
        if not self.load_data():
            return False
        
        # 分析数据
        self.analyze_data()
        
        if not self.traders_profiles:
            print("❌ 未找到有效的交易员数据")
            return False
        
        # 识别顶级交易员
        top_performers = self.identify_top_traders()
        
        # 生成跟单信号
        signals = self.generate_follow_signals(top_performers)
        
        # 保存结果
        self.save_results(top_performers, signals)
        
        print(f"\n🎉 分析完成！")
        print(f"📊 分析了 {len(self.traders_profiles)} 个交易员")
        print(f"🏆 识别了 {len(top_performers)} 个顶级交易员")
        print(f"🎯 生成了 {len(signals)} 个跟单信号")
        
        return True


def main():
    """主函数"""
    analyzer = FFDataAnalyzer()
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
