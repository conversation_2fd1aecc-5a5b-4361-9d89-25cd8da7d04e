<html class="no-js" lang="en"><head> <base href="https://www.forexfactory.com"> <meta charset="windows-1252"> <meta property="og:description" content="View, analyze, and follow live forex trades from around the world, or attempt to compete for a spot on the Top-10 Leaderboard."> <meta property="og:title" content="Trades"> <meta property="og:type" content="website"> <meta property="og:url" content="https://www.forexfactory.com/trades"> <meta property="og:image" content="https://resources.faireconomy.media/images/logos/bookmark_ff_400.png"> <meta property="og:site_name" content="Forex Factory"> <meta name="keywords" content="forex market cot, commitment of traders, trade following"> <meta name="description" content="View, analyze, and follow live forex trades from around the world, or attempt to compete for a spot on the Top-10 Leaderboard."> <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"> <meta name="csrf-token" content="b1476b4155b899debba83b5d37555e44"> <link rel="preconnect" href="https://resources.faireconomy.media"> <link rel="preconnect" href="https://assets.faireconomy.media"> <link rel="preconnect" href="https://www.googletagmanager.com"> <link rel="preconnect" href="https://www.google-analytics.com"> <link rel="preload" as="font" href="https://resources.faireconomy.media/fonts/ubuntu.woff" type="font/woff" crossorigin="anonymous">  <link rel="shortcut icon" href="https://resources.faireconomy.media/icon-ff.ico" type="image/x-icon"> <link rel="icon" href="https://resources.faireconomy.media/icon-ff.ico" type="image/ico"> <link rel="apple-touch-icon" sizes="400x400" href="https://resources.faireconomy.media/images/logos/bookmark_ff_400.png"> <link rel="apple-touch-icon" sizes="192x192" href="https://resources.faireconomy.media/images/logos/bookmark_ff_192.png">  <link rel="manifest" href="/manifests/manifest-ff.json">  <script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=UA-3311429-1&amp;cx=c&amp;gtm=45He57n0h1v898621507za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-QFGG4THJR2&amp;cx=c&amp;gtm=45He57n0h1v898621507za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211"></script><script type="text/javascript" async="" src="https://static.hotjar.com/c/hotjar-3279922.js?sv=7"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-PC5QDXT"></script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-PC5QDXT');</script> <script>
try {
    window.hj = window.hj||function(){(hj.q=hj.q||[]).push(arguments);};
    window.hj('identify', null, {
        'Usergroup': 'Guest',
        'MIRS': '',
        'Joined': '2025-07-28T01:20:47-04:00',
        'HTTP Status': 200,
        'Client Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
        'User Timezone': 'Asia/Hong_Kong',
        'Country Code': 'HK',
    });
} catch { }
</script>  <link type="text/css" rel="stylesheet" href="https://resources.faireconomy.media/css/ff.css?_v=1752602792"> <script type="text/javascript">
// Global FF settings object
window.FF = {
country_code: 'HK',
    content_group: 'Trades',
    timezone: '8'.replace('+', ''),
    timezone_name: 'Asia/Hong_Kong',
    display_time: '1:20pm',
    internal_referrer_uri: '',
    weekstart: '1',
    format : '12',
    userid: 0,
    cookie_prefix: 'ff',
mds_socket: {
domain: 'mds-wss.forexfactory.com',
port: 2096
},
calendar_feed_endpoint: 'wss://calendar-feed.forexfactory.com:2087',
    npd_api_endpoint: 'https://npd-api.forexfactory.com/api.php',
    mds_api_endpoint: 'https://mds-api.forexfactory.com',
    bds_api_endpoint: 'https://bds-api.forexfactory.com',
    explorer_api_endpoint: 'https://explorer-api.forexfactory.com/api.php/',
    mds_api_bearer: '',
    bds_api_bearer: '',
    is_android: false,
    is_team: false,
    perms: {
        can_control_panel: false,
        can_edit_microsite: false,
        can_manage_cbase: false,
        can_coordinate_calendar: false    },
    headerOffset: NaN,
    anchorOffset: NaN,
    device: {
        code: '',
        name: '',
    },
    max_emojis: 100,
    assets: 'https://assets.faireconomy.media/nfs',
    editorHeight: 252,
    mvEditorHeight: 166,
    ffnews_prevstorylimit: 625,
    ffnews_titlelimit: 150,
    news_stories_maxw: 110,
    liveDataUrl: 'faireconomy.media/livedata/',
    staticUrl: 'faireconomy.media',
    _sites: [{"id":1,"master":true,"code":"ff","url":"www.forexfactory.com","name":"Forex Factory","slug":"forex_factory","email_domain":"forexfactory.com","ga_tracking_id":"UA-3311429-1","gtm_container_id":"GTM-PC5QDXT","ga_4_tracking_id":"G-QFGG4THJR2"},{"id":4,"master":false,"code":"cc","url":"www.cryptocraft.com","name":"Crypto Craft","slug":"crypto_craft","email_domain":"cryptocraft.com","ga_tracking_id":"UA-3311429-6","gtm_container_id":"GTM-PSBH459","ga_4_tracking_id":"G-1QQ0PL15V6"},{"id":3,"master":false,"code":"ee","url":"www.energyexch.com","name":"Energy EXCH","slug":"energy_exch","email_domain":"energyexch.com","ga_tracking_id":"UA-3311429-5","gtm_container_id":"GTM-KC56BB6","ga_4_tracking_id":"G-VWXHHGYNQC"},{"id":2,"master":false,"code":"mm","url":"www.metalsmine.com","name":"Metals Mine","slug":"metals_mine","email_domain":"metalsmine.com","ga_tracking_id":"UA-3311429-7","gtm_container_id":"GTM-T7DCNJC","ga_4_tracking_id":"G-SJR6Y13356"}],
    sites: [],
    mvBreakpoint: 898,
    viewingUserProfile: 0,
    _colors: [{"variable":"$green","hex":"#39ad39"},{"variable":"$lightgreen","hex":"#87d687"},{"variable":"$hovergreen","hex":"#e3f1e5"},{"variable":"$blue2","hex":"#37548a"},{"variable":"$blue1","hex":"#283343"},{"variable":"$memnav","hex":"#314366"},{"variable":"$blue3","hex":"#4766a0"},{"variable":"$blue4","hex":"#5170aa"},{"variable":"$blue5","hex":"#758ab4"},{"variable":"$blue5alt","hex":"#8397bf"},{"variable":"$gutter2","hex":"#bac1c5"},{"variable":"$gutter1","hex":"#ccd3d8"},{"variable":"$blue6","hex":"#bfc8db"},{"variable":"$blue7","hex":"#d5d9e2"},{"variable":"$lightbluetext","hex":"#dde4ec"},{"variable":"$blue8","hex":"#e6e8ed"},{"variable":"$blue9","hex":"#e8ecf2"},{"variable":"$blue10","hex":"#f4f6f9"},{"variable":"$linkcolor","hex":"#0e5094"},{"variable":"$graphblue","hex":"#2874cd"},{"variable":"$searchblue","hex":"#4d95df"},{"variable":"$darkbluetext","hex":"#697c91"},{"variable":"$white","hex":"#ffffff"},{"variable":"$yellowtext","hex":"#fff000"},{"variable":"$black","hex":"#000000"},{"variable":"$linkhovercolor","hex":"#9a430f"},{"variable":"$grey","hex":"#afafaf"},{"variable":"$darkgrey","hex":"#656565"},{"variable":"$pollblue","hex":"#6b6bf7"},{"variable":"$pollred","hex":"#f76b6b"},{"variable":"$pollpink","hex":"#f76bf7"},{"variable":"$pollgreen","hex":"#7bd69c"},{"variable":"$pollturquoise","hex":"#6bd6d6"},{"variable":"$pollorange","hex":"#f7bd6b"},{"variable":"$shortred","hex":"#9a430f"},{"variable":"$longblue","hex":"#2874cd"},{"variable":"$graphorange","hex":"#d39b5e"},{"variable":"$printgreen","hex":"#53b048"},{"variable":"$subpurple","hex":"#43318d"},{"variable":"$buddyorange","hex":"#b36d35"},{"variable":"$greentext","hex":"#009900"},{"variable":"$red","hex":"#cc0000"},{"variable":"$lightgrey","hex":"#d9d9d9"},{"variable":"$yellow","hex":"#ffffc8"},{"variable":"$darkyellow","hex":"#d4d3b0"},{"variable":"$purple","hex":"#5c606f"},{"variable":"$lightpurpletext","hex":"#b2b4b8"},{"variable":"$void1","hex":"#2c3849"},{"variable":"$void2","hex":"#2b3644"},{"variable":"$var-background-gutter","hex":"\"..\/images\/generated\/bkgd\/ff\/gutter.gif?_v=1501713314\""},{"variable":"$var-background-void","hex":"\"..\/images\/generated\/bkgd\/ff\/void.gif?_v=1501713314\""}],};

window.firebaseServiceWorkerSrc = '/alerts-service-worker-production.js';

window.FF.Util = window.FF.Util || {};

window.FF.Util.extend = function(c1, c2) {
    for(let key in c2.prototype) {
        if(!( key in c1.prototype)) {
            c1.prototype[key] = c2.prototype[key];
        }
    }
};


if (typeof flexOptionsDisable === 'undefined'){flexOptionsDisable = {};}
if (typeof flexOptionsVisible === 'undefined'){flexOptionsVisible = {};}
var SECURITYTOKEN = "guest";
var IMGDIR_MISC = "https://assets.faireconomy.media/images/misc";
var RESOURCES = "https://resources.faireconomy.media";
var COOKIE_DOMAIN = "www.forexfactory.com";

window.googletag = window.googletag || {cmd: []};
window.moneySlots = [];
</script> <link rel="preload" href="https://resources.faireconomy.media/ts/dist/alerts-production.js?_v=b5735b35" as="script"> <script async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/money/moneyslotmanager.js?_v=c25f3c16" async="async"></script> <title>Trades | Forex Factory</title> <script type="text/javascript" src="https://resources.faireconomy.media/ts/dist/explorer-production.js?_v=803d28db"></script><script type="text/javascript" src="https://resources.faireconomy.media/ts/dist/cgs-production.js?_v=4f475ad0"></script><script async="" src="https://script.hotjar.com/modules.a3cb6dcf71aec7e1a87f.js" charset="utf-8"></script><meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><script src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202507220101/pubads_impl.js" async=""></script><link href="https://securepubads.g.doubleclick.net/pagead/managed/dict/m202507240101/gpt" rel="compression-dictionary"><script type="text/javascript" src="https://resources.faireconomy.media/ts/dist/alerts-production.js?_v=b5735b35"></script></head> <body> <div class="site--isdesktop"></div> <div class="site--istablet"></div> <div class="site--ismobile"></div> <div id="ui-outer" class="site"> <div id="ui-inner" class="sticky site__inner site__inner--prod" data-env="prod"> <header class="site-header"> <a href="/" class="site-header__logo headerlogo" data-touchable="" aria-label="Forex Factory home page"> <span class="headerlogo__image-container visible-tv visible-dv"> <img class="svg-img svg-img--header-dv-ff" width="93" height="31" src="resources/svg/images/header/dv/ff.svg?b88780ab"> </span> <span class="headerlogo__image-container visible-mv"> <img class="svg-img svg-img--header-mv-ff" width="35" height="31" src="resources/svg/images/header/mv/ff.svg?a57c67b7"> </span> <span class="headerlogo__indicator headerlogo__indicator--inactive"> <svg class="svg svg--product-nav-index" width="16" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-index"></use></svg> </span> <span class="headerlogo__background"></span> <span class="headerlogo__shadow"></span> <span class="headerlogo__borders"></span> </a> <div class="site-header__navs"> <div class="site-header__navs-background"> <ul class="header__product-nav product-nav"> <li class="product-nav__index product-nav--inactive" data-touchable=""> <a href="index.php"> <span class="product-nav__tab-background"> <span class="product-nav__tab-skew"> <svg class="svg svg--product-nav-index" width="16" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-index"></use></svg><span class="product-nav__title">Home</span> </span> </span> </a> </li> <li class="product-nav__forum product-nav--inactive" data-touchable=""> <a href="/forums"> <span class="product-nav__tab-background"> <span class="product-nav__tab-skew"> <svg class="svg svg--product-nav-forum" width="18" height="12"><use href="resources/svg/icons.svg?c70c3c61#product-nav-forum"></use></svg><span class="product-nav__title">Forums</span> </span> </span> </a> </li> <li class="product-nav__trades product-nav--active product-nav--mobile-tab product-nav--mobile-tab-left" data-touchable=""> <a href="/trades"> <span class="product-nav__tab-background"> <span class="product-nav__tab-skew"> <svg class="svg svg--product-nav-trades" width="18" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-trades"></use></svg><span class="product-nav__title">Trades</span> </span> </span> </a> </li> <li class="product-nav__news product-nav--inactive" data-touchable=""> <a href="/news"> <span class="product-nav__tab-background"> <span class="product-nav__tab-skew"> <svg class="svg svg--product-nav-news" width="16" height="16"><use href="resources/svg/icons.svg?c70c3c61#product-nav-news"></use></svg><span class="product-nav__title">News</span> </span> </span> </a> </li> <li class="product-nav__calendar product-nav--inactive product-nav--mobile-tab product-nav--mobile-tab-right" data-touchable=""> <a href="/calendar"> <span class="product-nav__tab-background"> <span class="product-nav__tab-skew"> <svg class="svg svg--product-nav-calendar" width="18" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-calendar"></use></svg><span class="product-nav__title">Calendar</span> </span> </span> </a> </li> <li class="product-nav__market product-nav--inactive" data-touchable=""> <a href="/market"> <span class="product-nav__tab-background"> <span class="product-nav__tab-skew"> <svg class="svg svg--product-nav-market" width="18" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-market"></use></svg><span class="product-nav__title">Market</span> </span> </span> </a> </li> <li class="product-nav__brokers product-nav--inactive" data-touchable=""> <a href="/brokers"> <span class="product-nav__tab-background"> <span class="product-nav__tab-skew"> <svg class="svg svg--product-nav-brokers" width="20" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-brokers"></use></svg><span class="product-nav__title">Brokers</span> </span> </span> </a> </li> </ul> <a href="/login" class="visible-mv mv-member-nav mv-member-nav--inactive" data-touchable="" aria-label="Log in"><span class="mv-member-nav__angle"></span><span class="mv-member-nav__bkg"><svg class="svg svg--login" width="12" height="10"><use href="resources/svg/icons.svg?c70c3c61#login"></use></svg></span></a><ul class="header__member-nav member-nav usertoolbar member-nav--login-inactive visible-tv visible-dv"><li class="member-nav__alerts toggle member-nav--inactive" data-login=""><a class="member-nav--angle member-nav--angle-ll"><span class="alert-header"><svg class="svg svg--alerts-bell" width="12" height="14"><use href="resources/svg/icons.svg?c70c3c61#alerts-bell"></use></svg></span></a></li><li class="member-nav__login member-nav--inactive" data-login=""><a class="member-nav--angle member-nav--angle-ll" href="javascript:void(0);"><span><svg class="svg svg--login" width="12" height="10"><use href="resources/svg/icons.svg?c70c3c61#login"></use></svg><span class="member-nav__login-text">Login</span></span></a></li><li class="member-nav__join join member-nav--inactive"><a class="member-nav--angle member-nav--angle-ll" href="/create-account"><span><svg class="svg svg--join" width="16" height="10"><use href="resources/svg/icons.svg?c70c3c61#join"></use></svg><span class="member-nav__join-text">Create </span><span class="member-nav__join-text member-nav__join-text--extended">Account</span></span></a></li><li class="member-nav__login-form member-nav--light-active member-nav--angle member-nav--angle-lr loginform active"><span><form action="/login" method="post"><input type="hidden" name="_csrf" value="b1476b4155b899debba83b5d37555e44"><input type="hidden" name="do" value="login"><input type="hidden" name="gotoprofile" value="1"><label for="vb_login_username_header">User/Email:</label><input type="text" id="vb_login_username_header" name="vb_login_username" class="small" value="" autocomplete="username" data-enterhandled="1"><label for="vb_login_password_header">Password:</label><input type="password" id="vb_login_password_header" name="vb_login_password" class="small" value="" autocomplete="current-password" data-enterhandled="1"><span><input type="submit" value="Login"></span></form></span></li><li class="member-nav__time time member-nav--inactive" style="position: relative;"><a href="/timezone" title="Time zone: " class="member-nav--angle member-nav--angle-ll"><span><svg width="14" height="14" class="svg svg--header-time"><use href="resources/svg/icons.svg?c70c3c61#header-time"></use></svg>
					1:20pm
				</span></a></li><li class="member-nav__search member-nav--inactive member-nav--angle member-nav--angle-lr search"><span><form class="searchbox" data-type="searchBing" method="get" action="/search"><input type="search" placeholder="Search" name="search" class="searchbox__input" value=""><div class="submit searchbox__button searchbox__button--svg"><svg class="svg svg--search" width="11" height="11"><use href="resources/svg/icons.svg?c70c3c61#search"></use></svg><input type="submit" value="Search"></div></form></span></li></ul> </div> <div class="site-header__navs-border"></div> </div> </header> <alerts-header-modal></alerts-header-modal> <!----> <header class="mobileheader mobileheader--invisible"> <div class="mobileheader__container"> <div class="mobileheader__content"> <a class="mobileheader__strip" data-touchable=""> <span>Menu</span> </a> <a href="/" class="mobileheader__home" data-touchable="" aria-label="Forex Factory home page"> <span class="mobileheader__logo"> <img class="svg-img svg-img--header-dv-ff" width="93" height="31" src="resources/svg/images/header/dv/ff.svg?b88780ab"> </span> <span class="mobileheader__indicator "> <svg class="svg svg--header-trapezoid" width="50" height="26"><use href="resources/svg/icons.svg?c70c3c61#header-trapezoid"></use></svg> <svg class="svg svg--product-nav-index" width="16" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-index"></use></svg> </span> </a> <ul class="mobileheader__nav mobileheader__nav--productnav"> <li> <a href="/forums" class="mobileheader__button mobileheader__button--forum mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--product-nav-forum" width="18" height="12"><use href="resources/svg/icons.svg?c70c3c61#product-nav-forum"></use></svg>Forums
</a> </li> <li> <a href="/trades" class="mobileheader__button mobileheader__button--trades mobileheader__button--active" data-touchable=""> <svg class="svg svg--product-nav-trades" width="18" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-trades"></use></svg>Trades
</a> </li> <li> <a href="/news" class="mobileheader__button mobileheader__button--news mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--product-nav-news" width="16" height="16"><use href="resources/svg/icons.svg?c70c3c61#product-nav-news"></use></svg>News
</a> </li> <li> <a href="/calendar" class="mobileheader__button mobileheader__button--calendar mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--product-nav-calendar" width="18" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-calendar"></use></svg>Calendar
</a> </li> <li> <a href="/market" class="mobileheader__button mobileheader__button--market mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--product-nav-market" width="18" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-market"></use></svg>Market
</a> </li> <li> <a href="/brokers" class="mobileheader__button mobileheader__button--brokers mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--product-nav-brokers" width="20" height="14"><use href="resources/svg/icons.svg?c70c3c61#product-nav-brokers"></use></svg>Brokers
</a> </li> </ul><ul class="mobileheader__nav mobileheader__nav--membernav"> <li> <a href="/login" class="mobileheader__button mobileheader__button--login mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--login" width="12" height="10"><use href="resources/svg/icons.svg?c70c3c61#login"></use></svg>
                Login
</a> </li> <li> <a href="/create-account" class="mobileheader__button mobileheader__button--join mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--join" width="16" height="10"><use href="resources/svg/icons.svg?c70c3c61#join"></use></svg>
                Create Account
</a> </li> <li> <a href="/timezone" class="mobileheader__button mobileheader__button--time mobileheader__button--inactive" data-touchable=""> <svg class="svg svg--header-time" width="14" height="14"><use href="resources/svg/icons.svg?c70c3c61#header-time"></use></svg>1:20pm
</a> </li> </ul> <form class="searchbox" data-type="searchBing" method="get" action="/search"> <input type="search" placeholder="Search" name="search" class="searchbox__input" value=""> <div class="submit searchbox__button searchbox__button--svg"> <svg class="svg svg--search" width="11" height="11"><use href="resources/svg/icons.svg?c70c3c61#search"></use></svg> <input type="submit" value="Search"> </div> </form> </div> </div> <a href="javascript:void(0);" class="mobileheader__overlay"></a> </header> <div id="content" class="site__content site__content--sidebar sidebar"> <section class="sidebar"> <div class="panel adbit sidebar__panel sidebar__panel--adbit"> <div id="__google-e0a6da483b27334c9b1683130e950092" data-google-query-id="COX3mKPn3o4DFbKaZgIdvJYSKA">  <div id="google_ads_iframe_/1014366/ff_branding_sidebar_0__container__" style="border: 0pt none;"><iframe id="google_ads_iframe_/1014366/ff_branding_sidebar_0" name="google_ads_iframe_/1014366/ff_branding_sidebar_0" title="3rd party ad content" width="160" height="600" scrolling="no" marginwidth="0" marginheight="0" frameborder="0" aria-label="Advertisement" tabindex="0" allow="private-state-token-redemption;attribution-reporting" data-load-complete="true" data-google-container-id="1" style="border: 0px; vertical-align: bottom;"></iframe></div></div> </div> <div class="sidebar__panel panel visible-tv visible-dv"> <h2>Forex Broker Activity</h2> <ul class="bulleted"> <li><a href="/brokers/spreads">Live Forex Spreads</a></li> <li><a href="/brokers#updates">Broker Guide Updates</a></li> </ul> </div> </section> <section class="content trades" rel="trades"> <h1 class="hidden">Forex Trades</h1> <div class="pagearrange__layout pagearrange__layout--arrangeable pagearrange__layout--zippable full" data-id="trades"> <div class="pagearrange__layout-row full" data-row="0"> <div class="pagearrange__layout-column pagearrange__layout-column--half  half" data-column="0"> <div class="pagearrange__layout-cell" data-cell="0" data-compid="TradeFeed" data-zip-index-offset="0"> <a name="TradeFeed" class="snap"></a> <div class="flexShell"> <div class="flexBox trades_activity trades_activity--moredefault" id="flexBox_flex_trades/activity_tradesActivity" data-more="0" data-checkstate="0" data-initcallback="trade_activity" data-visiblejs="{'buddies.off':[['format.threads'],['format.large'],['sort.latestliked'],['sort.mostliked']],'buddies.on':[['format.threads'],['format.large'],['sort.latestliked'],['sort.mostliked']]}" data-disablejs="{'buddies.off':[['format.threads'],['format.large'],['sort.latestliked'],['sort.mostliked']],'buddies.on':[['format.threads'],['format.large'],['sort.latestliked'],['sort.mostliked']]}" data-isdefault="1"> <form action="flex.php" method="post" onsubmit="return Flex.prepareSubmit(this);" data-submit="options"> <input type="hidden" name="_csrf" value="b1476b4155b899debba83b5d37555e44"> <input type="hidden" name="do" value="saveoptions"> <input type="hidden" name="setdefault" value="no"> <input type="hidden" name="ignoreinput" value="no"> <input type="hidden" name="flex[Trades/Activity_tradesActivity][idSuffix]" value=""> <input type="hidden" name="flex[Trades/Activity_tradesActivity][_flexForm_]" value="flexForm"> <input type="hidden" name="flex[Trades/Activity_tradesActivity][modelData]" value="eyJwcmVsb2FkT25WaWV3Ijp0cnVlLCJkZWZhdWx0SW5zdHJ1bWVudHMiOlsic3RvY2tzIiwiQVVEQ0FEIiwiQVVEQ0hGIiwiQVVER0JQIiwiQVVESlBZIiwiQVVETlpEIiwiQVVEU0dEIiwiQVVEVVNEIiwiQ0FEQ0hGIiwiQ0FESlBZIiwiQ0hGSlBZIiwiREtLSlBZIiwiRVVSQVVEIiwiRVVSQ0FEIiwiRVVSQ0hGIiwiRVVSR0JQIiwiRVVSSlBZIiwiRVVSTlpEIiwiRVVSU0dEIiwiRVVSVVNEIiwiR0JQQVVEIiwiR0JQQ0FEIiwiR0JQQ0hGIiwiR0JQSlBZIiwiR0JQTlpEIiwiR0JQU0dEIiwiR0JQVVNEIiwiTlpEQ0FEIiwiTlpEQ0hGIiwiTlpESlBZIiwiTlpEVVNEIiwiU0dESlBZIiwiVVNEQlJMIiwiVVNEQ0FEIiwiVVNEQ0hGIiwiVVNEQ05IIiwiVVNESlBZIiwiVVNETVhOIiwiVVNEU0VLIiwiVVNEU0dEIiwiVVNEWkFSIl0sInBhX2xheW91dF9pZCI6InRyYWRlcyIsInBhX2NvbXBvbmVudF9pZCI6IlRyYWRlRmVlZCIsInBhX2NvbnRyb2xzIjoidHJhZGVzfFRyYWRlRmVlZCIsInBhX2luamVjdHJldmVyc2UiOmZhbHNlLCJwYV9oYXJkaW5qZWN0aW9uIjpmYWxzZSwicGFfaW5qZWN0YXQiOmZhbHNlLCJzaG93TGl2ZSI6dHJ1ZSwic3RyZWFtQ2hlY2tib3giOiI8c3BhbiBjbGFzcz1cImZsZXhFeHRlcm5hbE9wdGlvblwiIGRhdGEtdHlwZT1cIklubGluZUNoZWNrYm94XCI+PGlucHV0IHR5cGU9XCJjaGVja2JveFwiIGNsYXNzPVwiY2hlY2tib3hcIiBuYW1lPVwiZmxleFtUcmFkZXNcL0FjdGl2aXR5X3RyYWRlc0FjdGl2aXR5XVt0cmFkZXNcL3N0cmVhbV1bXVwiIGlkPVwiZmxleFtUcmFkZXNcL0FjdGl2aXR5X3RyYWRlc0FjdGl2aXR5XVt0cmFkZXNcL3N0cmVhbV1bMV1cIiB2YWx1ZT1cIjFcIj48bGFiZWwgZm9yPVwiZmxleFtUcmFkZXNcL0FjdGl2aXR5X3RyYWRlc0FjdGl2aXR5XVt0cmFkZXNcL3N0cmVhbV1bMV1cIiA+U3RyZWFtPFwvbGFiZWw+XG48XC9zcGFuPiJ9"> <div class="head"> <ul> <li class="left"><h2><a title="View Options" class="highlight light options flexTitle"><span>Trade <span class="tradetype__feed">Feed</span> / Live Accounts</span></a></h2></li> <li class="loader"></li> <li class="right imagefade noborder "><a title="Filter" class="highlight noborder filters flexFilter"><div class="fade"></div><span class="visible-tv visible-dv">Filter</span></a></li> <li title="Stream Delayed Data" data-touchable="" class="containerCheck right livefeed livefeed--enabled highlight liveCheck livefeed--inactive"><span class="flexExternalOption" data-type="InlineCheckbox"><input type="checkbox" class="checkbox" name="flex[Trades/Activity_tradesActivity][trades/stream][]" id="flex[Trades/Activity_tradesActivity][trades/stream][1]" value="1"><label for="flex[Trades/Activity_tradesActivity][trades/stream][1]">Stream</label> </span></li> <li class="pagearrange__layoutcontrols layoutcontrols"><div class="pagearrange__controls pagearrange_controls"> <span class="moveRight" title="Move Block Right"></span></div> </li></ul> </div> <div class="options"> <div> <div class="overlay overlay--options trades_activity__flexoptions shell flexoptions"> <div class="overlay__title"> <span class="overlay__icon overlay__icon--options overlay__icon--trades_activityoptions"></span>Trade Feed Settings
</div> <div class="overlay__content"> <div class="flexcontrols"> <div class="flexcontrols__row"> <div class="flexcontrols__cell flexcontrols__cell--tradetype"> <p class="flexcontrols__title">Trade Type</p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--radio "> <ul class="flexcontrols__list flexcontrols__list--radio"> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="all" id="flex[Trades/Activity_tradesActivity][trades/tradetype]_all" name="flex[Trades/Activity_tradesActivity][trades/tradetype]" checked="checked" data-isdefault="true"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][trades/tradetype]_all">All Trades</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="entries" id="flex[Trades/Activity_tradesActivity][trades/tradetype]_entries" name="flex[Trades/Activity_tradesActivity][trades/tradetype]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][trades/tradetype]_entries">Entries</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="exits" id="flex[Trades/Activity_tradesActivity][trades/tradetype]_exits" name="flex[Trades/Activity_tradesActivity][trades/tradetype]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][trades/tradetype]_exits">Exits</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="winners" id="flex[Trades/Activity_tradesActivity][trades/tradetype]_winners" name="flex[Trades/Activity_tradesActivity][trades/tradetype]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][trades/tradetype]_winners">Winning Exits</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="losers" id="flex[Trades/Activity_tradesActivity][trades/tradetype]_losers" name="flex[Trades/Activity_tradesActivity][trades/tradetype]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][trades/tradetype]_losers">Losing Exits</label> </div> </li> </ul> </div> </div> <div class="flexcontrols__cell flexcontrols__cell--accounttype"> <p class="flexcontrols__title">Brokerage Account Type</p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--radio "> <ul class="flexcontrols__list flexcontrols__list--radio"> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="live" id="flex[Trades/Activity_tradesActivity][trades/accounttype]_live" name="flex[Trades/Activity_tradesActivity][trades/accounttype]" checked="checked" data-isdefault="true"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][trades/accounttype]_live">Live Accounts</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="demo" id="flex[Trades/Activity_tradesActivity][trades/accounttype]_demo" name="flex[Trades/Activity_tradesActivity][trades/accounttype]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][trades/accounttype]_demo">Demo Accounts</label> </div> </li> </ul> </div> <p class="darktext">Trade feed excludes Explorers that are set to private or traded by a commercial user.</p> </div> </div> </div> <div class="overlay__pad "></div> </div> <table class="overlay__controls "> <tbody><tr> <td class="overlay__error overlay__error--options overlay__button overlay__button--disabled flexOptionsError" data-touchable=""></td> <td class="overlay__submits flexSubmitButtons"> <input type="submit" class="overlay__button overlay__button--submit button flexOptionsSubmit" name="flexSettings" value="Apply Settings" data-touchable=""> <input type="button" class="overlay__button overlay__button--cancel button flexCancelOptions" value="Cancel" data-touchable=""> </td> <td class="overlay__defaults flexDefaults"></td> </tr> </tbody></table> </div> </div> <div> <div class="overlay overlay--filters trades_activity__flexfilters shell flexfilters"> <div class="overlay__title"> <span class="overlay__icon overlay__icon--filters overlay__icon--trades_activityfilters"></span>Trade Feed Filters
</div> <div class="overlay__content"> <div class="flexcontrols"> <div class="flexcontrols__row"> <div class="flexcontrols__cell flexcontrols__cell--mirs"> <div class="mirsfilter voucherFilter"> <p class="flexcontrols__title">Impact Hurdle</p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--radio"> <ul class="flexcontrols__list flexcontrols__list--radio"> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input data-click="active" type="radio" name="flex[Trades/Activity_tradesActivity][mirs]" id="flex[Trades/Activity_tradesActivity][mirs][vouchers]_0" value="0" checked="checked" data-isdefault="true"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Activity_tradesActivity][mirs][vouchers]_0">Off</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input data-click="active" type="radio" name="flex[Trades/Activity_tradesActivity][mirs]" id="flex[Trades/Activity_tradesActivity][mirs][vouchers]_1" value="1"> </div> <div class="flexcontrols__label flexcontrols__label--radio"> <label for="flex[Trades/Activity_tradesActivity][mirs][vouchers]_1">Only show trades from traders ranked at least:</label> <div> <select disabled="disabled" name="flex[Trades/Activity_tradesActivity][mirs]" style="margin-top: 4px;" class="button" data-forcedisable="yes"><option value="1">Low Impact</option> <option value="2">Medium Impact</option> <option value="3">High Impact</option> </select> <span class="mirsfilter__container mirs_container"><span style="display:none" class="mirsfilter__mirs mirs large "></span></span> </div> </div> </li> </ul> </div> </div> </div> <div class="flexcontrols__cell flexcontrols__cell--subscriptions"> <p class="flexcontrols__title">Subscriptions</p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--radio "> <ul class="flexcontrols__list flexcontrols__list--radio"> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="off" id="flex[Trades/Activity_tradesActivity][buddies]_off" name="flex[Trades/Activity_tradesActivity][buddies]" checked="checked" data-isdefault="true" data-forcedisable="yes" disabled="disabled"> </div> <div class="flexcontrols__label flexcontrols__label--radio flexcontrols__label--disabled"> <label for="flex[Trades/Activity_tradesActivity][buddies]_off">Off</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="on" id="flex[Trades/Activity_tradesActivity][buddies]_on" name="flex[Trades/Activity_tradesActivity][buddies]" data-forcedisable="yes" disabled="disabled"> </div> <div class="flexcontrols__label flexcontrols__label--radio flexcontrols__label--disabled"> <label for="flex[Trades/Activity_tradesActivity][buddies]_on">Only show trades from traders you are subscribed to</label> </div> </li> </ul> </div> <span class="darktext">
    You must be registered to have subscriptions (<a class="darklink" href="/create-account" target="_blank">join now</a>).
</span> </div> </div> <div class="flexcontrols__row"> <div class="flexcontrols__cell flexcontrols__cell--activityinstruments"> <div class="multicheck"> <p class="flexcontrols__title title">
Currency Pairs
<span class="normal">
(<a class="internal" href="#" onclick="return Flex.checkMulti(this, /flex\[Trades\/Activity_tradesActivity\]\[trades\/instruments\]/, true, true, 'No Instruments Selected');"><span>all</span><span class="loader"></span></a>, <a class="internal" href="#" onclick="return Flex.checkMulti(this, /flex\[Trades\/Activity_tradesActivity\]\[trades\/instruments\]/, false, true, 'No Instruments Selected');"><span>none</span></a>, <a href="#" class="internal" onclick="Flex.selectMultiCheckbox(this, 'Trades\/Activity_tradesActivity', 'trades\/instruments', ['EURUSD','GBPUSD','USDJPY','USDCHF','NZDUSD','USDCAD','AUDUSD'], true, 'No Instruments Selected'); return false;"><span>majors</span></a>)
</span> </p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--checkbox-columns"> <div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][AUDCAD]" value="AUDCAD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][AUDCAD]">
AUD/CAD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][AUDCHF]" value="AUDCHF" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][AUDCHF]">
AUD/CHF
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][AUDGBP]" value="AUDGBP" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][AUDGBP]">
AUD/GBP
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][AUDJPY]" value="AUDJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][AUDJPY]">
AUD/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][AUDNZD]" value="AUDNZD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][AUDNZD]">
AUD/NZD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][AUDSGD]" value="AUDSGD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][AUDSGD]">
AUD/SGD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][AUDUSD]" value="AUDUSD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][AUDUSD]">
AUD/USD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][CADCHF]" value="CADCHF" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][CADCHF]">
CAD/CHF
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][CADJPY]" value="CADJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][CADJPY]">
CAD/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][CHFJPY]" value="CHFJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][CHFJPY]">
CHF/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][DKKJPY]" value="DKKJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][DKKJPY]">
DKK/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURAUD]" value="EURAUD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURAUD]">
EUR/AUD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURCAD]" value="EURCAD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURCAD]">
EUR/CAD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURCHF]" value="EURCHF" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURCHF]">
EUR/CHF
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURGBP]" value="EURGBP" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURGBP]">
EUR/GBP
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURJPY]" value="EURJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURJPY]">
EUR/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURNZD]" value="EURNZD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURNZD]">
EUR/NZD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURSGD]" value="EURSGD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURSGD]">
EUR/SGD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][EURUSD]" value="EURUSD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][EURUSD]">
EUR/USD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][GBPAUD]" value="GBPAUD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][GBPAUD]">
GBP/AUD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][GBPCAD]" value="GBPCAD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][GBPCAD]">
GBP/CAD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][GBPCHF]" value="GBPCHF" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][GBPCHF]">
GBP/CHF
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][GBPJPY]" value="GBPJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][GBPJPY]">
GBP/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][GBPNZD]" value="GBPNZD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][GBPNZD]">
GBP/NZD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][GBPSGD]" value="GBPSGD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][GBPSGD]">
GBP/SGD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][GBPUSD]" value="GBPUSD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][GBPUSD]">
GBP/USD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][NZDCAD]" value="NZDCAD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][NZDCAD]">
NZD/CAD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][NZDCHF]" value="NZDCHF" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][NZDCHF]">
NZD/CHF
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][NZDJPY]" value="NZDJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][NZDJPY]">
NZD/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][NZDUSD]" value="NZDUSD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][NZDUSD]">
NZD/USD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][SGDJPY]" value="SGDJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][SGDJPY]">
SGD/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDBRL]" value="USDBRL" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDBRL]">
USD/BRL
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDCAD]" value="USDCAD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDCAD]">
USD/CAD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDCHF]" value="USDCHF" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDCHF]">
USD/CHF
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDCNH]" value="USDCNH" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDCNH]">
USD/CNH
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDJPY]" value="USDJPY" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDJPY]">
USD/JPY
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDMXN]" value="USDMXN" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDMXN]">
USD/MXN
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDSEK]" value="USDSEK" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDSEK]">
USD/SEK
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDSGD]" value="USDSGD" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDSGD]">
USD/SGD
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][USDZAR]" value="USDZAR" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][USDZAR]">
USD/ZAR
</label> </div> </div> <div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <span class="flexcontrols__checkbox-spacer spacer">&nbsp;</span> </div> </div><div><div class="flexcontrols__checkbox flexcontrols__checkbox--trades/instruments"> <div class="flexcontrols__selector flexcontrols__selector--checkbox"> <input type="checkbox" name="flex[Trades/Activity_tradesActivity][trades/instruments][]" id="flex[Trades/Activity_tradesActivity][trades/instruments][stocks]" value="stocks" checked="checked" data-isdefault="true" onclick="Flex.multiCheckboxClicked(this, 'Trades\/Activity_tradesActivity', true, 'No Instruments Selected');"> </div> <div class="flexcontrols__label flexcontrols__label--checkbox"> <label for="flex[Trades/Activity_tradesActivity][trades/instruments][stocks]">
Non-Forex
</label> </div> </div> </div> </div> </div> </div> </div> </div> <div class="overlay__pad "></div> </div> <table class="overlay__controls "> <tbody><tr> <td class="overlay__message overlay__message--invisible">Saving…</td> <td class="overlay__error overlay__error--filter overlay__button overlay__button--disabled flexFilterError"></td> <td class="overlay__submits flexSubmitButtons"> <input type="submit" class="overlay__button overlay__button--submit button flexFilterSubmit" name="flexFilters" value="Apply Filter" data-touchable=""> <input type="button" class="overlay__button overlay__button--cancel button flexCancelFilters" value="Cancel" data-touchable=""> </td> <td class="overlay__defaults flexDefaults"></td> </tr> </tbody></table> </div> </div> </div> </form> <table class="activity trades_activity__table"> <thead> <tr> <th class="trades_activity__header trades_activity__header--trade col1">Trade</th> <th class="trades_activity__header trades_activity__header--trader col2">Trader</th> <th class="trades_activity__header trades_activity__header--return col3"><a class="tooltip__info" data-info="trades_activity_return" data-registered="true" data-shell="tooltip1753680048487"></a>Return</th> <th class="trades_activity__header trades_activity__header--pips col4"><a class="tooltip__info" data-info="trades_activity_pips" data-registered="true"></a>Pips</th> </tr> </thead> <tbody> <tr id="flexActivity_257727_1076746473_1753679848" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
BTC/USD
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span> <span class="normal trades_activity__approx">~</span>                119,431.3
</strong> </p> <p class="trades_activity__info trades_activity__info--scaled caption" data-timestamp="1753679848"><a href="javascript:void(0);" class="internal showscaling" data-scale="flexActivity_257727_1076746473_1753679848"> <span>Scaled Out</span> </a> | <span class="nowrap">~3 min ago</span> </p> <div class="trades_activity__scaled"> <p class="caption"><a href="/dominicus#acct.44-tab.list-ticket.1076746473">Closed Long</a> at 119,431.3</p><p class="caption"><a href="/dominicus#acct.44-tab.list-ticket.1076747885">Closed Long</a> at 119,431.3</p><p class="caption"><a href="/dominicus#acct.44-tab.list-ticket.1076748390">Closed Long</a> at 119,431.3</p> </div> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="dominicus" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2082355_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">Dominicus</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <div class="trades_activity__scaled"> <div class="return"> <span class="better">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div> </div> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <div class="trades_activity__scaled"> <div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div><div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div><div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div> </div> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_238316_77289320_1753679838" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span> <span class="normal trades_activity__approx">~</span>                1.1742
</strong> </p> <p class="trades_activity__info trades_activity__info--scaled caption" data-timestamp="1753679838"><a href="javascript:void(0);" class="internal showscaling" data-scale="flexActivity_238316_77289320_1753679838"> <span>Scaled Out</span> </a> | <span class="nowrap">~3 min ago</span> </p> <div class="trades_activity__scaled"> <p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77289320">Closed Short</a> at 1.1742</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77290275">Closed Short</a> at 1.1742</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77290967">Closed Short</a> at 1.1742</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77292012">Closed Short</a> at 1.1742</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77293158">Closed Short</a> at 1.1742</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77294200">Closed Short</a> at 1.1742</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77296243">Closed Short</a> at 1.1742</p> </div> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="geo1683" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar169165_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">geo1683</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <div class="trades_activity__scaled"> <div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div> </div> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <div class="trades_activity__scaled"> <div class="pips"> <span class="worse">
-18
</span> </div><div class="pips"> <span class="worse">
-14
</span> </div><div class="pips"> <span class="worse">
-10
</span> </div><div class="pips"> <span class="worse">
-4
</span> </div><div class="pips"> <span class="better">
0
</span> </div><div class="pips"> <span class="better">
4
</span> </div><div class="pips"> <span class="better">
23
</span> </div> </div> <span class="better">4</span> </td> </tr> <tr id="flexActivity_227391_66445238_1753679827" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1742
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679827"><a href="/gus101#acct.08-tab.list-ticket.66445238">Opened Long</a> | <span class="nowrap">3 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="gus101" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar418237_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">gus101</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-0</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_260362_997709793_1753679805" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdc
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                3,338.498
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679805"><a href="/fusiongoldfx#acct.90-tab.list-ticket.997709793">Opened Short</a> | <span class="nowrap">4 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="fusiongoldfx" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2179615_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">fusiongoldfx</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_260362_997107743_1753679804" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdc
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span> <span class="normal trades_activity__approx">~</span>                3,338.7
</strong> </p> <p class="trades_activity__info trades_activity__info--scaled caption" data-timestamp="1753679804"><a href="javascript:void(0);" class="internal showscaling" data-scale="flexActivity_260362_997107743_1753679804"> <span>Scaled Out</span> </a> | <span class="nowrap">~4 min ago</span> </p> <div class="trades_activity__scaled"> <p class="caption"><a href="/fusiongoldfx#acct.90-tab.list-ticket.997107743">Closed Short</a> at 3,338.714</p><p class="caption"><a href="/fusiongoldfx#acct.90-tab.list-ticket.997347425">Closed Short</a> at 3,338.714</p><p class="caption"><a href="/fusiongoldfx#acct.90-tab.list-ticket.997478886">Closed Short</a> at 3,338.714</p> </div> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="fusiongoldfx" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2179615_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">fusiongoldfx</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <div class="trades_activity__scaled"> <div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="better">0.2%</span> </div> </div> <span class="better">0.1%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <div class="trades_activity__scaled"> <div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div><div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div><div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div> </div> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_259133_4891379_1753679797" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdc
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                3,338.36
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679797"><a href="/pipclubtrade#acct.53-tab.list-ticket.4891379">Opened Short</a> | <span class="nowrap">4 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="pipclubtrade" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">pipclubtrade</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_173134_61680847_1753679793" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1744
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679793"><a href="/thetrip75#acct.87-tab.list-ticket.61680847">Closed Short</a> | <span class="nowrap">4 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="thetrip75" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">theTrip75</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.1%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="better">20</span> </td> </tr> <tr id="flexActivity_259133_4891137_1753679793" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdc
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,338.58
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679793"><a href="/pipclubtrade#acct.53-tab.list-ticket.4891137">Closed Short</a> | <span class="nowrap">4 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="pipclubtrade" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">pipclubtrade</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_250578_29148943_1753679785" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                1.1743
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679785"><a href="/pnutfx503#acct.85-tab.list-ticket.29148943">Closed Long</a> | <span class="nowrap">4 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="pnutfx503" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">Pnutfx503</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">-0.5%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-11</span> </td> </tr> <tr id="flexActivity_249647_5184603614_1753679739" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusd_o
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,339.3
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679739"><a href="/aminx100#acct.10-tab.list-ticket.5184603614">Opened Long</a> | <span class="nowrap">5 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="aminx100" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1476932_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">AminX100</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">-0.1%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_238316_77308941_1753679709" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                1.1743
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679709"><a href="/geo1683#acct.69-tab.list-ticket.77308941">Opened Short</a> | <span class="nowrap">5 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="geo1683" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar169165_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">geo1683</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="better">1</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_238316_77308939_1753679708" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1745
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679708"><a href="/geo1683#acct.69-tab.list-ticket.77308939">Opened Long</a> | <span class="nowrap">5 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="geo1683" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar169165_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">geo1683</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-4</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_253283_3347969021_1753679703" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1745
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679703"><a href="/uaetrader#acct.11-tab.list-ticket.3347969021">Opened Long</a> | <span class="nowrap">5 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="uaetrader" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1410422_3.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">Uaetrader</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-4</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_259392_53185992_1753679686" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
USD/JPY
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                147.82
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679686"><a href="/thaoanh#acct.20-tab.list-ticket.53185992">Opened Long</a> | <span class="nowrap">6 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="thaoanh" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1878736_5.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">ThaoAnh</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-2</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_256819_50204732798_1753679671" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Cac_l
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                7,914.1
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679671"><a href="/sobhanjavadi#acct.43-tab.list-ticket.50204732798">Closed Long</a> | <span class="nowrap">6 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="sobhanjavadi" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2102104_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">sobhanjavadi</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_224732_953969526_1753679634" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1745
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679634"><a href="/hosseinjal#acct.98-tab.list-ticket.953969526">Opened Long</a> | <span class="nowrap">6 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="hosseinjal" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">hosseinjal</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">-0.1%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-3</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_256819_50203914533_1753679604" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Ethusd_l
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,925.75
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679604"><a href="/sobhanjavadi#acct.43-tab.list-ticket.50203914533">Opened Long</a> | <span class="nowrap">7 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="sobhanjavadi" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2102104_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">sobhanjavadi</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_259249_71651452_1753679520" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
XAU/USD
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                3,339.08
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679520"><a href="/yeluo#acct.84-tab.list-ticket.71651452">Closed Long</a> | <span class="nowrap">8 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="yeluo" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">yeluo</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_227390_91036071_1753679508" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1744
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679508"><a href="/gus101#acct.11-tab.list-ticket.91036071">Opened Long</a> | <span class="nowrap">8 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="gus101" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar418237_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">gus101</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-3</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_258566_26699976_1753679482" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
XAU/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span> <span class="normal trades_activity__approx">~</span>                3,339.8
</strong> </p> <p class="trades_activity__info trades_activity__info--scaled caption" data-timestamp="1753679482"><a href="javascript:void(0);" class="internal showscaling" data-scale="flexActivity_258566_26699976_1753679482"> <span>Scaled In</span> </a> | <span class="nowrap">~9 min ago</span> </p> <div class="trades_activity__scaled"> <p class="caption"><a href="/aimaxxtrade#acct.97-tab.list-ticket.26699976">Opened Long</a> at 3,339.75</p><p class="caption"><a href="/aimaxxtrade#acct.97-tab.list-ticket.26700106">Opened Long</a> at 3,339.94</p> </div> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="aimaxxtrade" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">AiMaxxTrade</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <div class="trades_activity__scaled"> <div class="return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm mtm">MTM</span> </div><div class="return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm mtm">MTM</span> </div> </div> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <div class="trades_activity__scaled"> <div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div><div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div> </div> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_258009_161972745_1753679464" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1745
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679464"><a href="/winstonfxf#acct.79-tab.list-ticket.161972745">Opened Long</a> | <span class="nowrap">9 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="winstonfxf" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">WinstonFxF</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-4</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_258009_161968420_1753679461" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1745
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679461"><a href="/winstonfxf#acct.79-tab.list-ticket.161968420">Closed Short</a> | <span class="nowrap">9 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="winstonfxf" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">WinstonFxF</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.1%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="better">12</span> </td> </tr> <tr id="flexActivity_238316_77289341_1753679449" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span> <span class="normal trades_activity__approx">~</span>                1.1745
</strong> </p> <p class="trades_activity__info trades_activity__info--scaled caption" data-timestamp="1753679449"><a href="javascript:void(0);" class="internal showscaling" data-scale="flexActivity_238316_77289341_1753679449"> <span>Scaled Out</span> </a> | <span class="nowrap">~9 min ago</span> </p> <div class="trades_activity__scaled"> <p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77289341">Closed Short</a> at 1.1745</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77290337">Closed Short</a> at 1.1745</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77291068">Closed Short</a> at 1.1745</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77292030">Closed Short</a> at 1.1745</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77293236">Closed Short</a> at 1.1745</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77294222">Closed Short</a> at 1.1745</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77295820">Closed Short</a> at 1.1745</p><p class="caption"><a href="/geo1683#acct.69-tab.list-ticket.77296261">Closed Short</a> at 1.1745</p> </div> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="geo1683" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar169165_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">geo1683</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <div class="trades_activity__scaled"> <div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="worse">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div> </div> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <div class="trades_activity__scaled"> <div class="pips"> <span class="worse">
-21
</span> </div><div class="pips"> <span class="worse">
-17
</span> </div><div class="pips"> <span class="worse">
-13
</span> </div><div class="pips"> <span class="worse">
-8
</span> </div><div class="pips"> <span class="worse">
-3
</span> </div><div class="pips"> <span class="better">
1
</span> </div><div class="pips"> <span class="better">
13
</span> </div><div class="pips"> <span class="better">
19
</span> </div> </div> <span class="better">4</span> </td> </tr> <tr id="flexActivity_238316_77308685_1753679400" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
EUR/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.1747
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679400"><a href="/geo1683#acct.69-tab.list-ticket.77308685">Opened Long</a> | <span class="nowrap">10 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="geo1683" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar169165_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">geo1683</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-5</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_253790_67241042_1753679374" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
XTI/USD
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                66.54
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679374"><a href="/anawatzaa#acct.41-tab.list-ticket.67241042">Closed Long</a> | <span class="nowrap">11 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="anawatzaa" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar589351_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">anawatzaa</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_257683_14550127_1753679343" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
GBP/CHF
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                1.0691
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679343"><a href="/maxsociety#acct.65-tab.list-ticket.14550127">Opened Long</a> | <span class="nowrap">11 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="maxsociety" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">MaxSociety</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">-4.8%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="worse">-2</span> <span class="trades_activity__mtm mtm">MTM</span> </td> </tr> <tr id="flexActivity_257727_1076747885_1753679329" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
BTC/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span> <span class="normal trades_activity__approx">~</span>                119,269.0
</strong> </p> <p class="trades_activity__info trades_activity__info--scaled caption" data-timestamp="1753679329"><a href="javascript:void(0);" class="internal showscaling" data-scale="flexActivity_257727_1076747885_1753679329"> <span>Scaled In</span> </a> | <span class="nowrap">~11 min ago</span> </p> <div class="trades_activity__scaled"> <p class="caption"><a href="/dominicus#acct.44-tab.list-ticket.1076747885">Opened Long</a> at 119,295.9</p><p class="caption"><a href="/dominicus#acct.44-tab.list-ticket.1076748390">Opened Long</a> at 119,242</p> </div> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="dominicus" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2082355_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">Dominicus</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <div class="trades_activity__scaled"> <div class="return"> <span class="better">0.0%</span> </div><div class="return"> <span class="better">0.0%</span> </div> </div> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <div class="trades_activity__scaled"> <div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div><div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div> </div> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_259133_4891137_1753679308" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdc
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                3,338.88
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679308"><a href="/pipclubtrade#acct.53-tab.list-ticket.4891137">Opened Short</a> | <span class="nowrap">12 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="pipclubtrade" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">pipclubtrade</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_259133_4890880_1753679307" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdc
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,339.07
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679307"><a href="/pipclubtrade#acct.53-tab.list-ticket.4890880">Closed Short</a> | <span class="nowrap">12 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="pipclubtrade" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">pipclubtrade</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_260509_2638447_1753679234" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
XAU/USD
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span> <span class="normal trades_activity__approx">~</span>                3,339.0
</strong> </p> <p class="trades_activity__info trades_activity__info--scaled caption" data-timestamp="1753679234"><a href="javascript:void(0);" class="internal showscaling" data-scale="flexActivity_260509_2638447_1753679234"> <span>Scaled Out</span> </a> | <span class="nowrap">~13 min ago</span> </p> <div class="trades_activity__scaled"> <p class="caption"><a href="/5uph4#acct.04-tab.list-ticket.2638447">Closed Long</a> at 3,339</p><p class="caption"><a href="/5uph4#acct.04-tab.list-ticket.2638448">Closed Long</a> at 3,339</p> </div> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="5uph4" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1327532_3.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">5uPh4</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <div class="trades_activity__scaled"> <div class="return"> <span class="worse">-1.7%</span> </div><div class="return"> <span class="worse">-1.6%</span> </div> </div> <span class="worse">-3.3%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <div class="trades_activity__scaled"> <div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div><div class="pips"> <span class="grey"> </span> <span class="grey">na</span> </div> </div> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_258620_117704662_1753679233" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdm
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,339.15
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679233"><a href="/goldenarm#acct.37-tab.list-ticket.117704662">Closed Short</a> | <span class="nowrap">13 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="goldenarm" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">goldenarm</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.2%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_257215_1577608782_1753679200" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdf
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,339.42
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679200"><a href="/patrickgroup#acct.58-tab.list-ticket.1577608782">Opened Long</a> | <span class="nowrap">14 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="patrickgroup" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1658421_6.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">PatrickGroup</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_247395_31088342_1753679096" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
US /OIL
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                65.342
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679096"><a href="/chayen#acct.19-tab.list-ticket.31088342">Opened Long</a> | <span class="nowrap">15 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="chayen" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1929581_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">chayen</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_245177_29552938_1753679096" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
USO/IL+
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                65.35
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679096"><a href="/virusz#acct.38-tab.list-ticket.29552938">Opened Long</a> | <span class="nowrap">15 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="virusz" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1848815_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">virusz</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_260441_35691112_1753679086" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
XAU/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,339.95
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679086"><a href="/furqan0321#acct.59-tab.list-ticket.35691112">Opened Long</a> | <span class="nowrap">16 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="furqan0321" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">furqan0321</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">-0.3%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_235009_268060520_1753679067" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusdc
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,340.025
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679067"><a href="/rapeepatnae#acct.87-tab.list-ticket.268060520">Opened Long</a> | <span class="nowrap">16 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="rapeepatnae" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">RapeepatNae</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">-0.4%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_248176_108570214_1753679055" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
XAU/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,339.726
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753679055"><a href="/ziaurseu#acct.45-tab.list-ticket.108570214">Opened Long</a> | <span class="nowrap">16 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="ziaurseu" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar380261_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">ziaurseu</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">0.0%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_256819_50203907635_1753678973" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Ethusd_l
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                3,900.39
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753678973"><a href="/sobhanjavadi#acct.43-tab.list-ticket.50203907635">Closed Long</a> | <span class="nowrap">17 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="sobhanjavadi" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2102104_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">sobhanjavadi</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.0%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_260638_3819672115_1753678959" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
XAU/USD
                <span class="trades_activity__direction trades_activity__direction--buy buy">BUY</span>
                                3,339.99
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753678959"><a href="/cutelong#acct.98-tab.list-ticket.3819672115">Opened Long</a> | <span class="nowrap">18 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="cutelong" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">cutelong</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="worse">-1.1%</span> <span class="trades_activity__mtm">MTM</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> <tr id="flexActivity_259041_50203716124_1753678894" class="trades_activity__row"> <td class="trade trades_activity__cell trades_activity__cell--trade"> <p class="trades_activity__overview"> <strong>
Xauusd_l
                <span class="trades_activity__direction trades_activity__direction--sell sell">SELL</span>
                                3,340.05
</strong> </p> <p class="trades_activity__info caption" data-timestamp="1753678894"><a href="/sobhanjavadi#acct.91-tab.list-ticket.50203716124">Closed Long</a> | <span class="nowrap">19 min ago</span></p> </td> <td class="trader trades_activity__cell trades_activity__cell--trader"> <div class="usernamedisplay small     "><a href="sobhanjavadi" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2102104_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">sobhanjavadi</span></a></div> </td> <td class="return trades_activity__cell trades_activity__cell--return"> <span class="better">0.1%</span> </td> <td class="pips trades_activity__cell trades_activity__cell--pips"> <span class="grey">na</span> </td> </tr> </tbody> </table> <div class="foot"> <ul> <li class="more"> <a href="javascript:void(0);" class="flexMore" data-preloadview="1"><span>More</span><span class="loader"></span></a> </li> </ul> </div> </div> </div> </div> </div> <div class="pagearrange__layout-column pagearrange__layout-column--half pagearrange__layout-column--last last half" data-column="1">  <script type="text/javascript">
window.hasInlineAds = true;
</script> <div class="pagearrange__layout-cell" data-cell="0" data-compid="TradeLeaderboard" data-zip-index-offset="0"> <a name="TradeLeaderboard" class="snap"></a> <div class="flexShell"> <div class="flexBox trades_leaderboard" id="flexBox_flex_trades/leaderboard_tradesLeaderboard" data-more="0" data-checkstate="0" data-initcallback="leaderboard" data-updatecallback="leaderboard" data-visiblejs="[]" data-disablejs="[]" data-isdefault="1"> <form action="flex.php" method="post" onsubmit="return Flex.prepareSubmit(this);" data-submit="options"> <input type="hidden" name="_csrf" value="b1476b4155b899debba83b5d37555e44"> <input type="hidden" name="do" value="saveoptions"> <input type="hidden" name="setdefault" value="no"> <input type="hidden" name="ignoreinput" value="no"> <input type="hidden" name="flex[Trades/Leaderboard_tradesLeaderboard][idSuffix]" value=""> <input type="hidden" name="flex[Trades/Leaderboard_tradesLeaderboard][_flexForm_]" value="flexForm"> <input type="hidden" name="flex[Trades/Leaderboard_tradesLeaderboard][modelData]" value="eyJwYV9sYXlvdXRfaWQiOiJ0cmFkZXMiLCJwYV9jb21wb25lbnRfaWQiOiJUcmFkZUxlYWRlcmJvYXJkIiwicGFfY29udHJvbHMiOiJ0cmFkZXN8VHJhZGVMZWFkZXJib2FyZCIsInBhX2luamVjdHJldmVyc2UiOmZhbHNlLCJwYV9oYXJkaW5qZWN0aW9uIjpmYWxzZSwicGFfaW5qZWN0YXQiOmZhbHNlfQ=="> <div class="head"> <ul> <li class="left"><h2><a class="highlight light options flexTitle"><span>July Leaderboard / Live Accounts</span></a></h2></li> <li class="loader"></li> <li class="pagearrange__layoutcontrols layoutcontrols"><div class="pagearrange__controls pagearrange_controls"> <span class="moveDown" title="Move Block Down"></span><span class="moveLeft" title="Move Block Left"></span></div> </li></ul> </div> <div class="options"> <div> <div class="overlay overlay--options trades_leaderboard__flexoptions shell flexoptions"> <div class="overlay__title"> <span class="overlay__icon overlay__icon--options overlay__icon--trades_leaderboardoptions"></span>Leaderboard Options
</div> <div class="overlay__content"> <div class="flexcontrols"> <div class="flexcontrols__row"> <div class="flexcontrols__cell flexcontrols__cell--rankperiod"> <p class="flexcontrols__title">Ranking Period</p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--radio "> <ul class="flexcontrols__list flexcontrols__list--radio"> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="thisweek" id="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]_thisweek" name="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]_thisweek">This Week</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="thismonth" id="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]_thismonth" name="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]" checked="checked" data-isdefault="true"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]_thismonth">This Month</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="thisyear" id="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]_thisyear" name="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Leaderboard_tradesLeaderboard][trades/period]_thisyear">This Year</label> </div> </li> </ul> </div> </div> <div class="flexcontrols__cell flexcontrols__cell--accounttype"> <p class="flexcontrols__title">Brokerage Account Type</p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--radio "> <ul class="flexcontrols__list flexcontrols__list--radio"> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="live" id="flex[Trades/Leaderboard_tradesLeaderboard][trades/accounttype]_live" name="flex[Trades/Leaderboard_tradesLeaderboard][trades/accounttype]" checked="checked" data-isdefault="true"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Leaderboard_tradesLeaderboard][trades/accounttype]_live">Live Accounts</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="demo" id="flex[Trades/Leaderboard_tradesLeaderboard][trades/accounttype]_demo" name="flex[Trades/Leaderboard_tradesLeaderboard][trades/accounttype]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Leaderboard_tradesLeaderboard][trades/accounttype]_demo">Demo Accounts</label> </div> </li> </ul> </div> <p class="darktext">Leaderboard excludes Explorers that are set to private, have equity less than 100, are traded by a commercial user, or are connected to a brokerage account opened after the leaderboard period began.</p> </div> </div> </div> <div class="overlay__pad "></div> </div> <table class="overlay__controls "> <tbody><tr> <td class="overlay__error overlay__error--options overlay__button overlay__button--disabled flexOptionsError" data-touchable=""></td> <td class="overlay__submits flexSubmitButtons"> <input type="submit" class="overlay__button overlay__button--submit button flexOptionsSubmit" name="flexSettings" value="Apply Settings" data-touchable=""> <input type="button" class="overlay__button overlay__button--cancel button flexCancelOptions" value="Cancel" data-touchable=""> </td> <td class="overlay__defaults flexDefaults"></td> </tr> </tbody></table> </div> </div> <div> <div class="overlay overlay--filters trades_leaderboard__flexfilters shell flexfilters"> <div class="overlay__title"> <span class="overlay__icon overlay__icon--filters overlay__icon--trades_leaderboardfilters"></span>Leaderboard Filters
</div> <div class="overlay__content"> <div class="overlay__pad "></div> </div> <table class="overlay__controls "> <tbody><tr> <td class="overlay__message overlay__message--invisible">Saving…</td> <td class="overlay__error overlay__error--filter overlay__button overlay__button--disabled flexFilterError"></td> <td class="overlay__submits flexSubmitButtons"> <input type="submit" class="overlay__button overlay__button--submit button flexFilterSubmit" name="flexFilters" value="Apply Filter" data-touchable=""> <input type="button" class="overlay__button overlay__button--cancel button flexCancelFilters" value="Cancel" data-touchable=""> </td> <td class="overlay__defaults flexDefaults"></td> </tr> </tbody></table> </div> </div> </div> </form> <table class="slidetable__table"> <thead> <tr> <th class="trades_leaderboard__header trades_leaderboard__header--rank rank slidetable__header--fixed" colspan="2"><a class="tooltip__info" data-info="trades_leaderboard_period" data-registered="true"></a>Jul Rank/Explorer</th> <th class="trades_leaderboard__header trades_leaderboard__header--return return"><span class="sort sort--desc"><a class="tooltip__info" data-info="trades_leaderboard_return" data-registered="true"></a>Jul Return</span></th> <th class="trades_leaderboard__header trades_leaderboard__header--trader trader">Trader</th> <th class="trades_leaderboard__header trades_leaderboard__header--chart chart"><a class="tooltip__info" data-info="trades_leaderboard_chart" data-registered="true"></a>Return/Days</th> </tr> </thead> <tbody> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#1</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="robson13#acct.78">Long Term</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">1,485.0%</span><span class="trades_leaderboard__mtm mtm">MTM</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="robson13" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">robson13</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="robson13#acct.78-tab.graph-explorer.240426-yaxis.return-xaxis.days-bars.28" data-account-id="240426" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#2</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="nguyenforex#acct.44">nguyenforex</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">431.4%</span><span class="trades_leaderboard__mtm mtm">MTM</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="nguyenforex" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar716781_2.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">nguyenforex</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="nguyenforex#acct.44-tab.graph-explorer.260366-yaxis.return-xaxis.days-bars.28" data-account-id="260366" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#3</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="shanks303#acct.80">Ere Emmanuel</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">399.3%</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="shanks303" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1500751_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">Shanks303</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="shanks303#acct.80-tab.graph-explorer.260851-yaxis.return-xaxis.days-bars.28" data-account-id="260851" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#4</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="kingdikatmb#acct.02">King_Sandika"Tmb"</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">311.9%</span><span class="trades_leaderboard__mtm mtm">MTM</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="kingdikatmb" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2015708_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">KingDikaTmb</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="kingdikatmb#acct.02-tab.graph-explorer.252462-yaxis.return-xaxis.days-bars.28" data-account-id="252462" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#5</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="alxhubert#acct.71">MR. AH</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">304.7%</span><span class="trades_leaderboard__mtm mtm">MTM</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="alxhubert" title="" class=""><span class="usernamedisplay__avatar avatar usernamedisplay__avatar--noavatar noavatar"></span>&nbsp;<span class="usernamedisplay__username username">Alxhubert</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="alxhubert#acct.71-tab.graph-explorer.258364-yaxis.return-xaxis.days-bars.28" data-account-id="258364" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#6</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="midalia#acct.48">Midalia-Price-Action</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">257.7%</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="midalia" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2177465_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">midalia</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="midalia#acct.48-tab.graph-explorer.260279-yaxis.return-xaxis.days-bars.28" data-account-id="260279" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#7</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="natthaphon13#acct.99">NTP</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">251.2%</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="natthaphon13" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar732344_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">natthaphon13</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="natthaphon13#acct.99-tab.graph-explorer.260898-yaxis.return-xaxis.days-bars.28" data-account-id="260898" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#8</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="dzulmonster#acct.86">IUX test</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">218.7%</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="dzulmonster" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar391270_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">dzulmonster</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="dzulmonster#acct.86-tab.graph-explorer.255071-yaxis.return-xaxis.days-bars.28" data-account-id="255071" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#9</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="yaguto#acct.42">Yagutua</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">179.5%</span><span class="trades_leaderboard__mtm mtm">MTM</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="yaguto" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar2185673_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">Yaguto</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="yaguto#acct.42-tab.graph-explorer.260586-yaxis.return-xaxis.days-bars.28" data-account-id="260586" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> <tr class="trades_leaderboard__row "> <td class="trades_leaderboard__cell trades_leaderboard__cell--rank rank slidetable__cell--fixed"><strong><span>#10</span></strong></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--explorer explorer slidetable__cell--fixed "><a href="hizeiris#acct.30">HizeNewhope</a></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--return return"><div><span class="better">157.1%</span><span class="trades_leaderboard__mtm mtm">MTM</span></div></td> <td class="trades_leaderboard__cell trades_leaderboard__cell--trader trader"> <div class="usernamedisplay small     "><a href="hizeiris" title="" class=""><span class="usernamedisplay__avatar avatar"><img alt="" loading="lazy" src="https://assets.faireconomy.media/nfs/customavatars/thumbs/small/avatar1734237_1.gif" height="16" width="16"></span>&nbsp;<span class="usernamedisplay__username username">hizeiris</span></a></div> </td> <td class="trades_leaderboard__cell trades_leaderboard__cell--chart chart"> <a title="Go to Interactive Graph" class="loaded" href="hizeiris#acct.30-tab.graph-explorer.258715-yaxis.return-xaxis.days-bars.28" data-account-id="258715" data-period="ThisMonth" data-touchable="" style="position: relative; width: 68px; height: 31px;"> <canvas width="68" height="31" style="width: 68px; height: 31px;"></canvas> </a> </td> </tr> </tbody> </table> <div class="foot"> <ul> <li class="more"> <a href="javascript:void(0);" class="flexMore" data-preload-show="no" data-preload="preloadingComplete" data-preloadid="preload_1"><span>More</span><span class="loader"></span></a> </li> </ul> </div> </div> </div> </div> <div class="pagearrange__layout-cell" data-cell="1" data-compid="TradePositions" data-zip-index-offset="0"> <a name="TradePositions" class="snap"></a> <a name="positions" class="anchor snap"></a><div class="flexShell"> <div class="flexBox trades_positions traders trades_positions--traders" id="flexBox_flex_trades/positions_tradesPositions" data-more="0" data-initcallback="trades_positions" data-visiblejs="[]" data-disablejs="[]" data-isdefault="1"> <form action="flex.php" method="post" onsubmit="return Flex.prepareSubmit(this);" data-submit="options"> <input type="hidden" name="_csrf" value="b1476b4155b899debba83b5d37555e44"> <input type="hidden" name="do" value="saveoptions"> <input type="hidden" name="setdefault" value="no"> <input type="hidden" name="ignoreinput" value="no"> <input type="hidden" name="flex[Trades/Positions_tradesPositions][idSuffix]" value=""> <input type="hidden" name="flex[Trades/Positions_tradesPositions][_flexForm_]" value="flexForm"> <input type="hidden" name="flex[Trades/Positions_tradesPositions][modelData]" value="eyJwYV9sYXlvdXRfaWQiOiJ0cmFkZXMiLCJwYV9jb21wb25lbnRfaWQiOiJUcmFkZVBvc2l0aW9ucyIsInBhX2NvbnRyb2xzIjoidHJhZGVzfFRyYWRlUG9zaXRpb25zIiwicGFfaW5qZWN0cmV2ZXJzZSI6ZmFsc2UsInBhX2hhcmRpbmplY3Rpb24iOmZhbHNlLCJwYV9pbmplY3RhdCI6ZmFsc2V9"> <div class="head"> <ul> <li class="left"><h2><a class="highlight light options flexTitle"><span>Positions / Live Accounts</span></a></h2></li> <li class="loader"></li> <li class="pagearrange__layoutcontrols layoutcontrols"><div class="pagearrange__controls pagearrange_controls"> <span class="moveUp" title="Move Block Up"></span><span class="moveDown" title="Move Block Down"></span><span class="moveLeft" title="Move Block Left"></span></div> </li></ul> </div> <div class="options"> <div> <div class="overlay overlay--options trades_positions__flexoptions shell flexoptions"> <div class="overlay__title"> <span class="overlay__icon overlay__icon--options overlay__icon--trades_positionsoptions"></span>Positions Settings
</div> <div class="overlay__content"> <div class="flexcontrols"> <div class="flexcontrols__row"> <div class="flexcontrols__cell flexcontrols__cell--metertype"> <p class="flexcontrols__title">Meter Type</p> <div class="flexcontrols__listcontainer flexcontrols__listcontainer--radio "> <ul class="flexcontrols__list flexcontrols__list--radio"> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="traders" id="flex[Trades/Positions_tradesPositions][trades/positions/type]_traders" name="flex[Trades/Positions_tradesPositions][trades/positions/type]" checked="checked" data-isdefault="true"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Positions_tradesPositions][trades/positions/type]_traders">Traders</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="lots" id="flex[Trades/Positions_tradesPositions][trades/positions/type]_lots" name="flex[Trades/Positions_tradesPositions][trades/positions/type]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Positions_tradesPositions][trades/positions/type]_lots">Lots</label> </div> </li> <li class="flexcontrols__radiolist-item"> <div class="flexcontrols__selector flexcontrols__selector--radio"> <input type="radio" value="dual" id="flex[Trades/Positions_tradesPositions][trades/positions/type]_dual" name="flex[Trades/Positions_tradesPositions][trades/positions/type]"> </div> <div class="flexcontrols__label flexcontrols__label--radio "> <label for="flex[Trades/Positions_tradesPositions][trades/positions/type]_dual">Traders and Lots</label> </div> </li> </ul> </div> </div> </div> </div> <div class="overlay__pad "></div> </div> <table class="overlay__controls "> <tbody><tr> <td class="overlay__error overlay__error--options overlay__button overlay__button--disabled flexOptionsError" data-touchable=""></td> <td class="overlay__submits flexSubmitButtons"> <input type="submit" class="overlay__button overlay__button--submit button flexOptionsSubmit" name="flexSettings" value="Apply Settings" data-touchable=""> <input type="button" class="overlay__button overlay__button--cancel button flexCancelOptions" value="Cancel" data-touchable=""> </td> <td class="overlay__defaults flexDefaults"></td> </tr> </tbody></table> </div> </div> <div> <div class="overlay overlay--filters trades_positions__flexfilters shell flexfilters"> <div class="overlay__title"> <span class="overlay__icon overlay__icon--filters overlay__icon--trades_positionsfilters"></span> </div> <div class="overlay__content"> <div class="overlay__pad "></div> </div> <table class="overlay__controls "> <tbody><tr> <td class="overlay__message overlay__message--invisible">Saving…</td> <td class="overlay__error overlay__error--filter overlay__button overlay__button--disabled flexFilterError"></td> <td class="overlay__submits flexSubmitButtons"> <input type="submit" class="overlay__button overlay__button--submit button flexFilterSubmit" name="flexFilters" value="Apply Filter" data-touchable=""> <input type="button" class="overlay__button overlay__button--cancel button flexCancelFilters" value="Cancel" data-touchable=""> </td> <td class="overlay__defaults flexDefaults"></td> </tr> </tbody></table> </div> </div> </div> </form> <table class="trades_positions__table"> <thead data-sortfield="" data-sortorder="asc"> <tr> <th class="trades_positions__header trades_positions__header--currency currency">Instrument</th> <th class="trades_positions__header trades_positions__header--long long"> <span class="trades_positions__label trades_positions__label--traders traders"> <span class="sort "> <a href="javascript:void(0);" class="internal traders" data-sort="traders_long">Long Traders</a> </span> </span> <span class="trades_positions__label trades_positions__label--lots lots"> <span class="sort "> <a href="javascript:void(0);" class="internal lots" data-sort="long_lots">Long Lots</a> </span> </span> <span class="trades_positions__label trades_positions__label--dual dual"> <strong>Long</strong> <span class="sort "> <a href="javascript:void(0);" class="internal traders" data-sort="traders_long">Traders</a> </span>
/
<span class="sort "> <a href="javascript:void(0);" class="internal lots" data-sort="long_lots">Lots</a> </span> </span> </th> <th class="trades_positions__header trades_positions__header--short short"> <span class="trades_positions__label trades_positions__label--traders traders"> <span class="sort "> <a href="javascript:void(0);" class="internal traders" data-sort="traders_short">Short Traders</a> </span> </span> <span class="trades_positions__label trades_positions__label--lots lots"> <span class="sort "> <a href="javascript:void(0);" class="internal lots" data-sort="short_lots">Short Lots</a> </span> </span> <span class="trades_positions__label trades_positions__label--dual dual"> <strong>Short</strong> <span class="sort "> <a href="javascript:void(0);" class="internal traders" data-sort="traders_short">Traders</a> </span>
/
<span class="sort "> <a href="javascript:void(0);" class="internal lots" data-sort="short_lots">Lots</a> </span> </span> </th> <th class="trades_positions__header trades_positions__header--detail detail">Detail</th> </tr> </thead> </table> <div class="trades_positions__row trades_positions__row--closed entry closed first" data-row="0"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">EUR/USD</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_0]" value="EURUSD"> <app-autocomplete id="currency_suggest_1" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_1-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>39%</strong> 72 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">112 Traders <strong>61%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 39%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 61%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 29%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 71%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>29%</strong> 40.8 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">97.4 Lots <strong>71%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="EURUSD">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>EUR/USD Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div><div class="trades_positions__row trades_positions__row--closed entry closed " data-row="1"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">GBP/USD</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_1]" value="GBPUSD"> <app-autocomplete id="currency_suggest_2" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_2-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>56%</strong> 68 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">54 Traders <strong>44%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 56%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 44%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 66%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 34%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>66%</strong> 44.1 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">22.3 Lots <strong>34%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="GBPUSD">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>GBP/USD Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div><div class="trades_positions__row trades_positions__row--closed entry closed " data-row="2"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">USD/JPY</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_2]" value="USDJPY"> <app-autocomplete id="currency_suggest_3" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_3-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>51%</strong> 38 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">36 Traders <strong>49%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 51%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 49%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 73%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 27%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>73%</strong> 18.5 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">6.7 Lots <strong>27%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="USDJPY">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>USD/JPY Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div><div class="trades_positions__row trades_positions__row--closed entry closed " data-row="3"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">USD/CHF</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_3]" value="USDCHF"> <app-autocomplete id="currency_suggest_4" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_4-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>70%</strong> 38 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">16 Traders <strong>30%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 70%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 30%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 98%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 2%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>98%</strong> 79.3 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">1.6 Lots <strong>2%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="USDCHF">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>USD/CHF Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div><div class="trades_positions__row trades_positions__row--closed entry closed " data-row="4"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">USD/CAD</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_4]" value="USDCAD"> <app-autocomplete id="currency_suggest_5" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_5-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>44%</strong> 26 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">33 Traders <strong>56%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 44%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 56%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 45%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 55%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>45%</strong> 13.9 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">16.6 Lots <strong>55%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="USDCAD">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>USD/CAD Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div><div class="trades_positions__row trades_positions__row--closed entry closed " data-row="5"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">AUD/USD</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_5]" value="AUDUSD"> <app-autocomplete id="currency_suggest_6" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_6-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>51%</strong> 34 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">33 Traders <strong>49%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 51%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 49%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 28%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 72%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>28%</strong> 15.3 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">40.2 Lots <strong>72%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="AUDUSD">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>AUD/USD Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div><div class="trades_positions__row trades_positions__row--closed entry closed " data-row="6"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">NZD/USD</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_6]" value="NZDUSD"> <app-autocomplete id="currency_suggest_7" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_7-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>49%</strong> 23 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">24 Traders <strong>51%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 49%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 51%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 68%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 32%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>68%</strong> 21.6 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">10.3 Lots <strong>32%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="NZDUSD">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>NZD/USD Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div><div class="trades_positions__row trades_positions__row--closed entry closed " data-row="7"> <table class="trades_position"> <tbody> <tr> <td class="trades_position__cell trades_position__cell--currency currency" data-type="position"> <span class="flexExternalOption" data-type="Market/Currency"><div class="currency_suggest "> <form method="get" action=""> <a class="currency" data-touchable="">GBP/JPY</a> <input type="hidden" name="flex[Trades/Positions_tradesPositions][market/currency_7]" value="GBPJPY"> <app-autocomplete id="currency_suggest_8" data-autoselect="true" class="autocomplete"> <input id="currency_suggest_8-input" name="pair" type="text" autofocus="" data-lpignore="true" autocomplete="off" placeholder="Instrument" data-enterhandled="1"> <ul role="listbox" style="display: none;"></ul> </app-autocomplete> <input type="submit" class="button button--pressable" value="Save" data-touchable=""> </form> </div> </span> </td> <td class="trades_position__cell trades_position__cell--chart chart" data-touchable="" title="Open Detail"> <div class="trades_position__chart trades_position__chart--traders traders"> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>51%</strong> 22 Traders</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">21 Traders <strong>49%</strong> </span></li> </ul> <ul class="trades_position__bars bars"> <li style="width: 51%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 49%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> </div> <div class="trades_position__chart trades_position__chart--lots lots"> <ul class="trades_position__bars bars" title="Open Details"> <li style="width: 64%;" class="trades_position__bar trades_position__bar--long long"><span class="bar"></span></li> <li style="width: 36%;" class="trades_position__bar trades_position__bar--short short"><span class="bar"></span></li> </ul> <ul class="trades_position__labels labels"> <li class="trades_position__label trades_position__label--long long"><span class="label"><strong>64%</strong> 11.5 Lots</span></li> <li class="trades_position__label trades_position__label--short short"><span class="label">6.6 Lots <strong>36%</strong> </span></li> </ul> </div> </td> <td class="trades_position__cell trades_position__cell--detail detail"> <a title="Open Detail" href="javascript:void(0);" data-currency="GBPJPY">Open</a> </td> </tr> </tbody> </table> <div class="tradespositiondetail overlay detail nest datagrid"> <div class="overlay__title"><span class="overlay__icon overlay__icon--trades_positiondetails"></span>GBP/JPY Position Detail</div> <div class="overlay__content"> <div class="tradespositiondetail__contents"></div> <div class="overlay__pad"></div> </div> <div class="overlay__controls"> <a href="javascript:void(0);" class="overlay__button" data-touchable=""><span>Exit Detail View</span></a> </div> </div> </div> </div> </div> </div> <div class="pagearrange__layout-cell" data-cell="2" data-compid="TradeAbout" data-zip-index-offset="0"> <a name="TradeAbout" class="snap"></a> <div class="flexBox noflex about trades__datasource"> <div class="head"> <ul> <li class="left noborder nolink"><h2>Data Source: Trade Explorer</h2></li> <li class="pagearrange__layoutcontrols layoutcontrols"><div class="pagearrange__controls pagearrange_controls"> <span class="removeComponent" title="Remove Block From Page"></span></div> </li> </ul> </div> <p class="trades__datasource-paragraph body">The data on this page is sourced from traders using Forex Factory's Trade Explorer, a web-based interface that empowers traders to intelligently analyze their trading performance.</p> <p class="trades__datasource-paragraph body"> <a href="/tradeexplorer"><img src="https://assets.faireconomy.media/images/misc/explorer_sample.png" alt=""></a> <span class="trades__datasource-createinfo"> <a href="/tradeexplorer">Create a Trade Explorer</a>, or learn <span class="more">more</span> in the <a href="/userguide#trade_explorer">user guide</a> and FF <a href="/blog">blog</a>.
</span> </p> </div> </div> <div class="pagearrange__layout-cell pagearrange__layout-inlinead" data-position="5"> <div id="__google-31963ecec056345d1cec349bdd83c7ec"> <script type="text/javascript">
        var slotDisabled = false;

        if (!slotDisabled)
        {
            var slotData = {
                uniqueId: '__google-31963ecec056345d1cec349bdd83c7ec',
                defaultWidth: parseInt('320'),
                defaultHeight: parseInt('250'),
                slot: '/1014366/ff_branding_sidebar',
                sizes: [[320,250],[300,250]],
                mapping: [],
                viewports: ["mobile"]
            };

            if (typeof FF.adSlotManager !== 'undefined' && typeof FF.AdSlot !== 'undefined')
            {
                FF.adSlotManager.register(new FF.AdSlot(slotData));
            }
            else
            {
                window.moneySlots.push(slotData);
            }
        }
        else
        {
            
        }
    </script> </div> </div> <script type="text/javascript">
window.hasInlineAds = true;
</script> </div> </div> </div> <div id="content_bottom_pad" class="full content__bottompad"></div> </section>  </div>  <div class="anchor-banner"> <div id="__google-eb0f83877e106ccf9a150b43b13b5a9f"> <script type="text/javascript">
        var slotDisabled = false;

        if (!slotDisabled)
        {
            var slotData = {
                uniqueId: '__google-eb0f83877e106ccf9a150b43b13b5a9f',
                defaultWidth: parseInt('320'),
                defaultHeight: parseInt('50'),
                slot: '/1014366/ff_branding_anchor',
                sizes: [[320,50],[300,50]],
                mapping: [],
                viewports: ["mobile"]
            };

            if (typeof FF.adSlotManager !== 'undefined' && typeof FF.AdSlot !== 'undefined')
            {
                FF.adSlotManager.register(new FF.AdSlot(slotData));
            }
            else
            {
                window.moneySlots.push(slotData);
            }
        }
        else
        {
            
        }
    </script> </div> </div> <guest-modal><div slot="content" class="modal-prompt modal-prompt--padded"><!----></div></guest-modal> <div class="device"> <div class="device__platform device__platform--mv visible-mv" data-code="mv" data-name="mobile"></div> <div class="device__platform device__platform--tv visible-tv" data-code="tv" data-name="tablet"></div> <div class="device__platform device__platform--dv visible-dv" data-code="dv" data-name="desktop"></div> </div> <script type="text/javascript" src="https://assets.faireconomy.media/clientscript/jquery.min.js"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/vue.min.js?_v=cdabb41c" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/vuex.min.js?_v=e633e0fe" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/app/data_store.js?_v=f6790880" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/ts/dist/common-production.js?_v=255bf35a" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/site.js?_v=59f4dfce" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/color.js?_v=ad71a866" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/global.js?_v=b9291bfd" charset="utf-8"></script><script type="text/javascript" src="https://assets.faireconomy.media/resources/js.min/clientscript/ffsettings.js?_v=6c376e46" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/pagearrange.js?_v=ce552743" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/icw-bridge.js?_v=da1edc8f" charset="utf-8"></script><script type="text/javascript" src="https://assets.faireconomy.media/resources/js.min/clientscript/flex.js?_v=81596e47" charset="utf-8"></script><script type="text/javascript" src="https://assets.faireconomy.media/resources/js.min/clientscript/quicksearch.js?_v=ca25b6b4" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/slide_table.js?_v=58806dfa" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/hidden_options.js?_v=a65c6f74" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/ff_util.js?_v=07db6e1e" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/phrase_editor.js?_v=c70d2ca2" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/web-components/simple-modal.js?_v=1be39faa" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/web-components/layout-selector-modal.js?_v=4b777c53" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/fader.js?_v=4911f066" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/web-components/alert-simple-modal.js?_v=8ec15c19" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/deferred_javascript.js?_v=92327bd8" charset="utf-8"></script><script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/date_formatter.js?_v=3b7dfad8" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/web-components/guest-modal.js?_v=8ea13c60" charset="utf-8"></script> <script type="text/javascript" src="https://resources.faireconomy.media/ts/dist/guest-modal-production.js?_v=6e4d657f" charset="utf-8"></script> <script type="text/javascript">if (typeof document.deferredScripts !== 'undefined') { document.deferredScripts.add({id: 'alerts-12', name: 'alerts', src: 'https://resources.faireconomy.media/ts/dist/alerts-production.js?_v=b5735b35', rules: 'async'}); }</script> <script type="text/javascript">if (typeof document.deferredScripts !== 'undefined') { document.deferredScripts.add({id: 'lottie-13', name: 'lottie', src: 'https://resources.faireconomy.media/js.min/resources/js/lottie.min.js?_v=5acf77bc', rules: 'async'}); }</script> <script type="text/javascript" src="https://resources.faireconomy.media/ts/dist/time-zone-production.js?_v=933b75d3" charset="utf-8"></script> <script type="text/javascript" src="https://assets.faireconomy.media/resources/js.min/clientscript/flex_tradesactivity.js?_v=8f85a124" charset="utf-8"></script> <script type="text/javascript" charset="utf-8">if (typeof flexTradesActivityInstances != 'undefined')
{
flexTradesActivityInstances.is_market_closed = false;

window.flexCallbacks = window.flexCallbacks || {};
window.flexCallbacks['init'] = window.flexCallbacks['init'] || {};
window.flexCallbacks['init'].trade_activity = {
callback: flexTradesActivityInstances.register,
scope: flexTradesActivityInstances
};
}
</script> <script type="text/javascript" charset="utf-8">window.flexCallbacks = window.flexCallbacks || {};
flexCallbacks['init'] = flexCallbacks['init'] || {};
flexCallbacks['init'].leaderboard = {
callback: (flexBox) =>
{
document.deferredScripts.loadByName('explorer')
.then(() => window.ExplorerLeaderboard.getInstance().init(flexBox));
}
};
</script> <script type="text/javascript">if (typeof document.deferredScripts !== 'undefined') { document.deferredScripts.add({id: 'cgs-0', name: 'cgs', src: 'https://resources.faireconomy.media/ts/dist/cgs-production.js?_v=4f475ad0', rules: 'async'}); }</script> <script type="text/javascript">if (typeof document.deferredScripts !== 'undefined') { document.deferredScripts.add({id: 'explorer-0', name: 'explorer', src: 'https://resources.faireconomy.media/ts/dist/explorer-production.js?_v=803d28db', rules: 'async'}); }</script> <script type="text/javascript" src="https://resources.faireconomy.media/js.min/resources/js/flex_traderpositions.js?_v=07bb8e60" charset="utf-8"></script> <script type="text/javascript" charset="utf-8">window.flexCallbacks = window.flexCallbacks || {};
window.flexCallbacks['init'] = window.flexCallbacks['init'] || {};
window.flexCallbacks['init'].trades_positions = {
callback: flexPositionInstances.register,
scope: flexPositionInstances
};
</script> <script type="text/javascript" src="https://assets.faireconomy.media/resources/js.min/clientscript/tooltip.js?_v=5339d999" charset="utf-8"></script> <script type="text/javascript" src="https://assets.faireconomy.media/resources/js.min/clientscript/flex_livedata.js?_v=cf522f70" charset="utf-8"></script> <script type="text/javascript" src="https://assets.faireconomy.media/resources/js.min/clientscript/flex_preload.js?_v=d6650d2e" charset="utf-8"></script> <script type="text/javascript" charset="utf-8">document.addEventListener('DOMContentLoaded', () => FFTooltipGen.register_list());
</script> <script type="text/javascript">

onBodyComplete.execute();
</script> <footer class="footer footer__ffa footer--has-topbar"> <div class="footer__topbar" id="footer-top-bar"> <a href="#" class="top scroll-top default"> <span class="icon icon--top-of-page"></span> <span>Top of Page</span> </a> <a rel="nofollow" href="/trades?reset=1" title="Return This Page to Default Settings" class="defaultpage"> <span class="icon icon--page-default-link"></span> <span>Default Page</span> </a> </div> <div class="footer__branding"> <div id="__google-ce5c5157b35084b59048267c81ffecb7" data-google-query-id="COb3mKPn3o4DFbKaZgIdvJYSKA">  <div id="google_ads_iframe_/1014366/ff_branding_footer_0__container__" style="border: 0pt none;"><iframe id="google_ads_iframe_/1014366/ff_branding_footer_0" name="google_ads_iframe_/1014366/ff_branding_footer_0" title="3rd party ad content" width="970" height="250" scrolling="no" marginwidth="0" marginheight="0" frameborder="0" aria-label="Advertisement" tabindex="0" allow="private-state-token-redemption;attribution-reporting" data-load-complete="true" data-google-container-id="2" style="border: 0px; vertical-align: bottom;"></iframe></div></div> </div> <div class="footer__bottom"> <div class="footer__left"> <div class="footer__logo-social"> <a href="https://www.forexfactory.com" data-touchable="" title="Homepage"> <svg class="svg svg--footer-logo-social-ff" width="107" height="89"><use href="resources/svg/icons.svg?c70c3c61#footer-logo-social-ff"></use></svg> </a> <ul class="footer__social visible-mv"> <li> <a href="https://www.facebook.com/forexfactory/" data-touchable="" title="Facebook"> <img class="svg-img svg-img--footer-social-facebook" width="24" height="24" src="resources/svg/images/footer/social-facebook.svg?12d94219"> </a> </li> <li> <a href="https://twitter.com/ForexFactory" data-touchable="" title="X"> <img class="svg-img svg-img--footer-social-x" width="24" height="24" src="resources/svg/images/footer/social-x.svg?d3d2e864"> </a> </li> </ul> </div> <div class="footer__link-collection"> <div class="footer__links footer__links--about"> <strong>About FF</strong> <ul> <li><a href="/mission">Mission</a></li> <li><a href="/products">Products</a></li> <li><a href="/userguide">User Guide</a></li> <li><a href="/mediakit">Media Kit</a></li> <li><a href="/blog">Blog</a></li> <li><a href="/contact">Contact</a></li> </ul> </div> <div class="footer__links footer__links--products"> <strong>FF Products</strong> <ul> <li><a href="forums">Forums</a></li> <li><a href="trades">Trades</a></li> <li><a href="calendar">Calendar</a></li> <li><a href="news">News</a></li> <li><a href="market">Market</a></li> <li><a href="brokers">Brokers</a></li> <li><a href="/tradeexplorer">Trade Explorer</a></li> </ul> </div> <div class="footer__links footer__links--website"> <strong>FF Website</strong> <ul> <li><a href="/">Homepage</a></li> <li><a href="/search">Search</a></li> <li><a href="/traders">Traders</a></li> <li><a href="/contact?subject=technical">Report a Problem</a></li> </ul> </div> <div class="footer__links footer__links--social visible-tv visible-dv"> <strong>Follow FF</strong> <ul> <li> <a href="https://www.facebook.com/forexfactory/" target="_blank" rel="noreferrer" data-touchable="" title="Facebook"> <img class="svg-img svg-img--footer-social-facebook" width="24" height="24" src="resources/svg/images/footer/social-facebook.svg?12d94219"> </a> </li> <li> <a href="https://twitter.com/ForexFactory" target="_blank" rel="noreferrer" data-touchable="" title="X"> <img class="svg-img svg-img--footer-social-x" width="24" height="24" src="resources/svg/images/footer/social-x.svg?d3d2e864"> </a> </li> </ul> </div> </div> </div> <div class="footer__right"> <div class="footer__view-page-on"> <p>FF Sister Sites:</p> <strong class="hidden"></strong> <ul> <li> <a href="https://www.metalsmine.com" data-touchable="" data-site-name="Metals Mine"> <svg class="svg svg--footer-mm-sm" width="34" height="28"><use href="resources/svg/icons.svg?c70c3c61#footer-mm-sm"></use></svg> </a> </li> <li> <a href="https://www.energyexch.com" data-touchable="" data-site-name="Energy EXCH"> <svg class="svg svg--footer-ee-sm" width="34" height="28"><use href="resources/svg/icons.svg?c70c3c61#footer-ee-sm"></use></svg> </a> </li> <li> <a href="https://www.cryptocraft.com" data-touchable="" data-site-name="Crypto Craft"> <svg class="svg svg--footer-cc-sm" width="35" height="28"><use href="resources/svg/icons.svg?c70c3c61#footer-cc-sm"></use></svg> </a> </li> </ul> </div> <div class="footer__legal"> <div> <p>Forex Factory® is a brand of <a href="https://www.faireconomy.com/" target="_blank">Fair Economy, Inc.</a></p> <p><a href="/notices#tos">Terms of Service</a> / <a href="/notices#copyright">©2025</a></p> </div> <a href="https://www.faireconomy.com/" target="_blank" data-touchable="" aria-label="Fair Economy"> <svg class="svg svg--footer-fei" width="36" height="36"><use href="resources/svg/icons.svg?c70c3c61#footer-fei"></use></svg> </a> </div> </div> </div> </footer> </div>  </div>  <script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-3311429-1"></script> <script>
window.dataLayer = window.dataLayer || [];
let layoutEl = document.querySelector('#ls-layout');
function gtag(){dataLayer.push(arguments);}

gtag('js', new Date());


gtag('set', 'custom_map', {
'dimension1': 'logged_in',
'dimension2': 'mirs_rank',
'dimension3': 'timezone',
'dimension4': 'page_depth',
});

gtag('config', 'G-QFGG4THJR2', Object.assign({
'content_group': 'Trades', // https://developers.google.com/analytics/devguides/collection/ga4/reference/config#content_group
'user_properties': {
'logged_in': '0',
'mirs_rank': '',
'timezone': 'Asia/Hong_Kong',
'page_depth': '0',
},
'layout_name': layoutEl !== null && typeof layoutEl !== 'undefined' ? layoutEl.dataset.name : '',
'layout_collection': layoutEl !== null && typeof layoutEl !== 'undefined' ? layoutEl.dataset.collection : '',
}, window.customGaPageViewParameters || {}));

gtag('config', 'UA-3311429-1', {
'custom_map': {
'dimension1': 'logged_in',
'dimension2': 'mirs_rank',
'dimension3': 'timezone',
            'dimension4': 'page_depth',
}
});

gtag('event', 'user_dimension', {
'logged_in': '0',
'mirs_rank': '',
'timezone': 'Asia/Hong_Kong',
        'page_depth': '0',
'non_interaction': true, // Interactive events jack up the bounce rate https://support.google.com/analytics/answer/1033068#NonInteractionEvents
});
</script> <script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'9661ede68f6b2416',t:'MTc1MzY4MDA0Ny4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;"></iframe> 
<div class="tooltip" id="tooltip1753680048487" style="display: none; z-index: 9999; top: 97px; left: 873.078px;"><div class="tooltip__arrow tooltip__arrow--top arrow top" style="left: 9px; visibility: visible;"></div><table style="margin-left: 0px;"><tbody><tr><td style="width: 282px;"><div class="content"><span class="math">= trade <i>Profit</i> / <i>Balance</i> at trade open</span><br><br><i>Return</i> is the <i>Profit</i> of the trade compared to the <i>Balance</i> when the trade was opened. An ‘MTM’ designation indicates the trade is currently open and the figure is marked-to-market.<br><br><i>Profit</i> is the money gain on the trade.<br><br><i>Balance</i> is the total value of the brokerage account, ignoring open trades.</div></td></tr></tbody></table><div class="tooltip__arrow tooltip__arrow--bottom arrow bottom" style="visibility: hidden;"></div></div><iframe id="_hjSafeContext_54304929" title="_hjSafeContext" tabindex="-1" aria-hidden="true" src="about:blank" style="display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;"></iframe><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><iframe name="google_ads_top_frame" id="google_ads_top_frame" style="display: none; position: fixed; left: -999px; top: -999px; width: 0px; height: 0px;"></iframe><iframe src="https://ep2.adtrafficquality.google/sodar/sodar2/237/runner.html" width="0" height="0" style="display: none;"></iframe><iframe src="https://www.google.com/recaptcha/api2/aframe" width="0" height="0" style="display: none;"></iframe></body><iframe name="goog_topics_frame" src="https://securepubads.g.doubleclick.net/static/topics/topics_frame.html" style="display: none;"></iframe></html>