#!/usr/bin/env python3
"""
增强版监控面板 - 付费信号专用
"""

from flask import Flask, render_template, jsonify
import json
import os
from datetime import datetime
from pathlib import Path

app = Flask(__name__)

@app.route('/')
def dashboard():
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>付费信号监控面板</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .card { background: rgba(255,255,255,0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; backdrop-filter: blur(10px); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric { text-align: center; }
        .metric-value { font-size: 2.5em; font-weight: bold; color: #00ff88; text-shadow: 0 0 10px rgba(0,255,136,0.5); }
        .metric-label { color: #ccc; margin-top: 5px; }
        .status-premium { color: #ffd700; font-weight: bold; }
        .signal-item { background: rgba(255,255,255,0.05); padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #00ff88; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.1); }
        th { background: rgba(255,255,255,0.1); }
        .profit-positive { color: #00ff88; }
        .profit-negative { color: #ff4757; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 付费信号智能监控面板</h1>
            <p class="status-premium">高质量付费信号 · 实时监控</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>📊 账户状态</h3>
                <div class="metric">
                    <div class="metric-value" id="balance">--</div>
                    <div class="metric-label">账户余额</div>
                </div>
            </div>
            
            <div class="card">
                <h3>💰 今日收益</h3>
                <div class="metric">
                    <div class="metric-value" id="daily-pnl">--</div>
                    <div class="metric-label">当日盈亏</div>
                </div>
            </div>
            
            <div class="card">
                <h3>🎯 信号质量</h3>
                <div class="metric">
                    <div class="metric-value" id="signal-quality">--</div>
                    <div class="metric-label">平均置信度</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>🔥 最新付费信号</h3>
            <div id="premium-signals">
                <div class="signal-item">
                    <strong>EURUSD BUY</strong> @ 1.0850
                    <br>来源: VIP外汇信号 | 置信度: 85%
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>📈 当前持仓</h3>
            <table id="positions-table">
                <thead>
                    <tr><th>品种</th><th>方向</th><th>手数</th><th>价格</th><th>盈亏</th></tr>
                </thead>
                <tbody id="positions-body">
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        function updateData() {
            // 更新账户信息
            fetch('/api/premium_account')
                .then(response => response.json())
                .then(data => {
                    if (data.Balance) {
                        document.getElementById('balance').textContent = '$' + parseFloat(data.Balance).toFixed(2);
                    }
                    if (data['Today PnL']) {
                        const pnl = parseFloat(data['Today PnL']);
                        document.getElementById('daily-pnl').textContent = '$' + pnl.toFixed(2);
                        document.getElementById('daily-pnl').className = 'metric-value ' + (pnl >= 0 ? 'profit-positive' : 'profit-negative');
                    }
                });
            
            // 更新信号信息
            fetch('/api/premium_signals')
                .then(response => response.json())
                .then(signals => {
                    const container = document.getElementById('premium-signals');
                    container.innerHTML = '';
                    
                    signals.slice(-5).forEach(signal => {
                        const div = document.createElement('div');
                        div.className = 'signal-item';
                        div.innerHTML = `
                            <strong>${signal.symbol} ${signal.action}</strong> @ ${signal.entry_price || 'Market'}
                            <br>来源: ${signal.source} | 置信度: ${Math.round(signal.confidence * 100)}%
                        `;
                        container.appendChild(div);
                    });
                    
                    // 更新信号质量
                    if (signals.length > 0) {
                        const avgConfidence = signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length;
                        document.getElementById('signal-quality').textContent = Math.round(avgConfidence * 100) + '%';
                    }
                });
        }
        
        // 每5秒更新一次数据
        setInterval(updateData, 5000);
        updateData();
    </script>
</body>
</html>
    """

@app.route('/api/premium_account')
def get_premium_account():
    """获取付费账户信息"""
    try:
        account_file = Path("MQL4/Files/premium_account_info.txt")
        if account_file.exists():
            with open(account_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            account_data = {}
            for line in lines:
                if ':' in line:
                    key, value = line.strip().split(':', 1)
                    account_data[key.strip()] = value.strip()
            
            return jsonify(account_data)
        else:
            return jsonify({'error': '账户信息文件不存在'})
            
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/premium_signals')
def get_premium_signals():
    """获取付费信号"""
    try:
        signals_file = Path("logs/premium_signals.json")
        if signals_file.exists():
            signals = []
            with open(signals_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        signal = json.loads(line.strip())
                        signals.append(signal)
                    except:
                        continue
            
            return jsonify(signals[-20:])  # 返回最近20个信号
        else:
            return jsonify([])
            
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    print("🌐 启动付费信号监控面板...")
    print("访问地址: http://localhost:8080")
    app.run(host='localhost', port=8080, debug=False)
