#!/usr/bin/env python3
"""
付费信号捕获系统启动器
绕过API限制，直接捕获付费渠道的高质量交易信号
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path


def check_requirements():
    """检查系统要求"""
    print("🔍 检查付费信号捕获系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'requests', 'beautifulsoup4', 'selenium', 
        'undetected-chromedriver', 'asyncio', 'flask'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'undetected-chromedriver':
                import undetected_chromedriver
            elif package == 'beautifulsoup4':
                import bs4
            else:
                __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n📦 正在安装缺失的包...")
        try:
            for package in missing_packages:
                print(f"安装 {package}...")
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              check=True, capture_output=True)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {e}")
            print("请手动安装: pip install requests beautifulsoup4 selenium undetected-chromedriver flask")
            return False
    
    # 检查Chrome浏览器
    try:
        import undetected_chromedriver as uc
        driver = uc.Chrome(headless=True)
        driver.quit()
        print("✅ Chrome浏览器检查通过")
    except Exception as e:
        print(f"⚠️  Chrome浏览器检查失败: {e}")
        print("请确保已安装Chrome浏览器")
    
    return True


def create_premium_config():
    """创建付费信号配置"""
    print("🔧 创建付费信号配置...")
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "premium_config.json"
    
    if config_file.exists():
        print("✅ 配置文件已存在")
        return True
    
    # 创建示例配置
    config = {
        "premium_sources": {
            "telegram_premium": {
                "enabled": True,
                "channels": [
                    {
                        "name": "VIP外汇信号",
                        "url": "https://t.me/s/vip_forex_signals",
                        "login_required": False,
                        "weight": 0.8,
                        "capture_method": "web_scraping",
                        "enabled": True
                    },
                    {
                        "name": "专业交易室",
                        "url": "https://t.me/premium_trading_room",
                        "login_required": True,
                        "phone": "请填入你的手机号",
                        "weight": 0.9,
                        "capture_method": "selenium",
                        "enabled": False
                    }
                ]
            },
            
            "myfxbook_premium": {
                "enabled": True,
                "traders": [
                    {
                        "name": "顶级交易师1",
                        "url": "https://www.myfxbook.com/members/trader123",
                        "login_required": True,
                        "username": "请填入用户名",
                        "password": "请填入密码",
                        "weight": 0.85,
                        "enabled": False
                    }
                ]
            },
            
            "discord_premium": {
                "enabled": False,
                "servers": [
                    {
                        "name": "专业交易Discord",
                        "invite_link": "https://discord.gg/xxxxx",
                        "channel_id": "signal-channel",
                        "token": "请填入Discord Token",
                        "weight": 0.7,
                        "enabled": False
                    }
                ]
            }
        },
        
        "capture_settings": {
            "check_interval": 30,
            "max_retries": 3,
            "timeout": 30,
            "use_proxy": False,
            "proxy_list": [],
            "anti_detection": True,
            "headless": True
        },
        
        "signal_filtering": {
            "min_confidence": 0.6,
            "required_fields": ["symbol", "action"],
            "allowed_symbols": ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", "USDCAD", "NZDUSD", "XAUUSD", "XAGUSD"],
            "max_signal_age_minutes": 10
        }
    }
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已创建: {config_file}")
    print("⚠️  请编辑配置文件，填入你的付费渠道信息")
    
    return True


def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🚀 付费信号捕获系统 - 绕过API限制")
    print("="*60)
    print("1. 🕷️  启动付费信号捕获器")
    print("2. 🌐 启动增强版监控面板")
    print("3. 🔄 同时启动捕获器和面板")
    print("4. ⚙️  配置付费信号源")
    print("5. 📊 查看捕获统计")
    print("6. 🧪 测试信号源连接")
    print("7. 📋 查看使用说明")
    print("8. 🔧 安装MT4增强版EA")
    print("0. 🚪 退出")
    print("="*60)


def start_premium_capture():
    """启动付费信号捕获器"""
    print("🕷️ 启动付费信号捕获器...")
    
    if not Path("premium_signal_capture.py").exists():
        print("❌ 付费信号捕获器文件不存在")
        return
    
    try:
        print("📡 开始捕获付费信号...")
        print("💡 提示: 按 Ctrl+C 停止捕获")
        print("🎯 正在监控高质量付费信号源...")
        subprocess.run([sys.executable, "premium_signal_capture.py"])
    except KeyboardInterrupt:
        print("\n⏹️  付费信号捕获器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def start_premium_dashboard():
    """启动增强版监控面板"""
    print("🌐 启动增强版监控面板...")
    
    # 创建增强版面板
    create_premium_dashboard()
    
    try:
        print("🖥️  启动增强版Web监控面板...")
        print("🌐 访问地址: http://localhost:8080")
        print("💡 提示: 按 Ctrl+C 停止面板")
        subprocess.run([sys.executable, "premium_dashboard.py"])
    except KeyboardInterrupt:
        print("\n⏹️  监控面板已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def create_premium_dashboard():
    """创建增强版监控面板"""
    dashboard_code = '''#!/usr/bin/env python3
"""
增强版监控面板 - 付费信号专用
"""

from flask import Flask, render_template, jsonify
import json
import os
from datetime import datetime
from pathlib import Path

app = Flask(__name__)

@app.route('/')
def dashboard():
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>付费信号监控面板</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .card { background: rgba(255,255,255,0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; backdrop-filter: blur(10px); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric { text-align: center; }
        .metric-value { font-size: 2.5em; font-weight: bold; color: #00ff88; text-shadow: 0 0 10px rgba(0,255,136,0.5); }
        .metric-label { color: #ccc; margin-top: 5px; }
        .status-premium { color: #ffd700; font-weight: bold; }
        .signal-item { background: rgba(255,255,255,0.05); padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #00ff88; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.1); }
        th { background: rgba(255,255,255,0.1); }
        .profit-positive { color: #00ff88; }
        .profit-negative { color: #ff4757; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 付费信号智能监控面板</h1>
            <p class="status-premium">高质量付费信号 · 实时监控</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>📊 账户状态</h3>
                <div class="metric">
                    <div class="metric-value" id="balance">--</div>
                    <div class="metric-label">账户余额</div>
                </div>
            </div>
            
            <div class="card">
                <h3>💰 今日收益</h3>
                <div class="metric">
                    <div class="metric-value" id="daily-pnl">--</div>
                    <div class="metric-label">当日盈亏</div>
                </div>
            </div>
            
            <div class="card">
                <h3>🎯 信号质量</h3>
                <div class="metric">
                    <div class="metric-value" id="signal-quality">--</div>
                    <div class="metric-label">平均置信度</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>🔥 最新付费信号</h3>
            <div id="premium-signals">
                <div class="signal-item">
                    <strong>EURUSD BUY</strong> @ 1.0850
                    <br>来源: VIP外汇信号 | 置信度: 85%
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>📈 当前持仓</h3>
            <table id="positions-table">
                <thead>
                    <tr><th>品种</th><th>方向</th><th>手数</th><th>价格</th><th>盈亏</th></tr>
                </thead>
                <tbody id="positions-body">
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        function updateData() {
            // 更新账户信息
            fetch('/api/premium_account')
                .then(response => response.json())
                .then(data => {
                    if (data.Balance) {
                        document.getElementById('balance').textContent = '$' + parseFloat(data.Balance).toFixed(2);
                    }
                    if (data['Today PnL']) {
                        const pnl = parseFloat(data['Today PnL']);
                        document.getElementById('daily-pnl').textContent = '$' + pnl.toFixed(2);
                        document.getElementById('daily-pnl').className = 'metric-value ' + (pnl >= 0 ? 'profit-positive' : 'profit-negative');
                    }
                });
            
            // 更新信号信息
            fetch('/api/premium_signals')
                .then(response => response.json())
                .then(signals => {
                    const container = document.getElementById('premium-signals');
                    container.innerHTML = '';
                    
                    signals.slice(-5).forEach(signal => {
                        const div = document.createElement('div');
                        div.className = 'signal-item';
                        div.innerHTML = `
                            <strong>${signal.symbol} ${signal.action}</strong> @ ${signal.entry_price || 'Market'}
                            <br>来源: ${signal.source} | 置信度: ${Math.round(signal.confidence * 100)}%
                        `;
                        container.appendChild(div);
                    });
                    
                    // 更新信号质量
                    if (signals.length > 0) {
                        const avgConfidence = signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length;
                        document.getElementById('signal-quality').textContent = Math.round(avgConfidence * 100) + '%';
                    }
                });
        }
        
        // 每5秒更新一次数据
        setInterval(updateData, 5000);
        updateData();
    </script>
</body>
</html>
    """

@app.route('/api/premium_account')
def get_premium_account():
    """获取付费账户信息"""
    try:
        account_file = Path("MQL4/Files/premium_account_info.txt")
        if account_file.exists():
            with open(account_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            account_data = {}
            for line in lines:
                if ':' in line:
                    key, value = line.strip().split(':', 1)
                    account_data[key.strip()] = value.strip()
            
            return jsonify(account_data)
        else:
            return jsonify({'error': '账户信息文件不存在'})
            
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/premium_signals')
def get_premium_signals():
    """获取付费信号"""
    try:
        signals_file = Path("logs/premium_signals.json")
        if signals_file.exists():
            signals = []
            with open(signals_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        signal = json.loads(line.strip())
                        signals.append(signal)
                    except:
                        continue
            
            return jsonify(signals[-20:])  # 返回最近20个信号
        else:
            return jsonify([])
            
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    Path("MQL4/Files").mkdir(parents=True, exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    print("🌐 启动付费信号监控面板...")
    print("访问地址: http://localhost:8080")
    app.run(host='localhost', port=8080, debug=False)
'''
    
    with open("premium_dashboard.py", "w", encoding="utf-8") as f:
        f.write(dashboard_code)


def configure_premium_sources():
    """配置付费信号源"""
    print("⚙️  配置付费信号源...")
    
    config_file = Path("config/premium_config.json")
    if not config_file.exists():
        create_premium_config()
    
    print("\n📋 配置指南:")
    print("1. Telegram付费频道:")
    print("   - 公开频道: 直接填入频道链接")
    print("   - 私有频道: 需要手机号登录")
    
    print("\n2. Myfxbook付费交易师:")
    print("   - 需要Myfxbook账户用户名和密码")
    print("   - 系统会自动登录并获取交易记录")
    
    print("\n3. Discord付费服务器:")
    print("   - 需要Discord Token")
    print("   - 填入服务器邀请链接和频道ID")
    
    print(f"\n📝 请编辑配置文件: {config_file}")
    
    # 询问是否现在编辑
    choice = input("\n是否现在打开配置文件编辑? (y/n): ").lower()
    if choice == 'y':
        try:
            if os.name == 'nt':  # Windows
                os.startfile(config_file)
            else:  # Linux/Mac
                os.system(f"nano {config_file}")
        except Exception as e:
            print(f"无法打开文件: {e}")
            print(f"请手动编辑: {config_file}")


def show_capture_stats():
    """显示捕获统计"""
    print("📊 付费信号捕获统计...")
    
    # 检查信号文件
    signals_file = Path("logs/premium_signals.json")
    if signals_file.exists():
        try:
            signals = []
            with open(signals_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        signal = json.loads(line.strip())
                        signals.append(signal)
                    except:
                        continue
            
            print(f"\n📈 统计信息:")
            print(f"总信号数: {len(signals)}")
            
            if signals:
                # 按来源统计
                sources = {}
                for signal in signals:
                    source = signal.get('source', 'Unknown')
                    sources[source] = sources.get(source, 0) + 1
                
                print("\n📊 信号来源分布:")
                for source, count in sources.items():
                    print(f"  {source}: {count} 个信号")
                
                # 平均置信度
                avg_confidence = sum(s.get('confidence', 0) for s in signals) / len(signals)
                print(f"\n🎯 平均信号质量: {avg_confidence*100:.1f}%")
                
                # 最近信号
                print(f"\n🔥 最近5个信号:")
                for signal in signals[-5:]:
                    print(f"  {signal.get('timestamp', '')[:19]} - {signal.get('symbol')} {signal.get('action')} (置信度: {signal.get('confidence', 0)*100:.0f}%)")
            
        except Exception as e:
            print(f"❌ 读取统计失败: {e}")
    else:
        print("❌ 暂无捕获数据")


def test_signal_sources():
    """测试信号源连接"""
    print("🧪 测试付费信号源连接...")
    
    config_file = Path("config/premium_config.json")
    if not config_file.exists():
        print("❌ 配置文件不存在，请先配置")
        return
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 测试Telegram频道
        telegram_channels = config.get('premium_sources', {}).get('telegram_premium', {}).get('channels', [])
        print(f"\n📱 测试 {len(telegram_channels)} 个Telegram频道...")
        
        for channel in telegram_channels:
            if channel.get('enabled', True):
                print(f"  测试: {channel['name']} - ", end="")
                try:
                    import requests
                    response = requests.get(channel['url'], timeout=10)
                    if response.status_code == 200:
                        print("✅ 连接成功")
                    else:
                        print(f"❌ HTTP {response.status_code}")
                except Exception as e:
                    print(f"❌ 连接失败: {e}")
        
        print("\n✅ 连接测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def show_instructions():
    """显示使用说明"""
    print("\n📋 付费信号捕获系统使用说明")
    print("="*50)
    
    print("\n🎯 系统特点:")
    print("✅ 绕过API限制，直接捕获付费信号")
    print("✅ 支持Telegram、Myfxbook、Discord等平台")
    print("✅ 智能反检测，模拟真实用户行为")
    print("✅ 高质量信号过滤和置信度评估")
    print("✅ 增强版MT4 EA，智能风险管理")
    
    print("\n📋 使用步骤:")
    print("1. 配置付费信号源 (选项4)")
    print("2. 测试信号源连接 (选项6)")
    print("3. 安装增强版MT4 EA (选项8)")
    print("4. 启动信号捕获器 (选项1)")
    print("5. 启动监控面板 (选项2)")
    print("6. 在MT4中启动EA开始跟单")
    
    print("\n⚠️  重要提醒:")
    print("- 付费信号质量更高，但仍需谨慎")
    print("- 建议先在模拟账户测试")
    print("- 定期检查系统运行状态")
    print("- 遵守各平台的使用条款")


def install_premium_ea():
    """安装增强版MT4 EA"""
    print("🔧 安装增强版MT4 EA...")
    
    ea_file = Path("MQL4/Experts/PremiumCopyTrader.mq4")
    if ea_file.exists():
        print("✅ 增强版EA文件已存在")
        print(f"📁 文件位置: {ea_file.absolute()}")
        
        print("\n📋 安装步骤:")
        print("1. 将 PremiumCopyTrader.mq4 复制到 MT4的 MQL4/Experts 目录")
        print("2. 在MT4中按F4打开MetaEditor")
        print("3. 编译EA文件")
        print("4. 将EA拖拽到图表上")
        print("5. 设置参数并启用自动交易")
        
        print("\n⚙️  推荐参数设置:")
        print("- BaseLotSize: 0.01 (基础手数)")
        print("- RiskPerTrade: 2.0 (单笔风险2%)")
        print("- MaxDailyRisk: 5.0 (每日最大风险5%)")
        print("- MinConfidence: 0.6 (最小置信度60%)")
        print("- EnableRiskManagement: true (启用风险管理)")
        
    else:
        print("❌ EA文件不存在，请检查文件")


def main():
    """主函数"""
    print("🚀 付费信号捕获系统")
    print("绕过API限制，直接获取高质量付费信号")
    
    # 检查系统要求
    if not check_requirements():
        print("❌ 系统要求检查失败")
        return
    
    # 创建必要目录
    for directory in ["config", "logs", "MQL4/Files", "MQL4/Experts"]:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # 创建配置文件
    create_premium_config()
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-8): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                start_premium_capture()
            elif choice == '2':
                start_premium_dashboard()
            elif choice == '3':
                print("🔄 同时启动捕获器和监控面板...")
                # 这里可以实现同时启动的逻辑
                print("请分别在两个终端中运行选项1和选项2")
            elif choice == '4':
                configure_premium_sources()
            elif choice == '5':
                show_capture_stats()
            elif choice == '6':
                test_signal_sources()
            elif choice == '7':
                show_instructions()
            elif choice == '8':
                install_premium_ea()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        if choice != '0':
            input("\n按回车键继续...")


if __name__ == "__main__":
    main()
