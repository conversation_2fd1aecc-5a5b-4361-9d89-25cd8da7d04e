"""
Telegram用户机器人 - 免费监控Telegram频道信号
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from telethon import TelegramClient, events
from telethon.tl.types import Message


class TelegramUserBot:
    """Telegram用户机器人"""
    
    def __init__(self, telegram_config: Dict, channels_config: List[Dict]):
        self.telegram_config = telegram_config
        self.channels_config = channels_config
        self.logger = logging.getLogger(__name__)
        
        self.client = None
        self.is_running = False
        self.signal_queue = asyncio.Queue()
        self.last_message_ids = {}
        
        # 信号关键词
        self.signal_keywords = {
            'buy': ['buy', 'long', 'bull', '买入', '做多', '看涨', 'call'],
            'sell': ['sell', 'short', 'bear', '卖出', '做空', '看跌', 'put'],
            'symbols': [
                'eurusd', 'gbpusd', 'usdjpy', 'usdchf', 'audusd', 'usdcad', 'nzdusd',
                'eurjpy', 'gbpjpy', 'eurgbp', 'xauusd', 'xagusd', 'gold', 'silver',
                '欧美', '镑美', '美日', '美瑞', '澳美', '美加', '纽美', '黄金', '白银'
            ]
        }
    
    async def initialize(self):
        """初始化Telegram客户端"""
        try:
            api_id = self.telegram_config['api_id']
            api_hash = self.telegram_config['api_hash']
            session_name = self.telegram_config.get('session_name', 'trading_session')
            
            # 创建客户端
            self.client = TelegramClient(session_name, api_id, api_hash)
            
            # 启动客户端
            await self.client.start(phone=self.telegram_config.get('phone_number'))
            
            # 设置消息监听器
            await self.setup_message_handlers()
            
            self.is_running = True
            self.logger.info("✅ Telegram用户机器人初始化完成")
            
            # 显示监控的频道
            for channel in self.channels_config:
                if channel.get('enabled', True):
                    self.logger.info(f"📱 监控频道: {channel['name']} ({channel['channel_id']})")
            
        except Exception as e:
            self.logger.error(f"❌ Telegram用户机器人初始化失败: {e}")
            raise
    
    async def setup_message_handlers(self):
        """设置消息处理器"""
        try:
            # 获取要监控的频道
            channel_entities = []
            for channel_config in self.channels_config:
                if channel_config.get('enabled', True):
                    try:
                        entity = await self.client.get_entity(channel_config['channel_id'])
                        channel_entities.append(entity)
                        self.logger.info(f"✅ 成功连接频道: {channel_config['name']}")
                    except Exception as e:
                        self.logger.error(f"❌ 连接频道失败 {channel_config['channel_id']}: {e}")
            
            if not channel_entities:
                self.logger.warning("⚠️ 没有可用的频道")
                return
            
            # 设置新消息监听器
            @self.client.on(events.NewMessage(chats=channel_entities))
            async def handle_new_message(event):
                await self.process_message(event)
            
            self.logger.info(f"✅ 消息监听器已设置，监控 {len(channel_entities)} 个频道")
            
        except Exception as e:
            self.logger.error(f"设置消息处理器错误: {e}")
    
    async def process_message(self, event):
        """处理新消息"""
        try:
            message = event.message
            
            # 获取频道信息
            channel_id = None
            channel_config = None
            
            if hasattr(message.peer_id, 'channel_id'):
                channel_id = message.peer_id.channel_id
                
                # 查找对应的频道配置
                for config in self.channels_config:
                    if str(channel_id) in config['channel_id'] or config['channel_id'] in str(channel_id):
                        channel_config = config
                        break
            
            if not channel_config:
                return
            
            # 检查消息内容
            text = message.text or ""
            if not text:
                return
            
            # 检查是否是交易信号
            if self.is_trading_signal(text):
                signal_data = {
                    'source': f"telegram_{channel_config['name']}",
                    'text': text,
                    'timestamp': message.date or datetime.now(),
                    'channel_name': channel_config['name'],
                    'message_id': message.id,
                    'confidence': self.calculate_confidence(text, channel_config)
                }
                
                await self.signal_queue.put(signal_data)
                self.logger.info(f"📨 收到Telegram信号: {channel_config['name']}")
                self.logger.debug(f"信号内容: {text[:100]}...")
            
        except Exception as e:
            self.logger.error(f"处理Telegram消息错误: {e}")
    
    def is_trading_signal(self, text: str) -> bool:
        """判断是否是交易信号"""
        try:
            text_lower = text.lower()
            
            # 检查是否包含交易方向
            has_action = False
            for action_words in [self.signal_keywords['buy'], self.signal_keywords['sell']]:
                if any(word in text_lower for word in action_words):
                    has_action = True
                    break
            
            # 检查是否包含交易品种
            has_symbol = any(symbol in text_lower for symbol in self.signal_keywords['symbols'])
            
            # 检查是否包含价格信息
            has_price = any(char in text for char in ['1.', '0.', '@', '价格', 'price'])
            
            # 排除明显的非信号内容
            spam_keywords = ['广告', '推广', '加群', '联系', '微信', 'qq', 'vip', '付费']
            is_spam = any(keyword in text_lower for keyword in spam_keywords)
            
            return has_action and has_symbol and not is_spam
            
        except Exception as e:
            self.logger.error(f"判断交易信号错误: {e}")
            return False
    
    def calculate_confidence(self, text: str, channel_config: Dict) -> float:
        """计算信号置信度"""
        try:
            base_confidence = 0.5
            
            # 基于频道权重
            channel_weight = channel_config.get('weight', 0.5)
            base_confidence += channel_weight * 0.3
            
            # 基于信号完整性
            text_lower = text.lower()
            
            # 有止损信息
            if any(word in text_lower for word in ['sl', 'stop loss', '止损']):
                base_confidence += 0.1
            
            # 有止盈信息
            if any(word in text_lower for word in ['tp', 'take profit', '止盈', '目标']):
                base_confidence += 0.1
            
            # 有入场价格
            if any(word in text_lower for word in ['entry', '入场', '@', 'price']):
                base_confidence += 0.1
            
            # 有风险回报比
            if any(word in text_lower for word in ['rr', 'risk reward', '风险回报']):
                base_confidence += 0.1
            
            # 信号格式规范
            if ':' in text and ('buy' in text_lower or 'sell' in text_lower):
                base_confidence += 0.1
            
            return min(1.0, max(0.0, base_confidence))
            
        except Exception as e:
            self.logger.error(f"计算置信度错误: {e}")
            return 0.5
    
    async def get_signals(self) -> List[Dict]:
        """获取新信号"""
        signals = []
        
        try:
            # 从队列中获取所有待处理信号
            while not self.signal_queue.empty():
                signal = await self.signal_queue.get()
                signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"获取Telegram信号错误: {e}")
        
        return signals
    
    async def get_channel_history(self, channel_id: str, limit: int = 50) -> List[Dict]:
        """获取频道历史消息"""
        try:
            entity = await self.client.get_entity(channel_id)
            messages = []
            
            async for message in self.client.iter_messages(entity, limit=limit):
                if message.text and self.is_trading_signal(message.text):
                    signal_data = {
                        'source': f"telegram_history",
                        'text': message.text,
                        'timestamp': message.date,
                        'message_id': message.id
                    }
                    messages.append(signal_data)
            
            return messages
            
        except Exception as e:
            self.logger.error(f"获取频道历史错误: {e}")
            return []
    
    async def join_channel(self, channel_id: str) -> bool:
        """加入频道"""
        try:
            await self.client.join_channel(channel_id)
            self.logger.info(f"✅ 成功加入频道: {channel_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"加入频道失败 {channel_id}: {e}")
            return False
    
    async def search_channels(self, keywords: List[str]) -> List[Dict]:
        """搜索相关频道"""
        try:
            channels = []
            
            for keyword in keywords:
                try:
                    # 搜索公开频道
                    results = await self.client.search_global(keyword)
                    
                    for result in results[:5]:  # 只取前5个结果
                        if hasattr(result, 'username') and result.username:
                            channel_info = {
                                'name': result.title or result.username,
                                'username': result.username,
                                'id': result.id,
                                'members_count': getattr(result, 'participants_count', 0)
                            }
                            channels.append(channel_info)
                            
                except Exception as e:
                    self.logger.error(f"搜索关键词 {keyword} 失败: {e}")
            
            return channels
            
        except Exception as e:
            self.logger.error(f"搜索频道错误: {e}")
            return []
    
    def is_active(self) -> bool:
        """检查是否活跃"""
        return self.is_running and self.client and self.client.is_connected()
    
    async def get_status(self) -> Dict:
        """获取状态信息"""
        try:
            if not self.client:
                return {'status': 'disconnected', 'channels': 0}
            
            # 获取监控的频道数量
            active_channels = len([c for c in self.channels_config if c.get('enabled', True)])
            
            return {
                'status': 'connected' if self.is_active() else 'disconnected',
                'channels': active_channels,
                'signals_today': 0,  # 可以添加统计逻辑
                'last_signal': None
            }
            
        except Exception as e:
            self.logger.error(f"获取状态错误: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def shutdown(self):
        """关闭Telegram客户端"""
        try:
            self.is_running = False
            
            if self.client:
                await self.client.disconnect()
            
            self.logger.info("✅ Telegram用户机器人已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭Telegram用户机器人错误: {e}")


class TelegramChannelRecommender:
    """Telegram频道推荐器"""
    
    @staticmethod
    def get_free_signal_channels() -> List[Dict]:
        """获取免费信号频道推荐"""
        return [
            {
                "name": "免费外汇信号",
                "channel_id": "@forexsignalsfree",
                "description": "免费的外汇交易信号",
                "language": "en",
                "estimated_signals_per_day": 5
            },
            {
                "name": "黄金信号",
                "channel_id": "@goldsignals",
                "description": "黄金交易信号",
                "language": "en",
                "estimated_signals_per_day": 3
            },
            {
                "name": "外汇VIP群组",
                "channel_id": "@forexvipgroup",
                "description": "VIP外汇信号群组",
                "language": "en",
                "estimated_signals_per_day": 8
            },
            {
                "name": "加密货币信号",
                "channel_id": "@cryptosignalsfree",
                "description": "免费的加密货币信号",
                "language": "en",
                "estimated_signals_per_day": 10
            },
            {
                "name": "外汇分析师",
                "channel_id": "@forexanalyst",
                "description": "专业外汇分析",
                "language": "en",
                "estimated_signals_per_day": 4
            }
        ]
    
    @staticmethod
    def get_chinese_signal_channels() -> List[Dict]:
        """获取中文信号频道推荐"""
        return [
            {
                "name": "外汇交易信号",
                "channel_id": "@forexsignalscn",
                "description": "中文外汇信号",
                "language": "zh",
                "estimated_signals_per_day": 6
            },
            {
                "name": "黄金白银信号",
                "channel_id": "@goldsilversignals",
                "description": "贵金属交易信号",
                "language": "zh",
                "estimated_signals_per_day": 4
            }
        ]
