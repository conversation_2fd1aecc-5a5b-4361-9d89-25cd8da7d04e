#!/usr/bin/env python3
"""
MT4智能跟单系统 - 快速启动脚本
"""

import os
import sys
import json
import asyncio
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")


def create_directories():
    """创建必要的目录"""
    directories = [
        "config",
        "logs",
        "data",
        "src/core",
        "src/sources",
        "src/utils",
        "src/database",
        "templates",
        "static"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")


def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        
        print("✅ 依赖包安装完成")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        print("请手动运行: pip install -r requirements.txt")


def create_config_file():
    """创建配置文件"""
    config_file = Path("config/config.json")
    
    if config_file.exists():
        print("✅ 配置文件已存在")
        return
    
    # 从示例配置复制
    example_config = Path("config/config.example.json")
    if example_config.exists():
        import shutil
        shutil.copy(example_config, config_file)
        print("✅ 配置文件创建完成")
        print("⚠️  请编辑 config/config.json 填入你的API密钥")
    else:
        print("❌ 示例配置文件不存在")


def check_mt5_installation():
    """检查MT5是否安装"""
    try:
        import MetaTrader5 as mt5
        if mt5.initialize():
            print("✅ MT5连接正常")
            mt5.shutdown()
        else:
            print("⚠️  MT5未运行或连接失败")
    except ImportError:
        print("❌ MetaTrader5包未安装")
        print("请运行: pip install MetaTrader5")


def setup_environment():
    """设置环境"""
    print("🔧 正在设置环境...")
    
    check_python_version()
    create_directories()
    create_config_file()
    
    # 检查是否需要安装依赖
    try:
        import MetaTrader5
        import telethon
        import flask
        print("✅ 主要依赖包已安装")
    except ImportError:
        install_dependencies()
    
    check_mt5_installation()


def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🚀 MT4智能跟单系统")
    print("="*50)
    print("1. 启动主程序 (跟单系统)")
    print("2. 启动Web监控面板")
    print("3. 同时启动主程序和监控面板")
    print("4. 配置向导")
    print("5. 系统检查")
    print("6. 查看日志")
    print("0. 退出")
    print("="*50)


def start_main_program():
    """启动主程序"""
    print("🚀 启动跟单系统...")
    try:
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 主程序启动失败: {e}")
    except KeyboardInterrupt:
        print("\n⏹️  主程序已停止")


def start_web_dashboard():
    """启动Web监控面板"""
    print("🌐 启动Web监控面板...")
    try:
        subprocess.run([sys.executable, "web_dashboard.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Web面板启动失败: {e}")
    except KeyboardInterrupt:
        print("\n⏹️  Web面板已停止")


def start_both():
    """同时启动主程序和监控面板"""
    print("🚀 同时启动主程序和监控面板...")
    
    import threading
    import time
    
    # 启动主程序
    main_thread = threading.Thread(target=start_main_program)
    main_thread.daemon = True
    main_thread.start()
    
    # 等待一下再启动Web面板
    time.sleep(2)
    
    # 启动Web面板
    web_thread = threading.Thread(target=start_web_dashboard)
    web_thread.daemon = True
    web_thread.start()
    
    try:
        # 等待线程结束
        main_thread.join()
        web_thread.join()
    except KeyboardInterrupt:
        print("\n⏹️  所有服务已停止")


def configuration_wizard():
    """配置向导"""
    print("\n🔧 配置向导")
    print("-" * 30)
    
    config_file = Path("config/config.json")
    
    if not config_file.exists():
        print("❌ 配置文件不存在，请先运行环境设置")
        return
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("请填入以下配置信息（按回车跳过）:")
    
    # MetaAPI配置
    print("\n📊 MetaAPI配置:")
    metaapi_token = input(f"MetaAPI Token [{config['api_keys']['metaapi']['token']}]: ").strip()
    if metaapi_token:
        config['api_keys']['metaapi']['token'] = metaapi_token
    
    account_id = input(f"MT4账户ID [{config['api_keys']['metaapi']['account_id']}]: ").strip()
    if account_id:
        config['api_keys']['metaapi']['account_id'] = account_id
    
    # Telegram配置
    print("\n📱 Telegram配置:")
    bot_token = input(f"Bot Token [{config['api_keys']['telegram']['bot_token']}]: ").strip()
    if bot_token:
        config['api_keys']['telegram']['bot_token'] = bot_token
    
    # 风险设置
    print("\n⚠️  风险设置:")
    max_risk = input(f"单笔最大风险% [{config['risk_management']['account_settings']['max_risk_per_trade']*100}]: ").strip()
    if max_risk:
        try:
            config['risk_management']['account_settings']['max_risk_per_trade'] = float(max_risk) / 100
        except ValueError:
            print("❌ 无效的数值")
    
    # 保存配置
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ 配置已保存")


def system_check():
    """系统检查"""
    print("\n🔍 系统检查")
    print("-" * 30)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查依赖包
    required_packages = [
        'MetaTrader5', 'telethon', 'flask', 'pandas', 
        'numpy', 'requests', 'asyncio'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
    
    # 检查配置文件
    config_file = Path("config/config.json")
    if config_file.exists():
        print("✅ 配置文件存在")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ 配置文件格式正确")
            
            # 检查关键配置
            if config['api_keys']['metaapi']['token'] == 'YOUR_METAAPI_TOKEN_HERE':
                print("⚠️  MetaAPI Token未配置")
            else:
                print("✅ MetaAPI Token已配置")
                
        except json.JSONDecodeError:
            print("❌ 配置文件格式错误")
    else:
        print("❌ 配置文件不存在")
    
    # 检查MT5连接
    check_mt5_installation()


def view_logs():
    """查看日志"""
    log_file = Path("logs/trading.log")
    
    if not log_file.exists():
        print("❌ 日志文件不存在")
        return
    
    print("\n📋 最近的日志记录:")
    print("-" * 50)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # 显示最后20行
            for line in lines[-20:]:
                print(line.strip())
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")


def main():
    """主函数"""
    # 首次运行时设置环境
    if not Path("config").exists():
        print("🔧 首次运行，正在设置环境...")
        setup_environment()
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-6): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                start_main_program()
            elif choice == '2':
                start_web_dashboard()
            elif choice == '3':
                start_both()
            elif choice == '4':
                configuration_wizard()
            elif choice == '5':
                system_check()
            elif choice == '6':
                view_logs()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    main()
